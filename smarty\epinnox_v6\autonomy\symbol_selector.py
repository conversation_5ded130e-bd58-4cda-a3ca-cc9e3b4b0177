#!/usr/bin/env python3
"""
Dynamic Symbol Selection Engine - Phase 2
Intelligent symbol selector that evaluates multiple criteria and automatically
switches between symbols based on opportunity scoring.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

from .market_scanner import MultiSymbolMarketScanner, SymbolMetrics, MarketOpportunity

logger = logging.getLogger(__name__)

@dataclass
class SymbolScore:
    """Comprehensive scoring for symbol selection."""
    symbol: str
    overall_score: float
    volatility_score: float
    liquidity_score: float
    trend_score: float
    momentum_score: float
    volume_score: float
    risk_adjusted_score: float
    opportunity_count: int
    confidence: float
    recommendation: str  # 'strong_buy', 'buy', 'hold', 'avoid'
    reasoning: str
    timestamp: float

@dataclass
class SymbolSelection:
    """Symbol selection decision."""
    selected_symbol: str
    previous_symbol: str
    selection_reason: str
    confidence: float
    expected_duration: int  # seconds
    risk_level: str
    potential_return: float
    stop_conditions: List[str]
    timestamp: float

class DynamicSymbolSelector:
    """
    Intelligent symbol selector that continuously evaluates all available symbols
    and automatically selects the best trading opportunities.
    """

    def __init__(self, config: Dict[str, Any], market_scanner: MultiSymbolMarketScanner):
        self.config = config
        self.market_scanner = market_scanner
        
        # Selection configuration
        self.selection_interval = config.get('symbol_selector', {}).get('selection_interval', 60)  # seconds
        self.min_selection_confidence = config.get('symbol_selector', {}).get('min_confidence', 0.6)
        self.max_symbol_switches_per_hour = config.get('symbol_selector', {}).get('max_switches_per_hour', 6)
        self.min_hold_duration = config.get('symbol_selector', {}).get('min_hold_duration', 300)  # 5 minutes
        
        # Current state
        self.current_symbol = config.get('symbol', 'DOGE/USDT:USDT')
        self.symbol_selected_at = time.time()
        self.symbol_scores: Dict[str, SymbolScore] = {}
        self.selection_history: List[SymbolSelection] = []
        self.last_selection_time = 0
        
        # Performance tracking
        self.selection_stats = {
            'total_selections': 0,
            'successful_selections': 0,
            'symbol_switches': 0,
            'avg_hold_duration': 0,
            'best_performing_symbol': None,
            'worst_performing_symbol': None
        }
        
        # Symbol performance tracking
        self.symbol_performance: Dict[str, Dict[str, float]] = {}
        
        self.running = False
        self.selection_task = None
        
        logger.info(f"🎯 Dynamic Symbol Selector initialized")
        logger.info(f"   📊 Current symbol: {self.current_symbol}")
        logger.info(f"   ⏱️ Selection interval: {self.selection_interval}s")
        logger.info(f"   🎚️ Min confidence: {self.min_selection_confidence}")

    async def start_selection(self):
        """Start the dynamic symbol selection process."""
        if self.running:
            logger.warning("Symbol selector is already running")
            return
            
        self.running = True
        self.selection_task = asyncio.create_task(self._selection_loop())
        logger.info("🚀 Dynamic symbol selector started")

    async def stop_selection(self):
        """Stop the symbol selection process."""
        self.running = False
        if self.selection_task:
            self.selection_task.cancel()
            try:
                await self.selection_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Dynamic symbol selector stopped")

    async def _selection_loop(self):
        """Main symbol selection loop."""
        while self.running:
            try:
                # Perform symbol evaluation and selection
                await self._evaluate_and_select()
                
                # Wait for next evaluation
                await asyncio.sleep(self.selection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in symbol selection loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _evaluate_and_select(self):
        """Evaluate all symbols and potentially select a new one."""
        try:
            current_time = time.time()
            
            # Check if we should consider switching
            if not self._should_consider_switch(current_time):
                return
            
            # Get latest market data
            top_symbols = self.market_scanner.get_top_symbols(20)
            if not top_symbols:
                logger.warning("⚠️ No symbols available from market scanner")
                return
            
            # Score all symbols
            await self._score_symbols(top_symbols)
            
            # Select best symbol
            best_symbol = await self._select_best_symbol()
            
            # Check if we should switch
            if best_symbol and best_symbol != self.current_symbol:
                await self._switch_symbol(best_symbol, current_time)
            
            self.last_selection_time = current_time
            
        except Exception as e:
            logger.error(f"❌ Error in symbol evaluation: {e}")

    def _should_consider_switch(self, current_time: float) -> bool:
        """Determine if we should consider switching symbols."""
        # Check minimum hold duration
        time_since_selection = current_time - self.symbol_selected_at
        if time_since_selection < self.min_hold_duration:
            return False
        
        # Check switch rate limits
        recent_switches = len([
            s for s in self.selection_history[-10:]
            if current_time - s.timestamp < 3600  # Last hour
        ])
        
        if recent_switches >= self.max_symbol_switches_per_hour:
            logger.debug(f"⏸️ Switch rate limit reached: {recent_switches} switches in last hour")
            return False
        
        return True

    async def _score_symbols(self, symbols: List[str]):
        """Score all available symbols for selection."""
        self.symbol_scores.clear()
        
        for symbol in symbols:
            try:
                score = await self._calculate_symbol_score(symbol)
                if score:
                    self.symbol_scores[symbol] = score
            except Exception as e:
                logger.warning(f"❌ Failed to score symbol {symbol}: {e}")

    async def _calculate_symbol_score(self, symbol: str) -> Optional[SymbolScore]:
        """Calculate comprehensive score for a symbol."""
        try:
            # Get symbol metrics
            metrics = self.market_scanner.get_symbol_metrics(symbol)
            if not metrics:
                return None
            
            # Get opportunities for this symbol
            opportunities = [
                opp for opp in self.market_scanner.get_market_opportunities()
                if opp.symbol == symbol
            ]
            
            # Calculate component scores
            volatility_score = self._score_volatility(metrics.volatility)
            liquidity_score = self._score_liquidity(metrics.liquidity_score)
            trend_score = self._score_trend(metrics.trend_strength, metrics.price_change_pct)
            momentum_score = self._score_momentum(metrics.momentum_score)
            volume_score = self._score_volume(metrics.volume_trend)
            
            # Calculate risk-adjusted score
            risk_penalty = metrics.risk_score * 0.3  # 30% penalty for high risk
            risk_adjusted_score = (volatility_score + liquidity_score + trend_score + 
                                 momentum_score + volume_score) / 5 * (1 - risk_penalty)
            
            # Opportunity bonus
            opportunity_bonus = min(len(opportunities) * 0.1, 0.3)  # Up to 30% bonus
            
            # Overall score
            overall_score = risk_adjusted_score + opportunity_bonus
            
            # Determine recommendation
            recommendation, reasoning = self._determine_recommendation(
                overall_score, metrics, opportunities
            )
            
            # Calculate confidence
            confidence = self._calculate_confidence(metrics, opportunities, overall_score)
            
            return SymbolScore(
                symbol=symbol,
                overall_score=overall_score,
                volatility_score=volatility_score,
                liquidity_score=liquidity_score,
                trend_score=trend_score,
                momentum_score=momentum_score,
                volume_score=volume_score,
                risk_adjusted_score=risk_adjusted_score,
                opportunity_count=len(opportunities),
                confidence=confidence,
                recommendation=recommendation,
                reasoning=reasoning,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"❌ Error calculating score for {symbol}: {e}")
            return None

    def _score_volatility(self, volatility: float) -> float:
        """Score volatility (optimal range for trading)."""
        # Optimal volatility range: 2-5%
        if 0.02 <= volatility <= 0.05:
            return 1.0
        elif 0.01 <= volatility < 0.02:
            return 0.7  # Too low
        elif 0.05 < volatility <= 0.08:
            return 0.8  # Acceptable high
        elif volatility > 0.08:
            return 0.5  # Too high
        else:
            return 0.3  # Too low

    def _score_liquidity(self, liquidity_score: float) -> float:
        """Score liquidity."""
        return min(liquidity_score / 5.0, 1.0)

    def _score_trend(self, trend_strength: float, price_change_pct: float) -> float:
        """Score trend quality."""
        # Strong trends with significant price movement are preferred
        trend_score = trend_strength
        momentum_bonus = min(abs(price_change_pct) * 10, 0.3)
        return min(trend_score + momentum_bonus, 1.0)

    def _score_momentum(self, momentum_score: float) -> float:
        """Score momentum."""
        return momentum_score

    def _score_volume(self, volume_trend: float) -> float:
        """Score volume trend."""
        # Optimal volume trend: 1.2-2.0x
        if 1.2 <= volume_trend <= 2.0:
            return 1.0
        elif 1.0 <= volume_trend < 1.2:
            return 0.7
        elif volume_trend > 2.0:
            return 0.8  # Very high volume
        else:
            return 0.4  # Low volume

    def _determine_recommendation(self, overall_score: float, metrics: SymbolMetrics,
                                opportunities: List[MarketOpportunity]) -> Tuple[str, str]:
        """Determine trading recommendation and reasoning."""
        if overall_score >= 0.8 and len(opportunities) >= 2:
            return 'strong_buy', f"Excellent score ({overall_score:.2f}) with {len(opportunities)} opportunities"
        elif overall_score >= 0.6 and len(opportunities) >= 1:
            return 'buy', f"Good score ({overall_score:.2f}) with trading opportunities"
        elif overall_score >= 0.4:
            return 'hold', f"Moderate score ({overall_score:.2f}), monitor for improvements"
        else:
            return 'avoid', f"Low score ({overall_score:.2f}), poor trading conditions"

    def _calculate_confidence(self, metrics: SymbolMetrics, opportunities: List[MarketOpportunity],
                            overall_score: float) -> float:
        """Calculate confidence in the symbol selection."""
        # Base confidence from score
        base_confidence = overall_score
        
        # Liquidity confidence
        liquidity_confidence = min(metrics.liquidity_score / 3.0, 1.0)
        
        # Opportunity confidence
        opportunity_confidence = min(len(opportunities) / 3.0, 1.0)
        
        # Volume confirmation
        volume_confidence = 1.0 if metrics.volume_trend > 1.1 else 0.7
        
        # Weighted average
        confidence = (
            0.4 * base_confidence +
            0.25 * liquidity_confidence +
            0.2 * opportunity_confidence +
            0.15 * volume_confidence
        )
        
        return max(0, min(1, confidence))

    async def _select_best_symbol(self) -> Optional[str]:
        """Select the best symbol based on scores."""
        if not self.symbol_scores:
            return None
        
        # Filter by minimum confidence
        qualified_symbols = {
            symbol: score for symbol, score in self.symbol_scores.items()
            if score.confidence >= self.min_selection_confidence and score.recommendation in ['strong_buy', 'buy']
        }
        
        if not qualified_symbols:
            logger.debug("⚠️ No symbols meet minimum selection criteria")
            return None
        
        # Select highest scoring symbol
        best_symbol = max(qualified_symbols.keys(), key=lambda s: qualified_symbols[s].overall_score)
        best_score = qualified_symbols[best_symbol]
        
        logger.debug(f"🎯 Best symbol: {best_symbol} (score: {best_score.overall_score:.3f}, "
                    f"confidence: {best_score.confidence:.2%})")
        
        return best_symbol

    async def _switch_symbol(self, new_symbol: str, current_time: float):
        """Switch to a new symbol."""
        try:
            previous_symbol = self.current_symbol
            
            # Create selection record
            selection = SymbolSelection(
                selected_symbol=new_symbol,
                previous_symbol=previous_symbol,
                selection_reason=self.symbol_scores[new_symbol].reasoning,
                confidence=self.symbol_scores[new_symbol].confidence,
                expected_duration=self.min_hold_duration,
                risk_level=self._assess_risk_level(new_symbol),
                potential_return=self._estimate_potential_return(new_symbol),
                stop_conditions=self._define_stop_conditions(new_symbol),
                timestamp=current_time
            )
            
            # Update state
            self.current_symbol = new_symbol
            self.symbol_selected_at = current_time
            self.selection_history.append(selection)
            
            # Update statistics
            self.selection_stats['total_selections'] += 1
            if previous_symbol != new_symbol:
                self.selection_stats['symbol_switches'] += 1
            
            logger.info(f"🔄 Symbol switched: {previous_symbol} → {new_symbol}")
            logger.info(f"   📊 Score: {self.symbol_scores[new_symbol].overall_score:.3f}")
            logger.info(f"   🎯 Confidence: {self.symbol_scores[new_symbol].confidence:.2%}")
            logger.info(f"   💡 Reason: {selection.selection_reason}")
            
        except Exception as e:
            logger.error(f"❌ Error switching symbol: {e}")

    def _assess_risk_level(self, symbol: str) -> str:
        """Assess risk level for a symbol."""
        metrics = self.market_scanner.get_symbol_metrics(symbol)
        if not metrics:
            return 'unknown'
        
        if metrics.risk_score < 0.3:
            return 'low'
        elif metrics.risk_score < 0.6:
            return 'medium'
        else:
            return 'high'

    def _estimate_potential_return(self, symbol: str) -> float:
        """Estimate potential return for a symbol."""
        opportunities = [
            opp for opp in self.market_scanner.get_market_opportunities()
            if opp.symbol == symbol
        ]
        
        if opportunities:
            return max(opp.potential_return for opp in opportunities)
        
        metrics = self.market_scanner.get_symbol_metrics(symbol)
        return metrics.volatility * 1.5 if metrics else 0.02

    def _define_stop_conditions(self, symbol: str) -> List[str]:
        """Define conditions for stopping trading this symbol."""
        return [
            "Low liquidity (< 1.0 score)",
            "High risk (> 0.8 score)",
            "No opportunities for 10 minutes",
            "Significant adverse price movement (> 3%)",
            "Volume drops below 50% of average"
        ]

    def get_current_symbol(self) -> str:
        """Get currently selected symbol."""
        return self.current_symbol

    def get_symbol_scores(self) -> Dict[str, SymbolScore]:
        """Get current symbol scores."""
        return self.symbol_scores.copy()

    def get_selection_history(self, limit: int = 10) -> List[SymbolSelection]:
        """Get recent selection history."""
        return self.selection_history[-limit:]

    def get_selection_stats(self) -> Dict[str, Any]:
        """Get selection performance statistics."""
        return self.selection_stats.copy()

    def force_symbol_selection(self, symbol: str) -> bool:
        """Force selection of a specific symbol (for manual override)."""
        try:
            if symbol in self.symbol_scores:
                self.current_symbol = symbol
                self.symbol_selected_at = time.time()
                logger.info(f"🔧 Manual symbol selection: {symbol}")
                return True
            else:
                logger.warning(f"⚠️ Symbol {symbol} not available for selection")
                return False
        except Exception as e:
            logger.error(f"❌ Error in manual symbol selection: {e}")
            return False
