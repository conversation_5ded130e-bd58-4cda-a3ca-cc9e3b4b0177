#!/usr/bin/env python3
"""
Simple Money Circle Server Starter
Direct server startup for testing live data flow and symbol selection
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def start_money_circle_server():
    """Start the Money Circle server."""
    try:
        logger.info("🚀 Starting Money Circle server...")
        
        # Import the app
        from app import MoneyCircleApp
        
        # Create app instance
        logger.info("📱 Creating Money Circle app instance...")
        app = MoneyCircleApp('development')
        logger.info("✅ Money Circle app created successfully")
        
        # Create web application
        logger.info("🌐 Creating web application...")
        web_app = await app.create_app()
        logger.info("✅ Web application created successfully")
        
        # Start the server
        logger.info("🚀 Starting web server...")
        from aiohttp import web
        
        runner = web.AppRunner(web_app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', 8087)
        await site.start()
        
        logger.info("🎉 Money Circle server started successfully!")
        logger.info("📍 Server running at: http://localhost:8087")
        logger.info("🔐 Login: epinnox / securepass123")
        logger.info("💰 Live Trading: http://localhost:8087/live-trading")
        logger.info("🛑 Press Ctrl+C to stop")
        
        # Start background tasks
        logger.info("🔄 Starting background tasks...")
        
        # Start market data manager if available
        if hasattr(app, 'market_data_manager') and app.market_data_manager:
            asyncio.create_task(app.market_data_manager.start())
            logger.info("📊 Market data manager started")
        
        # Start HTX monitoring if available
        if hasattr(app, 'htx_futures_client') and app.htx_futures_client:
            try:
                asyncio.create_task(app.htx_futures_client.start_monitoring())
                logger.info("📈 HTX futures monitoring started")
            except Exception as e:
                logger.warning(f"HTX monitoring warning: {e}")
        
        logger.info("✅ All systems ready!")
        
        # Keep the server running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown signal received")
            
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()
        raise

def main():
    """Main entry point."""
    print("💰 Money Circle Investment Club Platform")
    print("🔧 Simple Server Starter")
    print("=" * 50)
    
    try:
        asyncio.run(start_money_circle_server())
    except KeyboardInterrupt:
        print("\n🛑 Money Circle server stopped")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
