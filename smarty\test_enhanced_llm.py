#!/usr/bin/env python3
"""
Test Enhanced LLM Integration System
Validates dynamic prompt templates, market memory, and performance feedback
"""

import asyncio
import json
import logging
import requests
import websockets
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedLLMTester:
    """Test suite for enhanced LLM integration features."""

    def __init__(self):
        self.base_url = "http://localhost:8086"
        self.ws_url = "ws://localhost:8086/ws"
        self.test_results = {}

    async def run_comprehensive_tests(self):
        """Run comprehensive test suite for enhanced LLM features."""
        logger.info("🧪 Starting Enhanced LLM Integration Tests...")
        logger.info("=" * 60)

        # Test 1: Validate prompt template loading
        await self.test_prompt_template_loading()

        # Test 2: Check LLM decision format
        await self.test_llm_decision_format()

        # Test 3: Validate confidence score ranges
        await self.test_confidence_validation()

        # Test 4: Test performance context injection
        await self.test_performance_context()

        # Test 5: Test model disagreement detection
        await self.test_model_disagreement_detection()

        # Test 6: Test market regime analysis
        await self.test_market_regime_analysis()

        # Test 7: Test adaptive reasoning
        await self.test_adaptive_reasoning()

        # Test 8: Test self-awareness and meta-reasoning
        await self.test_self_awareness()

        # Test 9: Test conviction scoring
        await self.test_conviction_scoring()

        # Test 10: Test performance memory integration
        await self.test_performance_memory_integration()

        # Test 11: Monitor real-time enhanced decisions
        await self.monitor_real_time_decisions()

        # Generate test report
        self.generate_test_report()

    async def test_prompt_template_loading(self):
        """Test that prompt templates are properly loaded."""
        logger.info("=== TEST 1: Prompt Template Loading ===")

        try:
            # Check if the application started with template loading
            response = requests.get(f"{self.base_url}/api/status", timeout=5)

            if response.status_code == 200:
                logger.info("✅ Application is running with LLM integration")
                self.test_results['prompt_templates'] = 'PASS'
            else:
                logger.error("❌ Application not responding properly")
                self.test_results['prompt_templates'] = 'FAIL'

        except Exception as e:
            logger.error(f"❌ Error testing prompt templates: {e}")
            self.test_results['prompt_templates'] = 'ERROR'

    async def test_llm_decision_format(self):
        """Test LLM decision format and field validation."""
        logger.info("=== TEST 2: LLM Decision Format ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])

            if llm_decisions:
                latest_decision = llm_decisions[-1]

                # Check required fields
                required_fields = ['symbol', 'action', 'confidence', 'reasoning', 'risk_level']
                missing_fields = [field for field in required_fields if field not in latest_decision]

                if not missing_fields:
                    logger.info("✅ LLM decision has all required fields")
                    logger.info(f"   Action: {latest_decision.get('action')}")
                    logger.info(f"   Confidence: {latest_decision.get('confidence')}")
                    logger.info(f"   Risk Level: {latest_decision.get('risk_level')}")
                    self.test_results['decision_format'] = 'PASS'
                else:
                    logger.error(f"❌ Missing fields: {missing_fields}")
                    self.test_results['decision_format'] = 'FAIL'
            else:
                logger.warning("⚠️ No LLM decisions available yet")
                self.test_results['decision_format'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing decision format: {e}")
            self.test_results['decision_format'] = 'ERROR'

    async def test_confidence_validation(self):
        """Test confidence score validation and clamping."""
        logger.info("=== TEST 3: Confidence Validation ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])

            if llm_decisions:
                confidence_scores = []
                for decision in llm_decisions[-5:]:  # Check last 5 decisions
                    confidence = decision.get('confidence', 0)
                    if isinstance(confidence, (int, float)):
                        confidence_scores.append(confidence)

                if confidence_scores:
                    max_confidence = max(confidence_scores)
                    min_confidence = min(confidence_scores)

                    # Check if all scores are in valid range (0-1 for API, but display might be 0-100)
                    valid_range = all(0 <= score <= 1 for score in confidence_scores) or \
                                 all(0 <= score <= 100 for score in confidence_scores)

                    if valid_range and max_confidence <= 100:
                        logger.info("✅ Confidence scores are in valid range")
                        logger.info(f"   Range: {min_confidence:.1f}% - {max_confidence:.1f}%")
                        self.test_results['confidence_validation'] = 'PASS'
                    else:
                        logger.error(f"❌ Invalid confidence range: {min_confidence} - {max_confidence}")
                        self.test_results['confidence_validation'] = 'FAIL'
                else:
                    logger.warning("⚠️ No confidence scores to validate")
                    self.test_results['confidence_validation'] = 'PENDING'
            else:
                logger.warning("⚠️ No LLM decisions for confidence validation")
                self.test_results['confidence_validation'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing confidence validation: {e}")
            self.test_results['confidence_validation'] = 'ERROR'

    async def test_performance_context(self):
        """Test performance context injection in LLM decisions."""
        logger.info("=== TEST 4: Performance Context ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            # Check if performance data is available
            performance = data.get('performance', {})

            if performance:
                total_signals = performance.get('total_signals', 0)
                win_rate = performance.get('win_rate', 0)

                logger.info("✅ Performance context is available")
                logger.info(f"   Total Signals: {total_signals}")
                logger.info(f"   Win Rate: {win_rate:.1f}%")

                # Check if LLM decisions reference performance
                llm_decisions = data.get('llm_decisions', [])
                if llm_decisions:
                    latest_reasoning = llm_decisions[-1].get('reasoning', '')

                    # Look for performance-related keywords in reasoning
                    performance_keywords = ['performance', 'recent', 'signals', 'win', 'loss']
                    has_performance_context = any(keyword in latest_reasoning.lower()
                                                for keyword in performance_keywords)

                    if has_performance_context:
                        logger.info("✅ LLM reasoning includes performance context")
                        self.test_results['performance_context'] = 'PASS'
                    else:
                        logger.info("ℹ️ LLM reasoning may not include explicit performance context")
                        self.test_results['performance_context'] = 'PARTIAL'
                else:
                    self.test_results['performance_context'] = 'PENDING'
            else:
                logger.warning("⚠️ No performance data available")
                self.test_results['performance_context'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing performance context: {e}")
            self.test_results['performance_context'] = 'ERROR'

    async def test_model_disagreement_detection(self):
        """Enhanced test for explicit model disagreement communication."""
        logger.info("=== TEST 5: Enhanced Model Disagreement Detection ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            model_outputs = data.get('model_outputs', {})

            if model_outputs and len(model_outputs) >= 2:
                # Analyze model actions
                actions = [output.get('action', 'WAIT') for output in model_outputs.values()]
                unique_actions = set(actions)

                if len(unique_actions) > 1:
                    logger.info("✅ Model disagreement detected")
                    logger.info(f"   Actions: {actions}")

                    # Check enhanced LLM decision fields
                    llm_decisions = data.get('llm_decisions', [])
                    if llm_decisions:
                        latest_decision = llm_decisions[-1]
                        reasoning = latest_decision.get('reasoning', '')

                        # Check for explicit model name mentions
                        model_names = list(model_outputs.keys())
                        explicit_mentions = sum(1 for name in model_names
                                              if name.lower() in reasoning.lower())

                        # Check for confidence level mentions
                        confidence_mentions = reasoning.count('%') or reasoning.count('confidence')

                        # Check enhanced fields
                        disagreement_detected = latest_decision.get('disagreement_detected', False)
                        conflicting_models = latest_decision.get('conflicting_models', [])

                        logger.info(f"   Explicit model mentions: {explicit_mentions}/{len(model_names)}")
                        logger.info(f"   Confidence mentions: {confidence_mentions}")
                        logger.info(f"   Disagreement detected field: {disagreement_detected}")
                        logger.info(f"   Conflicting models: {conflicting_models}")

                        # Enhanced scoring
                        if (explicit_mentions >= 2 and confidence_mentions >= 1 and
                            disagreement_detected and conflicting_models):
                            logger.info("✅ Excellent explicit conflict communication")
                            self.test_results['disagreement_detection'] = 'PASS'
                        elif explicit_mentions >= 1 or disagreement_detected:
                            logger.info("🟡 Partial explicit conflict communication")
                            self.test_results['disagreement_detection'] = 'PARTIAL'
                        else:
                            logger.info("❌ Limited explicit conflict communication")
                            self.test_results['disagreement_detection'] = 'FAIL'
                    else:
                        self.test_results['disagreement_detection'] = 'PENDING'
                else:
                    logger.info("ℹ️ Models are in agreement")
                    self.test_results['disagreement_detection'] = 'N/A'
            else:
                logger.warning("⚠️ Insufficient model outputs for disagreement test")
                self.test_results['disagreement_detection'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing disagreement detection: {e}")
            self.test_results['disagreement_detection'] = 'ERROR'

    async def test_market_regime_analysis(self):
        """Test market regime analysis integration."""
        logger.info("=== TEST 6: Market Regime Analysis ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            features = data.get('features', {})

            if features:
                volatility = features.get('volatility', 0)
                price_change = features.get('price_change_1m', 0)

                logger.info("✅ Market features available for regime analysis")
                logger.info(f"   Volatility: {volatility:.6f}")
                logger.info(f"   Price Change: {price_change:.4f}%")

                # Determine expected regime
                if volatility > 0.02:
                    expected_regime = "high_volatility"
                elif abs(price_change) > 0.01:
                    expected_regime = "trending"
                else:
                    expected_regime = "ranging"

                logger.info(f"   Expected Regime: {expected_regime}")
                self.test_results['market_regime'] = 'PASS'
            else:
                logger.warning("⚠️ No market features available")
                self.test_results['market_regime'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing market regime analysis: {e}")
            self.test_results['market_regime'] = 'ERROR'

    async def test_adaptive_reasoning(self):
        """Test adaptive reasoning based on recent performance."""
        logger.info("=== TEST 7: Adaptive Reasoning ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])

            if len(llm_decisions) >= 3:
                # Analyze reasoning evolution
                recent_reasoning = [decision.get('reasoning', '') for decision in llm_decisions[-3:]]

                # Check for adaptive elements
                adaptive_keywords = ['recent', 'previous', 'performance', 'trend', 'pattern']
                adaptive_count = sum(1 for reasoning in recent_reasoning
                                   for keyword in adaptive_keywords
                                   if keyword in reasoning.lower())

                if adaptive_count > 0:
                    logger.info("✅ LLM reasoning shows adaptive elements")
                    logger.info(f"   Adaptive references: {adaptive_count}")
                    self.test_results['adaptive_reasoning'] = 'PASS'
                else:
                    logger.info("ℹ️ Limited adaptive reasoning detected")
                    self.test_results['adaptive_reasoning'] = 'PARTIAL'
            else:
                logger.warning("⚠️ Insufficient decisions for adaptive reasoning test")
                self.test_results['adaptive_reasoning'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing adaptive reasoning: {e}")
            self.test_results['adaptive_reasoning'] = 'ERROR'

    async def test_self_awareness(self):
        """Test LLM self-awareness and meta-reasoning capabilities."""
        logger.info("=== TEST 8: Self-Awareness & Meta-Reasoning ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])

            if llm_decisions:
                latest_decision = llm_decisions[-1]
                reasoning = latest_decision.get('reasoning', '')

                # Check for self-awareness indicators
                self_awareness_keywords = [
                    'conviction', 'confidence', 'uncertainty', 'limited', 'strong',
                    'weak', 'quality', 'assessment', 'meta', 'decision strength'
                ]

                self_awareness_count = sum(1 for keyword in self_awareness_keywords
                                         if keyword in reasoning.lower())

                # Check enhanced fields
                conviction_score = latest_decision.get('conviction_score')
                reasoning_quality = latest_decision.get('reasoning_quality')

                logger.info(f"   Self-awareness keywords: {self_awareness_count}")
                logger.info(f"   Conviction score: {conviction_score}")
                logger.info(f"   Reasoning quality: {reasoning_quality}")

                if (self_awareness_count >= 2 and conviction_score is not None and
                    reasoning_quality in ['LOW', 'MEDIUM', 'HIGH']):
                    logger.info("✅ Strong self-awareness demonstrated")
                    self.test_results['self_awareness'] = 'PASS'
                elif self_awareness_count >= 1 or conviction_score is not None:
                    logger.info("🟡 Partial self-awareness demonstrated")
                    self.test_results['self_awareness'] = 'PARTIAL'
                else:
                    logger.info("❌ Limited self-awareness demonstrated")
                    self.test_results['self_awareness'] = 'FAIL'
            else:
                logger.warning("⚠️ No LLM decisions for self-awareness test")
                self.test_results['self_awareness'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing self-awareness: {e}")
            self.test_results['self_awareness'] = 'ERROR'

    async def test_conviction_scoring(self):
        """Test conviction scoring accuracy and consistency."""
        logger.info("=== TEST 9: Conviction Scoring ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])

            if len(llm_decisions) >= 3:
                conviction_scores = []
                for decision in llm_decisions[-5:]:  # Last 5 decisions
                    score = decision.get('conviction_score')
                    if score is not None:
                        conviction_scores.append(score)

                if conviction_scores:
                    valid_scores = all(1 <= score <= 5 for score in conviction_scores)
                    score_range = max(conviction_scores) - min(conviction_scores)
                    avg_score = sum(conviction_scores) / len(conviction_scores)

                    logger.info(f"   Conviction scores: {conviction_scores}")
                    logger.info(f"   Valid range (1-5): {valid_scores}")
                    logger.info(f"   Score variability: {score_range}")
                    logger.info(f"   Average score: {avg_score:.1f}")

                    if valid_scores and score_range >= 1:  # Shows variability
                        logger.info("✅ Conviction scoring working properly")
                        self.test_results['conviction_scoring'] = 'PASS'
                    elif valid_scores:
                        logger.info("🟡 Conviction scoring valid but limited variability")
                        self.test_results['conviction_scoring'] = 'PARTIAL'
                    else:
                        logger.info("❌ Invalid conviction scores detected")
                        self.test_results['conviction_scoring'] = 'FAIL'
                else:
                    logger.warning("⚠️ No conviction scores found")
                    self.test_results['conviction_scoring'] = 'PENDING'
            else:
                logger.warning("⚠️ Insufficient decisions for conviction scoring test")
                self.test_results['conviction_scoring'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing conviction scoring: {e}")
            self.test_results['conviction_scoring'] = 'ERROR'

    async def test_performance_memory_integration(self):
        """Test performance memory integration in decision making."""
        logger.info("=== TEST 10: Performance Memory Integration ===")

        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            data = response.json()

            llm_decisions = data.get('llm_decisions', [])
            performance = data.get('performance', {})

            if llm_decisions and performance:
                latest_decision = llm_decisions[-1]
                reasoning = latest_decision.get('reasoning', '')

                # Check for performance-related references
                performance_keywords = [
                    'recent', 'previous', 'signals', 'performance', 'win', 'loss',
                    'streak', 'pattern', 'history', 'accuracy'
                ]

                performance_mentions = sum(1 for keyword in performance_keywords
                                         if keyword in reasoning.lower())

                # Check enhanced fields
                performance_context_applied = latest_decision.get('performance_context_applied', False)

                # Check if performance metrics influence reasoning
                win_rate = performance.get('win_rate', 0)
                total_signals = performance.get('total_signals', 0)

                logger.info(f"   Performance mentions in reasoning: {performance_mentions}")
                logger.info(f"   Performance context applied: {performance_context_applied}")
                logger.info(f"   Current win rate: {win_rate:.1f}%")
                logger.info(f"   Total signals: {total_signals}")

                if (performance_mentions >= 2 and performance_context_applied and
                    total_signals > 0):
                    logger.info("✅ Strong performance memory integration")
                    self.test_results['performance_memory'] = 'PASS'
                elif performance_mentions >= 1 or total_signals > 0:
                    logger.info("🟡 Partial performance memory integration")
                    self.test_results['performance_memory'] = 'PARTIAL'
                else:
                    logger.info("❌ Limited performance memory integration")
                    self.test_results['performance_memory'] = 'FAIL'
            else:
                logger.warning("⚠️ Insufficient data for performance memory test")
                self.test_results['performance_memory'] = 'PENDING'

        except Exception as e:
            logger.error(f"❌ Error testing performance memory integration: {e}")
            self.test_results['performance_memory'] = 'ERROR'

    async def monitor_real_time_decisions(self):
        """Monitor real-time LLM decisions via WebSocket."""
        logger.info("=== TEST 8: Real-time Decision Monitoring ===")

        try:
            logger.info("Connecting to WebSocket for real-time monitoring...")

            async with websockets.connect(self.ws_url) as websocket:
                logger.info("✅ WebSocket connected")

                # Monitor for 10 seconds
                start_time = time.time()
                decision_count = 0

                while time.time() - start_time < 10:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)

                        llm_decisions = data.get('llm_decisions', [])
                        if llm_decisions:
                            latest_decision = llm_decisions[-1]
                            decision_count += 1

                            logger.info(f"📊 LLM Decision #{decision_count}:")
                            logger.info(f"   Action: {latest_decision.get('action')}")
                            logger.info(f"   Confidence: {latest_decision.get('confidence')}")
                            logger.info(f"   Reasoning: {latest_decision.get('reasoning', '')[:100]}...")

                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        logger.warning(f"WebSocket message error: {e}")
                        break

                if decision_count > 0:
                    logger.info(f"✅ Monitored {decision_count} real-time decisions")
                    self.test_results['real_time_monitoring'] = 'PASS'
                else:
                    logger.info("ℹ️ No new decisions during monitoring period")
                    self.test_results['real_time_monitoring'] = 'PARTIAL'

        except Exception as e:
            logger.error(f"❌ Error in real-time monitoring: {e}")
            self.test_results['real_time_monitoring'] = 'ERROR'

    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("=" * 60)
        logger.info("🏁 Enhanced LLM Integration Test Report")
        logger.info("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Partial: {partial_tests}")
        logger.info(f"Success Rate: {(passed_tests + partial_tests * 0.5) / total_tests * 100:.1f}%")
        logger.info("")

        for test_name, result in self.test_results.items():
            status_emoji = {
                'PASS': '✅',
                'PARTIAL': '🟡',
                'FAIL': '❌',
                'ERROR': '🔴',
                'PENDING': '⏳',
                'N/A': '➖'
            }.get(result, '❓')

            logger.info(f"{status_emoji} {test_name.replace('_', ' ').title()}: {result}")

        logger.info("=" * 60)

async def main():
    """Main test execution."""
    tester = EnhancedLLMTester()
    await tester.run_comprehensive_tests()

if __name__ == "__main__":
    asyncio.run(main())
