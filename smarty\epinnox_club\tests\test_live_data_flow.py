#!/usr/bin/env python3
"""
Test Live Data Flow for Money Circle
Diagnose and fix live data streaming and symbol selection issues
"""

import asyncio
import logging
import json
import sys
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveDataFlowTester:
    """Test and fix live data flow issues."""
    
    def __init__(self):
        """Initialize the tester."""
        self.market_data_manager = None
        self.htx_client = None
        self.websocket_streamer = None
        logger.info("[TEST] Live Data Flow Tester initialized")
    
    async def test_market_data_manager(self):
        """Test the market data manager."""
        try:
            from market_data.advanced_market_data_manager import AdvancedMarketDataManager
            
            self.market_data_manager = AdvancedMarketDataManager()
            logger.info("[OK] Market data manager created")
            
            # Test symbol availability
            symbols = self.market_data_manager.symbols
            logger.info(f"[OK] Available symbols: {list(symbols)}")
            
            # Test market data retrieval
            for symbol in list(symbols)[:3]:  # Test first 3 symbols
                tick = self.market_data_manager.get_market_tick(symbol)
                if tick:
                    logger.info(f"[OK] Market data for {symbol}: ${tick.price:.4f}")
                else:
                    logger.warning(f"[WARNING] No market data for {symbol}")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Market data manager test failed: {e}")
            return False
    
    async def test_htx_client(self):
        """Test HTX futures client."""
        try:
            from trading.htx_futures_client import HTXFuturesClient
            
            self.htx_client = HTXFuturesClient()
            logger.info("[OK] HTX client created")
            
            # Test connection
            connected = await self.htx_client.connect()
            if connected:
                logger.info("[OK] HTX client connected successfully")
                
                # Test market data retrieval
                try:
                    market_data = await self.htx_client.get_market_data('DOGE/USDT:USDT')
                    if market_data:
                        logger.info(f"[OK] HTX market data: {market_data}")
                    else:
                        logger.warning("[WARNING] No HTX market data received")
                except Exception as e:
                    logger.warning(f"[WARNING] HTX market data error: {e}")
                
                return True
            else:
                logger.error("[ERROR] HTX client connection failed")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] HTX client test failed: {e}")
            return False
    
    async def test_websocket_streamer(self):
        """Test WebSocket streamer."""
        try:
            from market_data.websocket_streamer import MarketDataWebSocketStreamer
            
            if not self.market_data_manager:
                logger.error("[ERROR] Market data manager required for WebSocket streamer")
                return False
            
            self.websocket_streamer = MarketDataWebSocketStreamer(self.market_data_manager)
            logger.info("[OK] WebSocket streamer created")
            
            # Test symbol subscribers
            symbols = list(self.market_data_manager.symbols)
            logger.info(f"[OK] WebSocket can stream symbols: {symbols}")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket streamer test failed: {e}")
            return False
    
    async def test_symbol_selection(self):
        """Test symbol selection functionality."""
        try:
            # Test symbol normalization
            test_symbols = [
                'DOGE/USDT',
                'DOGEUSDT',
                'BTC/USDT',
                'BTCUSDT',
                'ETH/USDT',
                'ETHUSDT'
            ]
            
            logger.info("[TEST] Testing symbol normalization...")
            
            for symbol in test_symbols:
                # Test symbol format conversion
                if '/' not in symbol and symbol.endswith('USDT'):
                    base = symbol[:-4]
                    normalized = f"{base}/USDT"
                    logger.info(f"[OK] {symbol} -> {normalized}")
                else:
                    logger.info(f"[OK] {symbol} (already normalized)")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Symbol selection test failed: {e}")
            return False
    
    async def test_live_trading_interface(self):
        """Test live trading interface integration."""
        try:
            from trading.advanced_trading_interface import AdvancedTradingInterface
            
            # Create trading interface
            trading_interface = AdvancedTradingInterface()
            logger.info("[OK] Advanced trading interface created")
            
            # Test automation features
            automation_features = [
                'auto_close_by_pnl',
                'auto_close_by_time',
                'trailing_stop',
                'scalping_mode'
            ]
            
            for feature in automation_features:
                if hasattr(trading_interface, feature):
                    logger.info(f"[OK] Automation feature available: {feature}")
                else:
                    logger.warning(f"[WARNING] Automation feature missing: {feature}")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Live trading interface test failed: {e}")
            return False
    
    async def create_symbol_selection_fix(self):
        """Create a fix for symbol selection issues."""
        try:
            # Create a symbol configuration file
            symbol_config = {
                "supported_symbols": [
                    "DOGE/USDT:USDT",
                    "BTC/USDT:USDT", 
                    "ETH/USDT:USDT",
                    "BNB/USDT:USDT",
                    "ADA/USDT:USDT",
                    "SOL/USDT:USDT",
                    "XRP/USDT:USDT",
                    "DOT/USDT:USDT",
                    "AVAX/USDT:USDT"
                ],
                "default_symbol": "DOGE/USDT:USDT",
                "symbol_mapping": {
                    "DOGEUSDT": "DOGE/USDT:USDT",
                    "BTCUSDT": "BTC/USDT:USDT",
                    "ETHUSDT": "ETH/USDT:USDT",
                    "BNBUSDT": "BNB/USDT:USDT",
                    "ADAUSDT": "ADA/USDT:USDT",
                    "SOLUSDT": "SOL/USDT:USDT",
                    "XRPUSDT": "XRP/USDT:USDT",
                    "DOTUSDT": "DOT/USDT:USDT",
                    "AVAXUSDT": "AVAX/USDT:USDT"
                }
            }
            
            # Save to config directory
            config_dir = Path('config')
            config_dir.mkdir(exist_ok=True)
            
            with open(config_dir / 'symbols.json', 'w') as f:
                json.dump(symbol_config, f, indent=2)
            
            logger.info("[OK] Symbol configuration file created")
            
            # Create JavaScript fix for frontend
            js_fix = """
// Symbol Selection Fix for Live Trading Interface
function fixSymbolSelection() {
    const symbolSelect = document.getElementById('symbol-select');
    if (!symbolSelect) {
        console.log('Creating symbol selector...');
        createSymbolSelector();
        return;
    }
    
    // Update symbol options
    const symbols = [
        'DOGE/USDT:USDT',
        'BTC/USDT:USDT', 
        'ETH/USDT:USDT',
        'BNB/USDT:USDT',
        'ADA/USDT:USDT',
        'SOL/USDT:USDT',
        'XRP/USDT:USDT',
        'DOT/USDT:USDT',
        'AVAX/USDT:USDT'
    ];
    
    symbolSelect.innerHTML = '';
    symbols.forEach(symbol => {
        const option = document.createElement('option');
        option.value = symbol;
        option.textContent = symbol.replace(':USDT', '');
        symbolSelect.appendChild(option);
    });
    
    // Set default symbol
    symbolSelect.value = 'DOGE/USDT:USDT';
    
    // Add change event listener
    symbolSelect.addEventListener('change', function() {
        if (window.liveTradingInterface) {
            window.liveTradingInterface.currentSymbol = this.value;
            window.liveTradingInterface.updateMarketInfo();
        }
    });
    
    console.log('Symbol selection fixed');
}

function createSymbolSelector() {
    const tradingPanel = document.querySelector('.trading-panel');
    if (!tradingPanel) return;
    
    const symbolContainer = document.createElement('div');
    symbolContainer.className = 'symbol-selector-container';
    symbolContainer.innerHTML = `
        <label for="symbol-select">Trading Symbol:</label>
        <select id="symbol-select" class="symbol-select">
            <option value="DOGE/USDT:USDT">DOGE/USDT</option>
            <option value="BTC/USDT:USDT">BTC/USDT</option>
            <option value="ETH/USDT:USDT">ETH/USDT</option>
            <option value="BNB/USDT:USDT">BNB/USDT</option>
            <option value="ADA/USDT:USDT">ADA/USDT</option>
            <option value="SOL/USDT:USDT">SOL/USDT</option>
            <option value="XRP/USDT:USDT">XRP/USDT</option>
            <option value="DOT/USDT:USDT">DOT/USDT</option>
            <option value="AVAX/USDT:USDT">AVAX/USDT</option>
        </select>
    `;
    
    tradingPanel.insertBefore(symbolContainer, tradingPanel.firstChild);
    fixSymbolSelection();
}

// Auto-fix on page load
document.addEventListener('DOMContentLoaded', fixSymbolSelection);
"""
            
            # Save JavaScript fix
            static_js_dir = Path('static/js')
            static_js_dir.mkdir(parents=True, exist_ok=True)
            
            with open(static_js_dir / 'symbol_selection_fix.js', 'w') as f:
                f.write(js_fix)
            
            logger.info("[OK] JavaScript symbol selection fix created")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create symbol selection fix: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all live data flow tests."""
        logger.info("=" * 60)
        logger.info("LIVE DATA FLOW DIAGNOSTIC TEST SUITE")
        logger.info("=" * 60)
        
        test_results = {}
        
        # Test 1: Market Data Manager
        logger.info("\n[TEST 1] Testing Market Data Manager...")
        test_results['market_data_manager'] = await self.test_market_data_manager()
        
        # Test 2: HTX Client
        logger.info("\n[TEST 2] Testing HTX Client...")
        test_results['htx_client'] = await self.test_htx_client()
        
        # Test 3: WebSocket Streamer
        logger.info("\n[TEST 3] Testing WebSocket Streamer...")
        test_results['websocket_streamer'] = await self.test_websocket_streamer()
        
        # Test 4: Symbol Selection
        logger.info("\n[TEST 4] Testing Symbol Selection...")
        test_results['symbol_selection'] = await self.test_symbol_selection()
        
        # Test 5: Live Trading Interface
        logger.info("\n[TEST 5] Testing Live Trading Interface...")
        test_results['live_trading_interface'] = await self.test_live_trading_interface()
        
        # Create fixes
        logger.info("\n[FIX] Creating Symbol Selection Fix...")
        fix_created = await self.create_symbol_selection_fix()
        
        # Generate report
        logger.info("\n" + "=" * 60)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "[PASS]" if result else "[FAIL]"
            logger.info(f"{status} {test_name.replace('_', ' ').title()}")
        
        logger.info(f"\nPASSED: {passed}/{total}")
        logger.info(f"SUCCESS RATE: {passed/total*100:.1f}%")
        
        if fix_created:
            logger.info("\n[SUCCESS] Symbol selection fix created!")
            logger.info("Next steps:")
            logger.info("1. Restart the Money Circle server")
            logger.info("2. Check symbol dropdown in live trading interface")
            logger.info("3. Verify live data is flowing")
        
        return passed == total

async def main():
    """Main test function."""
    tester = LiveDataFlowTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n[SUCCESS] All tests passed!")
    else:
        print("\n[WARNING] Some tests failed - check logs above")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
