#!/usr/bin/env python3
"""
Symbol Management Utilities
Epinnox V6 - Standalone AI Strategy Tuner
"""

from typing import Dict, List, Optional

class SymbolManager:
    """Utility class for managing trading symbols and their properties."""
    
    # Symbol mappings and metadata
    SYMBOL_MAP = {
        'BTC-USDT': {
            'htx_symbol': 'btcusdt',
            'display_name': 'Bitcoin',
            'min_price_increment': 0.01,
            'min_quantity': 0.001,
            'price_precision': 2,
            'quantity_precision': 6
        },
        'ETH-USDT': {
            'htx_symbol': 'ethusdt',
            'display_name': 'Ethereum',
            'min_price_increment': 0.01,
            'min_quantity': 0.01,
            'price_precision': 2,
            'quantity_precision': 4
        },
        'DOGE-USDT': {
            'htx_symbol': 'dogeusdt',
            'display_name': 'Dog<PERSON>oin',
            'min_price_increment': 0.00001,
            'min_quantity': 1.0,
            'price_precision': 5,
            'quantity_precision': 0
        },
        'SOL-USDT': {
            'htx_symbol': 'solusdt',
            'display_name': 'Solana',
            'min_price_increment': 0.001,
            'min_quantity': 0.1,
            'price_precision': 3,
            'quantity_precision': 1
        },
        'ADA-USDT': {
            'htx_symbol': 'adausdt',
            'display_name': 'Cardano',
            'min_price_increment': 0.0001,
            'min_quantity': 1.0,
            'price_precision': 4,
            'quantity_precision': 0
        }
    }
    
    @classmethod
    def get_htx_symbol(cls, standard_symbol: str) -> Optional[str]:
        """Convert standard symbol to HTX format."""
        symbol_info = cls.SYMBOL_MAP.get(standard_symbol)
        return symbol_info['htx_symbol'] if symbol_info else None
    
    @classmethod
    def get_standard_symbol(cls, htx_symbol: str) -> Optional[str]:
        """Convert HTX symbol to standard format."""
        for standard, info in cls.SYMBOL_MAP.items():
            if info['htx_symbol'] == htx_symbol.lower():
                return standard
        return None
    
    @classmethod
    def get_display_name(cls, symbol: str) -> str:
        """Get display name for symbol."""
        symbol_info = cls.SYMBOL_MAP.get(symbol)
        return symbol_info['display_name'] if symbol_info else symbol
    
    @classmethod
    def get_price_precision(cls, symbol: str) -> int:
        """Get price precision for symbol."""
        symbol_info = cls.SYMBOL_MAP.get(symbol)
        return symbol_info['price_precision'] if symbol_info else 2
    
    @classmethod
    def get_quantity_precision(cls, symbol: str) -> int:
        """Get quantity precision for symbol."""
        symbol_info = cls.SYMBOL_MAP.get(symbol)
        return symbol_info['quantity_precision'] if symbol_info else 4
    
    @classmethod
    def format_price(cls, symbol: str, price: float) -> str:
        """Format price according to symbol precision."""
        precision = cls.get_price_precision(symbol)
        return f"{price:.{precision}f}"
    
    @classmethod
    def format_quantity(cls, symbol: str, quantity: float) -> str:
        """Format quantity according to symbol precision."""
        precision = cls.get_quantity_precision(symbol)
        return f"{quantity:.{precision}f}"
    
    @classmethod
    def get_supported_symbols(cls) -> List[str]:
        """Get list of supported symbols."""
        return list(cls.SYMBOL_MAP.keys())
    
    @classmethod
    def is_supported(cls, symbol: str) -> bool:
        """Check if symbol is supported."""
        return symbol in cls.SYMBOL_MAP
    
    @classmethod
    def get_symbol_info(cls, symbol: str) -> Optional[Dict]:
        """Get complete symbol information."""
        return cls.SYMBOL_MAP.get(symbol)
