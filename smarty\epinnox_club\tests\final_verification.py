#!/usr/bin/env python3
"""
Final comprehensive verification of Money Circle member directory and demo data.
"""

import sqlite3
import sys

def verify_complete_demo_data():
    """Verify all demo data is complete and properly integrated."""
    print("🔍 FINAL MONEY CIRCLE VERIFICATION")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # 1. Verify all demo users exist with profiles
        print("1️⃣ DEMO USERS & PROFILES:")
        users_with_profiles = conn.execute("""
            SELECT 
                u.username, 
                mp.display_name, 
                mp.trading_style, 
                mp.risk_tolerance,
                mp.reputation_score,
                COUNT(DISTINCT ut.id) as trades,
                COUNT(DISTINCT up.id) as positions,
                COALESCE(SUM(up.pnl), 0) as total_pnl
            FROM users u
            JOIN member_profiles mp ON u.id = mp.user_id
            LEFT JOIN user_trades ut ON u.id = ut.user_id
            LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
            WHERE u.username != 'epinnox'
            GROUP BY u.id, u.username, mp.display_name, mp.trading_style, mp.risk_tolerance, mp.reputation_score
            ORDER BY mp.reputation_score DESC
        """).fetchall()
        
        expected_users = [
            'trader_alex', 'crypto_sarah', 'quant_mike', 'forex_emma', 'options_david',
            'swing_lisa', 'momentum_james', 'value_maria', 'algo_robert', 'scalp_jenny'
        ]
        
        found_users = [user['username'] for user in users_with_profiles]
        missing_users = set(expected_users) - set(found_users)
        
        print(f"   ✅ Expected users: {len(expected_users)}")
        print(f"   ✅ Found users: {len(found_users)}")
        print(f"   {'✅' if len(missing_users) == 0 else '❌'} Missing users: {list(missing_users) if missing_users else 'None'}")
        
        # Show sample user data
        print("\n   📊 Sample User Data:")
        for user in users_with_profiles[:3]:
            print(f"      {user['username']} ({user['display_name']})")
            print(f"         Style: {user['trading_style']}, Risk: {user['risk_tolerance']}")
            print(f"         Reputation: {user['reputation_score']:.1f}★, Trades: {user['trades']}, P&L: ${user['total_pnl']:+.0f}")
        
        # 2. Verify trading data distribution
        print(f"\n2️⃣ TRADING DATA DISTRIBUTION:")
        
        total_trades = conn.execute("SELECT COUNT(*) FROM user_trades").fetchone()[0]
        total_positions = conn.execute("SELECT COUNT(*) FROM user_positions WHERE status = 'open'").fetchone()[0]
        
        print(f"   ✅ Total trades: {total_trades}")
        print(f"   ✅ Open positions: {total_positions}")
        
        # Check data per user
        users_with_data = conn.execute("""
            SELECT 
                COUNT(DISTINCT CASE WHEN ut.id IS NOT NULL THEN u.id END) as users_with_trades,
                COUNT(DISTINCT CASE WHEN up.id IS NOT NULL THEN u.id END) as users_with_positions
            FROM users u
            LEFT JOIN user_trades ut ON u.id = ut.user_id
            LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
            WHERE u.username != 'epinnox'
        """).fetchone()
        
        print(f"   ✅ Users with trades: {users_with_data['users_with_trades']}/10")
        print(f"   ✅ Users with positions: {users_with_data['users_with_positions']}/10")
        
        # 3. Verify member directory data integrity
        print(f"\n3️⃣ MEMBER DIRECTORY INTEGRATION:")
        
        # Test the exact query used by member directory
        directory_data = conn.execute("""
            SELECT
                u.id, u.username, mp.display_name, mp.trading_style, mp.risk_tolerance,
                mp.reputation_score, 
                COUNT(DISTINCT ut.id) as trades_count,
                COUNT(DISTINCT up.id) as positions_count,
                COALESCE(SUM(up.pnl), 0) as total_pnl
            FROM users u
            LEFT JOIN member_profiles mp ON u.id = mp.user_id
            LEFT JOIN user_trades ut ON u.id = ut.user_id
            LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
            WHERE u.role IN ('member', 'admin') AND mp.user_id IS NOT NULL
            GROUP BY u.id, u.username, mp.display_name, mp.trading_style, mp.risk_tolerance, mp.reputation_score
            ORDER BY mp.reputation_score DESC
        """).fetchall()
        
        print(f"   ✅ Directory query returns: {len(directory_data)} members")
        
        # Check for complete profiles
        complete_profiles = sum(1 for user in directory_data if all([
            user['display_name'], user['trading_style'], user['risk_tolerance'], user['reputation_score']
        ]))
        
        print(f"   ✅ Complete profiles: {complete_profiles}/{len(directory_data)}")
        
        # 4. Verify achievements system
        print(f"\n4️⃣ ACHIEVEMENTS SYSTEM:")
        
        # Test achievement queries
        achievement_counts = {}
        
        # Top performers
        top_performers = conn.execute("""
            SELECT COUNT(*) as count FROM (
                SELECT u.id FROM users u
                JOIN user_positions up ON u.id = up.user_id
                WHERE up.pnl > 1000
                GROUP BY u.id
                HAVING SUM(up.pnl) > 5000
            )
        """).fetchone()['count']
        achievement_counts['top_performer'] = top_performers
        
        # Consistent traders
        consistent_traders = conn.execute("""
            SELECT COUNT(*) as count FROM users u
            JOIN member_profiles mp ON u.id = mp.user_id
            WHERE mp.reputation_score > 4.0
        """).fetchone()['count']
        achievement_counts['consistent_trader'] = consistent_traders
        
        # Strategy masters
        strategy_masters = conn.execute("""
            SELECT COUNT(*) as count FROM users u
            JOIN member_profiles mp ON u.id = mp.user_id
            WHERE mp.joined_strategies >= 5
        """).fetchone()['count']
        achievement_counts['strategy_master'] = strategy_masters
        
        print(f"   ✅ Top Performers: {achievement_counts['top_performer']} users")
        print(f"   ✅ Consistent Traders: {achievement_counts['consistent_trader']} users")
        print(f"   ✅ Strategy Masters: {achievement_counts['strategy_master']} users")
        
        # 5. Calculate overall success metrics
        print(f"\n5️⃣ SUCCESS METRICS:")
        
        profile_completion = (len(found_users) / len(expected_users)) * 100
        data_coverage = (users_with_data['users_with_trades'] / 10) * 100
        achievement_coverage = (sum(achievement_counts.values()) / (10 * 3)) * 100  # 3 achievement types
        
        print(f"   📊 Profile Completion: {profile_completion:.1f}%")
        print(f"   📊 Trading Data Coverage: {data_coverage:.1f}%")
        print(f"   📊 Achievement Coverage: {achievement_coverage:.1f}%")
        
        overall_score = (profile_completion + data_coverage + achievement_coverage) / 3
        print(f"   🎯 Overall Success Score: {overall_score:.1f}%")
        
        conn.close()
        
        # 6. Final assessment
        print(f"\n6️⃣ FINAL ASSESSMENT:")
        
        if overall_score >= 95:
            print("🎉 EXCELLENT! Demo environment is production-ready!")
            status = "EXCELLENT"
        elif overall_score >= 85:
            print("✅ VERY GOOD! Demo environment is ready for demonstration.")
            status = "VERY_GOOD"
        elif overall_score >= 75:
            print("👍 GOOD! Demo environment is mostly ready.")
            status = "GOOD"
        else:
            print("⚠️ NEEDS IMPROVEMENT! Some issues remain.")
            status = "NEEDS_WORK"
        
        print(f"\n🌟 MONEY CIRCLE MEMBER DIRECTORY STATUS: {status}")
        print("=" * 60)
        
        if overall_score >= 85:
            print("✅ ALL DEMO USERS VISIBLE IN MEMBER DIRECTORY")
            print("✅ COMPLETE PROFILES WITH TRADING ACTIVITY")
            print("✅ REALISTIC PERFORMANCE DATA")
            print("✅ WORKING ACHIEVEMENTS SYSTEM")
            print("✅ PROPER DATA INTEGRATION")
            print("\n🌐 Ready for demonstration at:")
            print("   http://localhost:8084/club/members")
            print("\n🔐 Login with any demo account:")
            print("   Username: trader_alex, crypto_sarah, etc.")
            print("   Password: securepass123")
        
        return overall_score >= 85
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main verification function."""
    success = verify_complete_demo_data()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
