#!/usr/bin/env python3
"""
Test Position Limits Final - Phase 9.8
Tests position limits with opposite direction trades to verify limits work correctly
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

async def test_position_limits_final():
    """Test position limits with opposite direction trades."""
    try:
        logger.info("🧪 Testing Position Limits Final - Phase 9.8")
        
        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
        
        # Set high limits to test position counting
        logger.info("🎯 Setting high limits for testing...")
        test_settings = {
            'max-open-positions': 5,      # Allow 5 positions
            'max-margin-pct': 50.0,       # High margin limit
        }
        
        await dashboard._apply_live_settings(test_settings)
        
        logger.info(f"   Max open positions set to: {execution_controller.max_open_positions}")
        logger.info(f"   Margin limit set to: {execution_controller.margin_usage_limit}%")
        
        # Test with SHORT positions (opposite to existing LONG)
        logger.info("🎯 Testing with SHORT positions (opposite direction)...")
        
        # Create SHORT trading decisions
        short_decisions = []
        for i in range(3):  # Try 3 SHORT positions
            short_decisions.append({
                'symbol': 'DOGE/USDT:USDT',
                'action': 'SHORT',  # Opposite direction
                'confidence': 0.8,
                'conviction_score': 8,
                'reasoning': f'Test SHORT position {i+1} for limit testing',
                'market_regime': 'normal',
                'timestamp': time.time()
            })
        
        # Mock market data
        market_data = {
            'last_price': 0.40,
            'volume': 1000000,
            'volatility': 0.02,
            'timestamp': time.time()
        }
        
        executed_positions = 0
        blocked_positions = 0
        
        for i, decision in enumerate(short_decisions):
            logger.info(f"   Attempting SHORT position {i+1}...")
            
            # Try to execute the decision
            result = await execution_controller.process_trading_decision(decision, market_data)
            
            if result and result.execution:
                executed_positions += 1
                logger.info(f"   ✅ SHORT position {i+1} executed successfully")
            else:
                blocked_positions += 1
                logger.info(f"   🚫 SHORT position {i+1} blocked")
        
        logger.info(f"📊 SHORT Position Results:")
        logger.info(f"   Executed: {executed_positions}")
        logger.info(f"   Blocked: {blocked_positions}")
        
        # Test position limit enforcement
        logger.info("🎯 Testing position limit enforcement...")
        
        # Check current active positions
        current_positions = len(execution_controller.active_positions)
        max_positions = execution_controller.max_open_positions
        
        logger.info(f"   Current active positions: {current_positions}")
        logger.info(f"   Max allowed positions: {max_positions}")
        
        # Try to exceed position limit
        logger.info("🎯 Testing position limit boundary...")
        
        # Create more positions to test the limit
        excess_decisions = []
        for i in range(max_positions + 2):  # Try to exceed limit
            excess_decisions.append({
                'symbol': 'DOGE/USDT:USDT',
                'action': 'SHORT',
                'confidence': 0.8,
                'conviction_score': 8,
                'reasoning': f'Excess position {i+1} to test limits',
                'market_regime': 'normal',
                'timestamp': time.time()
            })
        
        excess_executed = 0
        excess_blocked = 0
        
        for i, decision in enumerate(excess_decisions):
            logger.info(f"   Attempting excess position {i+1}...")
            
            result = await execution_controller.process_trading_decision(decision, market_data)
            
            if result and result.execution:
                excess_executed += 1
                logger.info(f"   ✅ Excess position {i+1} executed")
            else:
                excess_blocked += 1
                logger.info(f"   🚫 Excess position {i+1} blocked")
                
                # Check if blocked by position limit
                current_active = len(execution_controller.active_positions)
                if current_active >= max_positions:
                    logger.info(f"   📊 Position limit reached: {current_active}/{max_positions}")
                    break
        
        logger.info(f"📊 Excess Position Results:")
        logger.info(f"   Executed: {excess_executed}")
        logger.info(f"   Blocked: {excess_blocked}")
        
        # Final verification
        final_positions = len(execution_controller.active_positions)
        logger.info(f"📊 Final active positions: {final_positions}")
        
        # Summary
        logger.info("✅ Position Limits Final Test Complete!")
        logger.info("\n🎯 Summary:")
        logger.info(f"   ✅ Max positions setting: {max_positions}")
        logger.info(f"   ✅ SHORT positions executed: {executed_positions}")
        logger.info(f"   ✅ Excess positions blocked: {excess_blocked}")
        logger.info(f"   ✅ Final active positions: {final_positions}")
        
        # Validation
        if executed_positions > 0:
            logger.info("   ✅ Position execution with opposite direction: PASS")
        else:
            logger.warning("   ⚠️ No positions executed - may be other blocking factors")
        
        if excess_blocked > 0:
            logger.info("   ✅ Position limit enforcement: PASS")
        else:
            logger.warning("   ⚠️ Position limit not tested - insufficient attempts")
        
        if final_positions <= max_positions:
            logger.info("   ✅ Position limit respected: PASS")
        else:
            logger.error("   ❌ Position limit exceeded: FAIL")
        
        logger.info("\n🎉 TRADING CONTROLS ARE WORKING!")
        logger.info("🚀 You can now adjust max open positions in the dashboard and see immediate effects!")
        
    except Exception as e:
        logger.error(f"❌ Error in position limits final test: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_position_limits_final())
