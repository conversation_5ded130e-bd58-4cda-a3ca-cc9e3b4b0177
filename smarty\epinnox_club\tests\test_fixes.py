#!/usr/bin/env python3
"""
Test script to verify Money Circle fixes
Tests the performance middleware and database initialization fixes
"""

import asyncio
import aiohttp
import logging
import time
from pathlib import Path

# Setup logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_health_endpoint(base_url="http://localhost:8086"):
    """Test the health endpoint to verify middleware is working."""
    logger.info("🔍 Testing health endpoint...")

    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.get(f"{base_url}/health") as response:
                response_time = time.time() - start_time

                if response.status == 200:
                    data = await response.json()
                    headers = dict(response.headers)

                    logger.info(f"✅ Health endpoint working (HTTP {response.status})")
                    logger.info(f"⏱️ Response time: {response_time:.3f}s")

                    # Check for performance headers
                    if 'X-Response-Time' in headers:
                        logger.info(f"✅ Performance middleware working (X-Response-Time: {headers['X-Response-Time']})")
                    else:
                        logger.warning("⚠️ Performance middleware headers missing")

                    # Check health data
                    if 'status' in data:
                        logger.info(f"✅ Health status: {data['status']}")

                    return True
                else:
                    logger.error(f"❌ Health endpoint failed (HTTP {response.status})")
                    return False

    except Exception as e:
        logger.error(f"❌ Health endpoint test error: {e}")
        return False

async def test_login_page(base_url="http://localhost:8086"):
    """Test the login page to verify middleware doesn't break normal requests."""
    logger.info("🔍 Testing login page...")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/login") as response:
                if response.status == 200:
                    logger.info("✅ Login page accessible")

                    # Check for performance headers
                    if 'X-Response-Time' in response.headers:
                        logger.info(f"✅ Performance headers present: {response.headers['X-Response-Time']}")

                    return True
                else:
                    logger.error(f"❌ Login page failed (HTTP {response.status})")
                    return False

    except Exception as e:
        logger.error(f"❌ Login page test error: {e}")
        return False

def test_database_initialization():
    """Test database initialization to check for duplicate logging."""
    logger.info("🔍 Testing database initialization...")

    try:
        # Count log messages before
        log_count_before = len([
            record for record in logging.getLogger().handlers[0].buffer
            if hasattr(logging.getLogger().handlers[0], 'buffer')
        ]) if hasattr(logging.getLogger().handlers[0], 'buffer') else 0

        # Import and initialize database components
        from database.models import DatabaseManager
        from database.club_models import ClubDatabaseManager

        # Create database manager
        db_path = "data/test_money_circle.db"
        Path(db_path).unlink(missing_ok=True)  # Remove test database if exists

        db_manager = DatabaseManager(db_path)
        logger.info("✅ Main database initialized")

        # Create club database manager multiple times to test duplicate prevention
        club_db1 = ClubDatabaseManager(db_manager)
        club_db2 = ClubDatabaseManager(db_manager)
        club_db3 = ClubDatabaseManager(db_manager)

        logger.info("✅ Club database managers created (should only see one initialization message)")

        # Clean up test database
        Path(db_path).unlink(missing_ok=True)

        return True

    except Exception as e:
        logger.error(f"❌ Database initialization test error: {e}")
        return False

async def run_all_tests():
    """Run all tests to verify fixes."""
    logger.info("🧪 Running Money Circle Fix Tests")
    logger.info("🔧 Testing performance middleware and database initialization fixes")
    logger.info("=" * 60)

    tests_passed = 0
    total_tests = 3

    # Test 1: Database initialization
    logger.info("\n📋 Test 1: Database Initialization")
    if test_database_initialization():
        tests_passed += 1
        logger.info("✅ Database test PASSED")
    else:
        logger.error("❌ Database test FAILED")

    # Test 2: Health endpoint (requires running server)
    logger.info("\n📋 Test 2: Health Endpoint")
    if await test_health_endpoint():
        tests_passed += 1
        logger.info("✅ Health endpoint test PASSED")
    else:
        logger.warning("⚠️ Health endpoint test FAILED (server may not be running)")

    # Test 3: Login page (requires running server)
    logger.info("\n📋 Test 3: Login Page")
    if await test_login_page():
        tests_passed += 1
        logger.info("✅ Login page test PASSED")
    else:
        logger.warning("⚠️ Login page test FAILED (server may not be running)")

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Fixes are working correctly!")
    elif tests_passed >= 1:
        logger.info("✅ PARTIAL SUCCESS - Some tests passed")
        logger.info("💡 Start Money Circle server to test web endpoints")
    else:
        logger.error("❌ ALL TESTS FAILED - Check the fixes")

    return tests_passed == total_tests

def main():
    """Main test runner."""
    print("🧪 Money Circle Fix Verification")
    print("Testing performance middleware and database initialization fixes")
    print()

    try:
        success = asyncio.run(run_all_tests())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
