#!/usr/bin/env python3
"""
Database Connection Fix Test
Test to verify the database connection issue is resolved
"""

import requests
import re
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection_fix():
    """Test that database connection issues are resolved."""
    logger.info("🔧 TESTING DATABASE CONNECTION FIX")
    logger.info("=" * 50)
    
    base_url = "http://localhost:8086"
    
    try:
        # Step 1: Get login page and extract CSRF token
        logger.info("📋 Step 1: Getting login page...")
        response = requests.get(f"{base_url}/login", timeout=10)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to get login page: {response.status_code}")
            return False
        
        # Extract CSRF token
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if not csrf_match:
            logger.error("❌ CSRF token not found in login page")
            return False
        
        csrf_token = csrf_match.group(1)
        logger.info(f"✅ Login page loaded, CSRF token: {csrf_token[:16]}...")
        
        # Step 2: Test login with correct credentials
        logger.info("\n📋 Step 2: Testing login with epinnox credentials...")
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123',
            'csrf_token': csrf_token
        }
        
        login_response = requests.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
        logger.info(f"Login response status: {login_response.status_code}")
        
        if login_response.status_code == 302:
            location = login_response.headers.get('Location', '')
            logger.info(f"Redirected to: {location}")
            
            if '/dashboard' in location:
                logger.info("✅ Login successful! Database connection working!")
                return True
            elif 'error=invalid_credentials' in location:
                logger.error("❌ Login failed with invalid credentials error")
                logger.error("🔧 This suggests database connection is still broken")
                return False
            else:
                logger.warning(f"⚠️ Unexpected redirect: {location}")
                return False
        elif login_response.status_code == 403:
            logger.error("❌ Login blocked by CSRF protection")
            return False
        else:
            logger.error(f"❌ Unexpected login response: {login_response.status_code}")
            logger.error(f"Response: {login_response.text}")
            return False
        
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Network error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

def test_multiple_login_attempts():
    """Test multiple login attempts to verify connection stability."""
    logger.info("\n📋 Testing multiple login attempts...")
    
    base_url = "http://localhost:8086"
    success_count = 0
    total_attempts = 3
    
    for i in range(total_attempts):
        logger.info(f"Attempt {i+1}/{total_attempts}...")
        
        try:
            # Get fresh CSRF token for each attempt
            response = requests.get(f"{base_url}/login", timeout=10)
            csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
            
            if not csrf_match:
                logger.error(f"❌ Attempt {i+1}: No CSRF token")
                continue
            
            csrf_token = csrf_match.group(1)
            
            # Try login
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123',
                'csrf_token': csrf_token
            }
            
            login_response = requests.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
            
            if login_response.status_code == 302:
                location = login_response.headers.get('Location', '')
                if '/dashboard' in location:
                    logger.info(f"✅ Attempt {i+1}: Success")
                    success_count += 1
                else:
                    logger.error(f"❌ Attempt {i+1}: Failed - {location}")
            else:
                logger.error(f"❌ Attempt {i+1}: HTTP {login_response.status_code}")
            
            # Small delay between attempts
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"❌ Attempt {i+1}: Error - {e}")
    
    logger.info(f"Multiple attempts result: {success_count}/{total_attempts} successful")
    return success_count >= total_attempts * 0.8  # 80% success rate

def main():
    """Main function."""
    print("🔧 Database Connection Fix Test")
    print("Testing resolution of 'Cannot operate on a closed database' error")
    print()
    
    try:
        # Test 1: Single login attempt
        single_success = test_database_connection_fix()
        
        # Test 2: Multiple login attempts
        multiple_success = test_multiple_login_attempts()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS:")
        print(f"✅ Single login test: {'SUCCESS' if single_success else 'FAILED'}")
        print(f"✅ Multiple login test: {'SUCCESS' if multiple_success else 'FAILED'}")
        
        overall_success = single_success and multiple_success
        
        if overall_success:
            print("\n🎉 DATABASE CONNECTION FIX SUCCESSFUL!")
            print("✅ No more 'Cannot operate on a closed database' errors")
            print("✅ User authentication working correctly")
            print("✅ Database connection stable")
            print("✅ Login system fully functional")
            print("\n🌐 You can now login at: http://localhost:8086")
            print("🔐 Use credentials: epinnox / securepass123")
            print("🎯 All Money Circle features should work correctly")
            return 0
        else:
            print("\n❌ DATABASE CONNECTION STILL HAS ISSUES")
            if not single_success:
                print("🔧 Single login attempts are failing")
            if not multiple_success:
                print("🔧 Multiple login attempts are unstable")
            print("🔧 Check server logs for database connection errors")
            return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
