# 🧠 AI Strategy Tuner - Smart Model Integrated

A dedicated, lightweight interface specifically designed for tuning and monitoring the **Smart Model Integrated** strategy (orchestrator.py with full AI + LLM integration).

## 🎯 Purpose

This tool provides a focused environment for:
- **Real-time parameter tuning** of AI model weights and trading thresholds
- **Live visualization** of AI decision-making processes
- **Performance monitoring** of the Smart Model Integrated strategy
- **LLM reasoning analysis** and decision tracking

## 🚀 Quick Start

### Option 1: Using Startup Script (Recommended)
```bash
cd smarty
python start_ai_tuner.py
```

### Option 2: Direct Launch
```bash
cd smarty
python ai_strategy_tuner.py
```

**Access:** http://localhost:8084

## 🎛️ Features

### 1. **Strategy Parameter Modification**
- **Model Weights**: Adjust weights for all 9 AI models in real-time
  - RSI, OrderFlow, Volatility Regime, VWAP Deviation
  - Liquidity Imbalance, GARCH Volatility, Funding Momentum
  - Open Interest Momentum, Social Sentiment
- **Trading Thresholds**: Modify buy/sell thresholds dynamically
- **LLM Settings**: Tune LLM call intervals, temperature, max tokens
- **Risk Parameters**: Adjust position sizing and stop-loss percentages

### 2. **AI Output Visualization**
- **Individual Model Outputs**: Real-time display of each AI model's predictions
- **Confidence Scores**: Visual confidence bars for each model
- **LLM Reasoning**: Live display of LLM decision-making process
- **Signal Timeline**: Chronological view of trading signals

### 3. **Performance Analytics**
- **Real-time Metrics**: Win rate, total P&L, signal count
- **Model Contribution**: Track which models contribute most to performance
- **Performance Charts**: Visual representation of strategy performance
- **Accuracy Tracking**: Monitor individual model accuracy over time

### 4. **Live Monitoring**
- **WebSocket Updates**: Real-time data without page refresh
- **Strategy Status**: Live status indicator (running/stopped)
- **Data Quality**: Monitor data feed health and connectivity

## 🔧 Configuration

The tuner automatically loads and modifies `config.yaml`. Key sections:

### Model Weights (Ensemble Configuration)
```yaml
ensemble_model_config:
  model_weights:
    rsi: 1.0
    orderflow: 1.5
    volatility_regime: 1.2
    vwap_deviation: 1.0
    liquidity_imbalance: 1.0
    garch_volatility: 1.3
    funding_momentum: 1.2
    open_interest_momentum: 1.1
    social_sentiment: 0.8
```

### LLM Settings
```yaml
llm:
  call_interval_s: 30
  temperature: 0.0
  max_tokens: 128
  dummy_mode: false
```

### Trading Parameters
```yaml
trading:
  base_buy_threshold: 0.3
  base_sell_threshold: -0.3
  simulation_mode: true
```

## 🎮 Usage Workflow

### 1. **Start the Tuner**
```bash
python start_ai_tuner.py
```

### 2. **Access Dashboard**
- Open http://localhost:8084
- Review current parameter settings
- Check strategy status

### 3. **Start Strategy**
- Click "Start Strategy" button
- Monitor AI model outputs in real-time
- Observe LLM decision-making process

### 4. **Tune Parameters**
- Adjust model weights based on performance
- Modify trading thresholds for different market conditions
- Fine-tune LLM settings for optimal reasoning

### 5. **Monitor Performance**
- Track real-time performance metrics
- Analyze model contribution to decisions
- Review LLM reasoning for insights

## 📊 Dashboard Panels

### ⚙️ Strategy Parameters
- **Model Weights**: Sliders for all 9 AI models
- **Trading Thresholds**: Buy/sell threshold controls
- **LLM Settings**: Temperature, interval, token controls

### 🤖 AI Model Outputs
- **Real-time Predictions**: Live model outputs
- **Confidence Indicators**: Visual confidence levels
- **Signal Strength**: Model conviction display

### 🧠 LLM Decisions
- **Reasoning Display**: LLM thought process
- **Decision Timeline**: Chronological decisions
- **Context Analysis**: Market context understanding

### 📈 Performance Metrics
- **Win Rate**: Success percentage
- **Total P&L**: Cumulative profit/loss
- **Signal Count**: Total signals generated
- **Model Accuracy**: Individual model performance

## 🔄 Real-time Updates

The dashboard updates every 2 seconds with:
- Latest AI model predictions
- New LLM decisions and reasoning
- Updated performance metrics
- Current strategy status

## 🎯 Optimization Tips

### Model Weight Tuning
1. **Start with default weights** (as configured)
2. **Monitor individual model accuracy** in performance panel
3. **Increase weights** for consistently accurate models
4. **Decrease weights** for underperforming models
5. **Test in simulation mode** before live trading

### LLM Optimization
1. **Temperature 0.0** for deterministic trading decisions
2. **30-second intervals** for balanced responsiveness
3. **128 tokens** for concise but complete reasoning
4. **Monitor reasoning quality** in LLM decisions panel

### Trading Threshold Adjustment
1. **Higher thresholds** (0.4-0.5) for conservative trading
2. **Lower thresholds** (0.2-0.3) for more aggressive signals
3. **Adjust based on market volatility** and performance

## 🔗 Integration

The AI Strategy Tuner integrates seamlessly with:
- **Orchestrator.py**: The core Smart Model Integrated strategy
- **SQLite Bus**: Real-time data pipeline
- **Config.yaml**: Centralized configuration management
- **LLM System**: Phi-3.1 model integration

## 🚨 Important Notes

- **Simulation Mode**: Always test parameter changes in simulation first
- **Backup Config**: The tuner automatically saves config changes
- **Strategy Restart**: Some parameter changes may require strategy restart
- **Performance Impact**: Frequent parameter changes may affect performance

## 🎉 Benefits

✅ **Focused Interface**: Dedicated to Smart Model Integrated strategy
✅ **Real-time Tuning**: Immediate parameter adjustment feedback
✅ **AI Transparency**: Clear visibility into AI decision-making
✅ **Performance Tracking**: Comprehensive metrics and analytics
✅ **Lightweight**: Fast startup compared to full trading platforms
✅ **Professional UI**: Clean, intuitive interface design

The AI Strategy Tuner provides the perfect environment for optimizing your Smart Model Integrated strategy performance! 🚀
