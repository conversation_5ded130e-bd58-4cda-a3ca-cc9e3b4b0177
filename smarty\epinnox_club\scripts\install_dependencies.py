#!/usr/bin/env python3
"""
Dependency Installer for Money Circle
Ensures all required packages are installed for local development
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package):
    """Install a single package using pip."""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install {package}: {e}")
        return False

def main():
    """Install all required dependencies."""
    print("📦 Installing Money Circle Dependencies")
    print("=" * 40)
    
    # Critical dependencies that might be missing
    critical_packages = [
        'PyJWT>=2.8.0',
        'aiofiles>=23.2.0',
        'websockets>=11.0.0',
        'numpy>=1.24.0',
        'pandas>=2.0.0'
    ]
    
    print("Installing critical packages...")
    for package in critical_packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
    
    print("\nInstalling from requirements.txt...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ All requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return 1
    
    print("\n🎉 Dependencies installation complete!")
    print("🚀 You can now run: python run_local.py")
    return 0

if __name__ == '__main__':
    sys.exit(main())
