#!/usr/bin/env python3
"""
Database Migration Script
Ensures the database has the correct schema and adds missing tables/columns.
"""

import sqlite3
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Migrate database to latest schema."""
    db_path = "data/money_circle.db"
    
    if not Path(db_path).exists():
        logger.error(f"Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        
        logger.info("🔄 Starting database migration...")
        
        # Add trading_performance table if it doesn't exist
        logger.info("📊 Adding trading_performance table...")
        conn.execute("""
            CREATE TABLE IF NOT EXISTS trading_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                date DATE NOT NULL,
                portfolio_value REAL DEFAULT 0.0,
                total_return REAL DEFAULT 0.0,
                daily_return REAL DEFAULT 0.0,
                win_rate REAL DEFAULT 0.0,
                trade_size REAL DEFAULT 0.0,
                trades_count INTEGER DEFAULT 0,
                max_drawdown REAL DEFAULT 0.0,
                sharpe_ratio REAL DEFAULT 0.0,
                volatility REAL DEFAULT 0.0,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                UNIQUE(user_id, date)
            )
        """)
        
        # Check if strategy_proposals table has required columns
        logger.info("📋 Checking strategy_proposals table schema...")
        cursor = conn.execute("PRAGMA table_info(strategy_proposals)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            'title': 'TEXT',
            'strategy_type': 'TEXT',
            'is_active': 'BOOLEAN DEFAULT FALSE'
        }
        
        for column, column_type in required_columns.items():
            if column not in columns:
                logger.info(f"➕ Adding missing column: {column}")
                try:
                    conn.execute(f"ALTER TABLE strategy_proposals ADD COLUMN {column} {column_type}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        logger.warning(f"⚠️ Could not add column {column}: {e}")
        
        # Seed some demo trading performance data
        logger.info("📈 Seeding demo trading performance data...")
        
        # Get demo users
        demo_users = conn.execute("""
            SELECT id FROM users WHERE username IN ('trader_alex', 'crypto_sarah', 'quant_mike', 'forex_emma')
        """).fetchall()
        
        if demo_users:
            import random
            from datetime import datetime, timedelta
            
            # Generate 30 days of performance data for each demo user
            for user in demo_users:
                user_id = user[0]
                base_date = datetime.now() - timedelta(days=30)
                
                for i in range(30):
                    date = base_date + timedelta(days=i)
                    
                    # Generate realistic trading performance data
                    portfolio_value = 10000 + random.uniform(-2000, 5000)
                    daily_return = random.uniform(-5.0, 8.0)
                    total_return = random.uniform(-15.0, 25.0)
                    win_rate = random.uniform(0.4, 0.8)
                    trades_count = random.randint(0, 15)
                    
                    conn.execute("""
                        INSERT OR REPLACE INTO trading_performance
                        (user_id, date, portfolio_value, total_return, daily_return, win_rate, trades_count)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (user_id, date.date().isoformat(), portfolio_value, total_return, daily_return, win_rate, trades_count))
        
        # Update strategy_proposals with missing data
        logger.info("🔧 Updating strategy_proposals with missing data...")
        
        # Set default values for missing columns
        conn.execute("""
            UPDATE strategy_proposals 
            SET title = COALESCE(title, name),
                strategy_type = COALESCE(strategy_type, 'algorithmic'),
                is_active = COALESCE(is_active, 0)
            WHERE title IS NULL OR strategy_type IS NULL OR is_active IS NULL
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("✅ Database migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during database migration: {e}")
        return False

if __name__ == "__main__":
    migrate_database()
