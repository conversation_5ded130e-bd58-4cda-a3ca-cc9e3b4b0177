#!/usr/bin/env python3
"""
Test script to verify Money Circle app can start up properly
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_app_startup():
    """Test that the app can be created and configured."""
    try:
        print("[TEST] Testing Money Circle app startup...")

        # Import the app
        from app import MoneyCircleApp
        print("[OK] App module imported successfully")

        # Create app instance
        app_instance = MoneyCircleApp()
        print("[OK] App instance created successfully")

        # Create web application
        web_app = await app_instance.create_app()
        print("[OK] Web application created successfully")

        # Check that routes are configured
        routes = list(web_app.router.routes())
        print(f"[OK] {len(routes)} routes configured")

        # Check key routes exist
        route_paths = [route.resource.canonical for route in routes if hasattr(route.resource, 'canonical')]
        key_routes = ['/dashboard', '/club', '/club/strategies', '/club/members', '/club/analytics']

        for route in key_routes:
            if route in route_paths:
                print(f"[OK] Route {route} configured")
            else:
                print(f"[ERROR] Route {route} missing")

        print("\n[SUCCESS] App startup test completed successfully!")
        return True

    except Exception as e:
        print(f"[ERROR] App startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_template_rendering():
    """Test that templates can be rendered."""
    try:
        print("\n[TEST] Testing template rendering...")

        from app import MoneyCircleApp
        import aiohttp_jinja2
        from aiohttp.test_utils import make_mocked_request

        # Create app instance
        app_instance = MoneyCircleApp()
        web_app = await app_instance.create_app()

        # Create a mock request
        request = make_mocked_request('GET', '/dashboard', app=web_app)

        # Test personal dashboard template
        try:
            context = {
                'user': {'username': 'test_user', 'role': 'member'},
                'portfolio': {
                    'total_value': 1000.0,
                    'available_balance': 500.0,
                    'open_positions': 2,
                    'daily_pnl': 25.50,
                    'daily_change': 2.55,
                    'win_rate': 65.0,
                    'avg_trade': 12.50,
                    'max_drawdown': 5.0,
                    'sharpe_ratio': 1.2
                },
                'exchanges': []
            }

            aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
            print("[OK] Personal dashboard template renders successfully")

        except Exception as e:
            print(f"[ERROR] Personal dashboard template error: {e}")

        # Test club dashboard template
        try:
            context = {
                'user': {'username': 'test_user', 'role': 'admin'},
                'club_stats': {
                    'total_members': 15,
                    'active_strategies': 5,
                    'performance': 8.5,
                    'total_volume': 50000.0
                },
                'pending_strategies': [],
                'active_votes': [],
                'recent_activities': [],
                'top_strategies': [],
                'top_members': [],
                'notifications': []
            }

            aiohttp_jinja2.render_template('club_dashboard.html', request, context)
            print("[OK] Club dashboard template renders successfully")

        except Exception as e:
            print(f"[ERROR] Club dashboard template error: {e}")

        print("[OK] Template rendering test completed")
        return True

    except Exception as e:
        print(f"[ERROR] Template rendering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("[STARTUP] Starting Money Circle comprehensive tests...\n")

    # Test app startup
    startup_success = await test_app_startup()

    # Test template rendering
    template_success = await test_template_rendering()

    if startup_success and template_success:
        print("\n[SUCCESS] All tests passed! Money Circle is ready to run.")
        print("\n[INFO] To start the application:")
        print("   python app.py")
        print("\n[INFO] Then visit: http://localhost:8080")
        return True
    else:
        print("\n[ERROR] Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
