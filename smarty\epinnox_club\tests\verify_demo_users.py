#!/usr/bin/env python3
"""
Verify demo users in the database.
"""

import sqlite3
import hashlib

def verify_users():
    """Verify demo users exist and can authenticate."""
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # Check all users
        users = conn.execute("SELECT * FROM users").fetchall()
        print(f"Total users in database: {len(users)}")
        
        for user in users:
            print(f"User: {user['username']}, Email: {user['email']}, Role: {user['role']}")
        
        # Test password hash
        test_password = "securepass123"
        test_hash = hashlib.sha256(test_password.encode()).hexdigest()
        
        # Check if trader_alex exists with correct password
        alex = conn.execute("SELECT * FROM users WHERE username = ?", ('trader_alex',)).fetchone()
        if alex:
            print(f"\ntrader_alex found:")
            print(f"  Stored hash: {alex['hashed_password'][:20]}...")
            print(f"  Test hash:   {test_hash[:20]}...")
            print(f"  Match: {alex['hashed_password'] == test_hash}")
        else:
            print("\ntrader_alex not found!")
        
        # Check strategies
        strategies = conn.execute("SELECT * FROM strategy_proposals").fetchall()
        print(f"\nTotal strategies: {len(strategies)}")
        
        # Check trades
        trades = conn.execute("SELECT COUNT(*) as count FROM user_trades").fetchone()
        print(f"Total trades: {trades['count']}")
        
        # Check positions
        positions = conn.execute("SELECT COUNT(*) as count FROM user_positions").fetchone()
        print(f"Total positions: {positions['count']}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    verify_users()
