#!/usr/bin/env python3
"""
Simple CSRF Security Test
Test CSRF fixes without aiodns dependency
"""

import requests
import logging
import sys
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8086"

class SimpleCSRFTester:
    """Simple CSRF security tester using requests."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = requests.Session()
        self.session_cookie = None
        self.csrf_token = None
        self.test_results = {}
        logger.info("Simple CSRF Tester initialized")
    
    def run_all_tests(self):
        """Run all CSRF security tests."""
        try:
            logger.info("=" * 70)
            logger.info("CSRF SECURITY FIXES TEST SUITE (SIMPLE)")
            logger.info("=" * 70)
            
            # Test 1: CSRF token endpoint
            self._test_csrf_token_endpoint()
            
            # Test 2: Login and get session
            self._test_login_and_session()
            
            # Test 3: Authenticated API requests (should work without CSRF)
            self._test_authenticated_api_requests()
            
            # Test 4: Exchange balance API (the specific failing endpoint)
            self._test_exchange_balance_api()
            
            # Test 5: Dashboard functionality
            self._test_dashboard_functionality()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        
        return True
    
    def _test_csrf_token_endpoint(self):
        """Test CSRF token endpoint."""
        logger.info("[TEST 1] Testing CSRF Token Endpoint...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/csrf-token")
            
            if response.status_code == 200:
                token_data = response.json()
                self.csrf_token = token_data.get('csrf_token')
                
                if self.csrf_token and len(self.csrf_token) == 64:
                    logger.info(f"✅ CSRF token endpoint working: {self.csrf_token[:16]}...")
                    self.test_results['csrf_token_endpoint'] = 'PASS'
                else:
                    logger.error("❌ Invalid CSRF token format")
                    self.test_results['csrf_token_endpoint'] = 'FAIL'
            else:
                logger.error(f"❌ CSRF token endpoint failed: {response.status_code}")
                self.test_results['csrf_token_endpoint'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"CSRF token endpoint test failed: {e}")
            self.test_results['csrf_token_endpoint'] = 'FAIL'
    
    def _test_login_and_session(self):
        """Test login and session management."""
        logger.info("[TEST 2] Testing Login and Session Management...")
        
        try:
            # First get CSRF token for login
            if not self.csrf_token:
                response = self.session.get(f"{BASE_URL}/api/csrf-token")
                if response.status_code == 200:
                    token_data = response.json()
                    csrf_token = token_data.get('csrf_token')
                    logger.info(f"✅ Got CSRF token for login: {csrf_token[:16]}...")
                else:
                    logger.error(f"❌ Failed to get CSRF token: {response.status_code}")
                    self.test_results['login_session'] = 'FAIL'
                    return
            else:
                csrf_token = self.csrf_token
            
            # Login with CSRF token
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123',
                'csrf_token': csrf_token
            }
            
            response = self.session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
            
            if response.status_code == 302:  # Redirect after successful login
                # Check if we got session cookie
                if 'session_id' in self.session.cookies:
                    self.session_cookie = self.session.cookies['session_id']
                    logger.info(f"✅ Login successful, session: {self.session_cookie[:16]}...")
                    self.test_results['login_session'] = 'PASS'
                else:
                    logger.error("❌ No session cookie received")
                    self.test_results['login_session'] = 'FAIL'
            else:
                logger.error(f"❌ Login failed: {response.status_code}")
                self.test_results['login_session'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Login test failed: {e}")
            self.test_results['login_session'] = 'FAIL'
    
    def _test_authenticated_api_requests(self):
        """Test authenticated API requests (should work without CSRF)."""
        logger.info("[TEST 3] Testing Authenticated API Requests...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping authenticated API test")
                self.test_results['authenticated_api'] = 'SKIP'
                return
            
            # Test portfolio API (GET request)
            response = self.session.get(f"{BASE_URL}/api/portfolio")
            
            if response.status_code == 200:
                logger.info("✅ Authenticated GET API request successful")
                get_result = 'PASS'
            else:
                logger.error(f"❌ Authenticated GET API request failed: {response.status_code}")
                get_result = 'FAIL'
            
            # Test exchanges list API (GET request)
            response = self.session.get(f"{BASE_URL}/api/exchanges/list")
            
            if response.status_code == 200:
                logger.info("✅ Authenticated exchanges list API successful")
                list_result = 'PASS'
            else:
                logger.error(f"❌ Authenticated exchanges list API failed: {response.status_code}")
                list_result = 'FAIL'
            
            # Overall result
            if get_result == 'PASS' and list_result == 'PASS':
                self.test_results['authenticated_api'] = 'PASS'
            elif get_result == 'PASS' or list_result == 'PASS':
                self.test_results['authenticated_api'] = 'PARTIAL'
            else:
                self.test_results['authenticated_api'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Authenticated API test failed: {e}")
            self.test_results['authenticated_api'] = 'FAIL'
    
    def _test_exchange_balance_api(self):
        """Test the specific exchange balance API that was failing."""
        logger.info("[TEST 4] Testing Exchange Balance API (Exchange ID 48)...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping exchange balance test")
                self.test_results['exchange_balance_api'] = 'SKIP'
                return
            
            # Test the specific endpoint that was failing
            exchange_id = 48  # The exchange ID from the error logs
            
            # Test without CSRF token (should work for authenticated users)
            response = self.session.post(f"{BASE_URL}/api/exchanges/balance/{exchange_id}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        logger.info("✅ Exchange balance API successful without CSRF token")
                        self.test_results['exchange_balance_api'] = 'PASS'
                    else:
                        logger.warning(f"⚠️ Exchange balance API returned error: {data.get('error')}")
                        self.test_results['exchange_balance_api'] = 'PARTIAL'
                except:
                    logger.info("✅ Exchange balance API responded (non-JSON response)")
                    self.test_results['exchange_balance_api'] = 'PASS'
            elif response.status_code == 403:
                logger.error("❌ Exchange balance API still blocked by CSRF protection")
                self.test_results['exchange_balance_api'] = 'FAIL'
            else:
                logger.warning(f"⚠️ Exchange balance API unexpected status: {response.status_code}")
                # Check response content for clues
                try:
                    content = response.text[:200]
                    logger.info(f"Response content: {content}")
                except:
                    pass
                self.test_results['exchange_balance_api'] = 'PARTIAL'
                
        except Exception as e:
            logger.error(f"Exchange balance API test failed: {e}")
            self.test_results['exchange_balance_api'] = 'FAIL'
    
    def _test_dashboard_functionality(self):
        """Test dashboard functionality."""
        logger.info("[TEST 5] Testing Dashboard Functionality...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping dashboard test")
                self.test_results['dashboard_functionality'] = 'SKIP'
                return
            
            # Test dashboard page load
            response = self.session.get(f"{BASE_URL}/dashboard")
            
            if response.status_code == 200:
                content = response.text
                
                # Check if dashboard loaded properly
                if 'dashboard-container' in content and 'dashboard_csrf_fix.js' in content:
                    logger.info("✅ Dashboard page loaded with CSRF manager")
                    dashboard_result = 'PASS'
                elif 'dashboard-container' in content:
                    logger.info("✅ Dashboard page loaded (CSRF manager may be optional)")
                    dashboard_result = 'PASS'
                else:
                    logger.warning("⚠️ Dashboard page missing expected content")
                    dashboard_result = 'PARTIAL'
            else:
                logger.error(f"❌ Dashboard page failed to load: {response.status_code}")
                dashboard_result = 'FAIL'
            
            self.test_results['dashboard_functionality'] = dashboard_result
                    
        except Exception as e:
            logger.error(f"Dashboard functionality test failed: {e}")
            self.test_results['dashboard_functionality'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("CSRF SECURITY FIXES TEST RESULTS")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] CSRF security fixes working!")
            logger.info("\nThe Money Circle platform should now have:")
            logger.info("- Working authenticated API requests without CSRF token requirements")
            logger.info("- Proper CSRF protection for form submissions")
            logger.info("- Functional exchange balance API for dashboard")
            logger.info("- Enhanced security without blocking legitimate requests")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most CSRF fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical CSRF issues still need attention")
        
        logger.info("=" * 70)

def main():
    """Main test function."""
    tester = SimpleCSRFTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 CSRF security fixes test suite completed!")
    else:
        print("\n❌ CSRF security fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
