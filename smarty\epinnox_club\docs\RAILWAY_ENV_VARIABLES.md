# Railway Environment Variables Configuration
## Money Circle Investment Club Platform

### **🔧 REQUIRED ENVIRONMENT VARIABLES**

Copy and paste these variables into your Railway app's Variables tab:

#### **Core Configuration**
```
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=$PORT
PYTHONUNBUFFERED=1
```

#### **Database Configuration**
```
DATABASE_URL=${{Postgres.DATABASE_URL}}
```
*Note: Railway will automatically populate this when you add PostgreSQL*

#### **Security Configuration**
```
JWT_SECRET=your-secure-32-character-jwt-secret-here
SESSION_TIMEOUT=7200
HTTPS_ONLY=true
SECURE_COOKIES=true
CSRF_PROTECTION=true
```

#### **Trading Configuration**
```
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
MAX_POSITION_SIZE=1000.0
RISK_LIMIT_PERCENT=2.0
```

#### **Exchange Configuration**
```
BINANCE_ENABLED=true
HTX_ENABLED=true
BYBIT_ENABLED=true
EXCHANGE_TIMEOUT=30
EXCHANGE_RETRY_ATTEMPTS=3
```

#### **Monitoring Configuration**
```
MONITORING_ENABLED=true
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true
METRICS_INTERVAL=60
```

#### **Backup Configuration**
```
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
MAX_BACKUPS=30
BACKUP_COMPRESSION=true
```

#### **Rate Limiting**
```
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
```

---

### **🔐 GENERATING JWT SECRET**

**IMPORTANT**: You need to generate a secure JWT secret. Use one of these methods:

#### **Method 1: Python (Recommended)**
```python
import secrets
print(secrets.token_urlsafe(32))
```

#### **Method 2: Online Generator**
Go to: https://generate-secret.vercel.app/32

#### **Method 3: Command Line**
```bash
openssl rand -base64 32
```

**Copy the generated secret and use it as your JWT_SECRET value.**

---

### **📋 STEP-BY-STEP VARIABLE SETUP**

1. **Go to your Railway project dashboard**
2. **Click on your app service (not the database)**
3. **Click the "Variables" tab**
4. **Click "New Variable" for each variable below:**

#### **Add these variables one by one:**

1. **ENVIRONMENT** = `production`
2. **DEBUG** = `false`
3. **HOST** = `0.0.0.0`
4. **PORT** = `$PORT` (Railway will auto-populate this)
5. **PYTHONUNBUFFERED** = `1`
6. **DATABASE_URL** = `${{Postgres.DATABASE_URL}}` (Railway will auto-populate this)
7. **JWT_SECRET** = `[your-generated-secret-here]`
8. **SESSION_TIMEOUT** = `7200`
9. **HTTPS_ONLY** = `true`
10. **SECURE_COOKIES** = `true`
11. **CSRF_PROTECTION** = `true`
12. **LIVE_TRADING_ENABLED** = `true`
13. **TESTNET_MODE** = `false`
14. **MAX_POSITION_SIZE** = `1000.0`
15. **RISK_LIMIT_PERCENT** = `2.0`
16. **BINANCE_ENABLED** = `true`
17. **HTX_ENABLED** = `true`
18. **BYBIT_ENABLED** = `true`
19. **EXCHANGE_TIMEOUT** = `30`
20. **EXCHANGE_RETRY_ATTEMPTS** = `3`
21. **MONITORING_ENABLED** = `true`
22. **PERFORMANCE_MONITORING** = `true`
23. **ERROR_TRACKING** = `true`
24. **METRICS_INTERVAL** = `60`
25. **BACKUP_ENABLED** = `true`
26. **BACKUP_INTERVAL** = `3600`
27. **MAX_BACKUPS** = `30`
28. **BACKUP_COMPRESSION** = `true`
29. **RATE_LIMIT_REQUESTS** = `1000`
30. **RATE_LIMIT_WINDOW** = `3600`
31. **MAX_LOGIN_ATTEMPTS** = `5`
32. **LOCKOUT_DURATION** = `900`

---

### **✅ VERIFICATION CHECKLIST**

After adding all variables, verify:

- [ ] **All 32 variables added**
- [ ] **JWT_SECRET is 32+ characters long**
- [ ] **DATABASE_URL shows the PostgreSQL connection string**
- [ ] **PORT shows $PORT (Railway's dynamic port)**
- [ ] **No typos in variable names**
- [ ] **Boolean values are lowercase** (true/false, not True/False)

---

### **🔄 REDEPLOY AFTER ADDING VARIABLES**

After adding all environment variables:

1. **Go to the "Deployments" tab**
2. **Click "Redeploy"** or push a new commit to GitHub
3. **Wait for deployment to complete** (2-3 minutes)
4. **Check logs for any errors**

---

### **🚨 TROUBLESHOOTING**

#### **Common Issues:**

**Deployment Fails:**
- Check that all required variables are set
- Verify JWT_SECRET is properly generated
- Ensure DATABASE_URL is populated

**Database Connection Errors:**
- Verify PostgreSQL service is running
- Check DATABASE_URL format
- Ensure database migration completed

**Authentication Issues:**
- Verify JWT_SECRET is set correctly
- Check SESSION_TIMEOUT value
- Ensure CSRF_PROTECTION is set to true

**Performance Issues:**
- Check PYTHONUNBUFFERED is set to 1
- Verify MONITORING_ENABLED is true
- Review application logs

---

### **📞 SUPPORT**

If you encounter issues:
1. **Check Railway logs** in the deployment tab
2. **Verify all environment variables** are set correctly
3. **Review the troubleshooting section** above
4. **Contact Railway support** if needed

---

**Next Step**: After setting up all environment variables, proceed to domain configuration or verification testing.
