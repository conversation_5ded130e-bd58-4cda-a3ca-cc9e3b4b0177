#!/usr/bin/env python3
"""
Advanced Alert System - Phase 10.2
Multi-channel alerting system with SMS, Email, Discord, and Telegram support
"""

import asyncio
import logging
import time
import json
import aiohttp
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime, timedelta
import os

logger = logging.getLogger(__name__)

# Email imports with error handling
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    logger.warning("Email functionality not available - install email packages if needed")

@dataclass
class AlertChannel:
    """Alert channel configuration."""
    name: str
    type: str  # 'email', 'sms', 'discord', 'telegram', 'webhook'
    enabled: bool
    config: Dict[str, Any]
    rate_limit_minutes: int = 5  # Minimum minutes between alerts

@dataclass
class AlertRule:
    """Alert rule definition."""
    rule_id: str
    name: str
    condition: str  # Python expression to evaluate
    severity: str  # 'info', 'warning', 'critical'
    channels: List[str]  # Channel names to send to
    enabled: bool = True
    cooldown_minutes: int = 15  # Cooldown between same alerts
    max_alerts_per_hour: int = 10

@dataclass
class Alert:
    """Alert instance."""
    alert_id: str
    rule_id: str
    timestamp: float
    severity: str
    title: str
    message: str
    data: Dict[str, Any]
    channels_sent: List[str] = None
    resolved: bool = False
    resolved_timestamp: Optional[float] = None

class AdvancedAlertSystem:
    """
    Advanced multi-channel alerting system for Onnyx V6.

    Features:
    - Multiple alert channels (Email, SMS, Discord, Telegram, Webhooks)
    - Configurable alert rules with Python expressions
    - Rate limiting and cooldown periods
    - Alert escalation and de-escalation
    - Rich message formatting with trading context
    - Alert acknowledgment and resolution tracking
    - Integration with production monitor
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.alert_config = config.get('alert_system', {})

        # Alert channels
        self.channels: Dict[str, AlertChannel] = {}
        self._load_channels()

        # Alert rules
        self.rules: Dict[str, AlertRule] = {}
        self._load_rules()

        # Alert history
        self.alerts: List[Alert] = []
        self.alert_counts: Dict[str, int] = {}  # Rule ID -> count in last hour
        self.last_alert_times: Dict[str, float] = {}  # Rule ID -> last alert time

        # Rate limiting
        self.channel_last_sent: Dict[str, float] = {}  # Channel -> last sent time

        # External integrations
        self.production_monitor = None

        logger.info("🚨 Advanced Alert System initialized")

    def set_production_monitor(self, monitor):
        """Set production monitor integration."""
        self.production_monitor = monitor
        logger.info("🔗 Alert system connected to production monitor")

    def _load_channels(self):
        """Load alert channels from configuration."""
        channels_config = self.alert_config.get('channels', {})

        # Email channel
        if 'email' in channels_config:
            email_config = channels_config['email']
            self.channels['email'] = AlertChannel(
                name='email',
                type='email',
                enabled=email_config.get('enabled', False),
                config=email_config,
                rate_limit_minutes=email_config.get('rate_limit_minutes', 5)
            )

        # SMS channel (using email-to-SMS gateways)
        if 'sms' in channels_config:
            sms_config = channels_config['sms']
            self.channels['sms'] = AlertChannel(
                name='sms',
                type='sms',
                enabled=sms_config.get('enabled', False),
                config=sms_config,
                rate_limit_minutes=sms_config.get('rate_limit_minutes', 10)
            )

        # Discord webhook
        if 'discord' in channels_config:
            discord_config = channels_config['discord']
            self.channels['discord'] = AlertChannel(
                name='discord',
                type='discord',
                enabled=discord_config.get('enabled', False),
                config=discord_config,
                rate_limit_minutes=discord_config.get('rate_limit_minutes', 2)
            )

        # Telegram bot
        if 'telegram' in channels_config:
            telegram_config = channels_config['telegram']
            self.channels['telegram'] = AlertChannel(
                name='telegram',
                type='telegram',
                enabled=telegram_config.get('enabled', False),
                config=telegram_config,
                rate_limit_minutes=telegram_config.get('rate_limit_minutes', 2)
            )

        # Custom webhook
        if 'webhook' in channels_config:
            webhook_config = channels_config['webhook']
            self.channels['webhook'] = AlertChannel(
                name='webhook',
                type='webhook',
                enabled=webhook_config.get('enabled', False),
                config=webhook_config,
                rate_limit_minutes=webhook_config.get('rate_limit_minutes', 1)
            )

        logger.info(f"📡 Loaded {len(self.channels)} alert channels")

    def _load_rules(self):
        """Load alert rules from configuration."""
        rules_config = self.alert_config.get('rules', {})

        # Default critical system rules
        default_rules = {
            'high_cpu': AlertRule(
                rule_id='high_cpu',
                name='High CPU Usage',
                condition='system_metrics.cpu_percent > 85',
                severity='critical',
                channels=['email', 'sms', 'discord'],
                cooldown_minutes=10
            ),
            'high_memory': AlertRule(
                rule_id='high_memory',
                name='High Memory Usage',
                condition='system_metrics.memory_percent > 90',
                severity='critical',
                channels=['email', 'sms'],
                cooldown_minutes=10
            ),
            'trading_stopped': AlertRule(
                rule_id='trading_stopped',
                name='Trading System Stopped',
                condition='(time.time() - trading_metrics.last_signal_time) > 300',  # 5 minutes
                severity='critical',
                channels=['email', 'sms', 'discord', 'telegram'],
                cooldown_minutes=5
            ),
            'high_latency': AlertRule(
                rule_id='high_latency',
                name='High Execution Latency',
                condition='trading_metrics.execution_latency_ms > 3000',
                severity='warning',
                channels=['discord', 'telegram'],
                cooldown_minutes=15
            ),
            'daily_loss_limit': AlertRule(
                rule_id='daily_loss_limit',
                name='Daily Loss Limit Approached',
                condition='trading_metrics.daily_pnl < -2.0',  # $2 loss
                severity='critical',
                channels=['email', 'sms', 'discord'],
                cooldown_minutes=30
            ),
            'position_stuck': AlertRule(
                rule_id='position_stuck',
                name='Position Stuck Too Long',
                condition='trading_metrics.active_positions > 0 and (time.time() - trading_metrics.last_execution_time) > 1800',  # 30 minutes
                severity='warning',
                channels=['discord', 'telegram'],
                cooldown_minutes=20
            )
        }

        # Load custom rules from config
        for rule_id, rule_config in rules_config.items():
            self.rules[rule_id] = AlertRule(
                rule_id=rule_id,
                name=rule_config.get('name', rule_id),
                condition=rule_config.get('condition', 'False'),
                severity=rule_config.get('severity', 'info'),
                channels=rule_config.get('channels', ['discord']),
                enabled=rule_config.get('enabled', True),
                cooldown_minutes=rule_config.get('cooldown_minutes', 15),
                max_alerts_per_hour=rule_config.get('max_alerts_per_hour', 10)
            )

        # Add default rules if not overridden
        for rule_id, rule in default_rules.items():
            if rule_id not in self.rules:
                self.rules[rule_id] = rule

        logger.info(f"📋 Loaded {len(self.rules)} alert rules")

    async def start_monitoring(self):
        """Start alert monitoring loop."""
        logger.info("🚀 Starting alert monitoring...")

        while True:
            try:
                await self._check_alert_rules()
                await self._cleanup_old_alerts()
                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"Error in alert monitoring: {e}")
                await asyncio.sleep(10)

    async def _check_alert_rules(self):
        """Check all alert rules against current metrics."""
        if not self.production_monitor:
            return

        try:
            # Get current metrics
            current_metrics = self.production_monitor.get_current_metrics()

            if not current_metrics:
                return

            system_metrics = current_metrics.get('system')
            trading_metrics = current_metrics.get('trading')

            if not system_metrics or not trading_metrics:
                return

            # Create evaluation context
            eval_context = {
                'system_metrics': type('obj', (object,), system_metrics),
                'trading_metrics': type('obj', (object,), trading_metrics),
                'time': time,
                'current_time': time.time()
            }

            # Check each rule
            for rule in self.rules.values():
                if not rule.enabled:
                    continue

                try:
                    # Check cooldown
                    if self._is_rule_in_cooldown(rule.rule_id):
                        continue

                    # Check rate limit
                    if self._is_rule_rate_limited(rule.rule_id):
                        continue

                    # Evaluate condition
                    if eval(rule.condition, {"__builtins__": {}}, eval_context):
                        await self._trigger_alert(rule, current_metrics)

                except Exception as e:
                    logger.error(f"Error evaluating rule {rule.rule_id}: {e}")

        except Exception as e:
            logger.error(f"Error checking alert rules: {e}")

    def _is_rule_in_cooldown(self, rule_id: str) -> bool:
        """Check if rule is in cooldown period."""
        if rule_id not in self.last_alert_times:
            return False

        rule = self.rules[rule_id]
        last_time = self.last_alert_times[rule_id]
        cooldown_seconds = rule.cooldown_minutes * 60

        return (time.time() - last_time) < cooldown_seconds

    def _is_rule_rate_limited(self, rule_id: str) -> bool:
        """Check if rule has exceeded rate limit."""
        rule = self.rules[rule_id]
        current_time = time.time()
        one_hour_ago = current_time - 3600

        # Count alerts in last hour
        recent_alerts = [
            a for a in self.alerts
            if a.rule_id == rule_id and a.timestamp > one_hour_ago
        ]

        return len(recent_alerts) >= rule.max_alerts_per_hour

    async def _trigger_alert(self, rule: AlertRule, metrics: Dict[str, Any]):
        """Trigger an alert for the given rule."""
        try:
            # Create alert
            alert = Alert(
                alert_id=f"{rule.rule_id}_{int(time.time())}",
                rule_id=rule.rule_id,
                timestamp=time.time(),
                severity=rule.severity,
                title=rule.name,
                message=self._format_alert_message(rule, metrics),
                data=metrics,
                channels_sent=[]
            )

            # Send to channels
            for channel_name in rule.channels:
                if channel_name in self.channels:
                    channel = self.channels[channel_name]
                    if channel.enabled and not self._is_channel_rate_limited(channel_name):
                        success = await self._send_to_channel(alert, channel)
                        if success:
                            alert.channels_sent.append(channel_name)
                            self.channel_last_sent[channel_name] = time.time()

            # Store alert
            self.alerts.append(alert)
            self.last_alert_times[rule.rule_id] = time.time()

            # Log alert
            severity_icon = "🔴" if rule.severity == 'critical' else "🟡" if rule.severity == 'warning' else "🔵"
            logger.warning(f"{severity_icon} [ALERT] {rule.name}: {alert.message}")

        except Exception as e:
            logger.error(f"Error triggering alert for rule {rule.rule_id}: {e}")

    def _is_channel_rate_limited(self, channel_name: str) -> bool:
        """Check if channel is rate limited."""
        if channel_name not in self.channel_last_sent:
            return False

        channel = self.channels[channel_name]
        last_sent = self.channel_last_sent[channel_name]
        rate_limit_seconds = channel.rate_limit_minutes * 60

        return (time.time() - last_sent) < rate_limit_seconds

    def _format_alert_message(self, rule: AlertRule, metrics: Dict[str, Any]) -> str:
        """Format alert message with context."""
        system = metrics.get('system', {})
        trading = metrics.get('trading', {})

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        message = f"🚨 {rule.name}\n"
        message += f"⏰ Time: {timestamp}\n"
        message += f"🔥 Severity: {rule.severity.upper()}\n\n"

        # Add relevant metrics based on rule
        if 'cpu' in rule.rule_id.lower():
            message += f"💻 CPU Usage: {system.get('cpu_percent', 0):.1f}%\n"

        if 'memory' in rule.rule_id.lower():
            message += f"🧠 Memory Usage: {system.get('memory_percent', 0):.1f}%\n"

        if 'trading' in rule.rule_id.lower() or 'signal' in rule.rule_id.lower():
            message += f"📊 Signals/min: {trading.get('signals_per_minute', 0):.1f}\n"
            message += f"⚡ Execution Latency: {trading.get('execution_latency_ms', 0):.0f}ms\n"
            message += f"📈 Active Positions: {trading.get('active_positions', 0)}\n"
            message += f"💰 Daily P&L: ${trading.get('daily_pnl', 0):.2f}\n"

        message += f"\n🔧 Rule: {rule.condition}"

        return message

    async def _send_to_channel(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert to specific channel."""
        try:
            if channel.type == 'email':
                return await self._send_email(alert, channel)
            elif channel.type == 'sms':
                return await self._send_sms(alert, channel)
            elif channel.type == 'discord':
                return await self._send_discord(alert, channel)
            elif channel.type == 'telegram':
                return await self._send_telegram(alert, channel)
            elif channel.type == 'webhook':
                return await self._send_webhook(alert, channel)
            else:
                logger.warning(f"Unknown channel type: {channel.type}")
                return False

        except Exception as e:
            logger.error(f"Error sending to {channel.name}: {e}")
            return False

    async def _send_email(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert via email."""
        if not EMAIL_AVAILABLE:
            logger.warning("Email functionality not available")
            return False

        try:
            config = channel.config

            # Email configuration
            smtp_server = config.get('smtp_server', 'smtp.gmail.com')
            smtp_port = config.get('smtp_port', 587)
            username = config.get('username')
            password = config.get('password')
            to_emails = config.get('to_emails', [])

            if not username or not password or not to_emails:
                logger.warning("Email configuration incomplete")
                return False

            # Create message
            msg = MimeMultipart()
            msg['From'] = username
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = f"[ONNYX] {alert.title}"

            # Add body
            body = alert.message
            msg.attach(MimeText(body, 'plain'))

            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            text = msg.as_string()
            server.sendmail(username, to_emails, text)
            server.quit()

            logger.info(f"📧 Email alert sent: {alert.title}")
            return True

        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False

    async def _send_sms(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert via SMS (using email-to-SMS gateway)."""
        try:
            config = channel.config

            # SMS configuration (using email-to-SMS)
            phone_numbers = config.get('phone_numbers', [])
            carrier_gateways = config.get('carrier_gateways', {
                'verizon': '@vtext.com',
                'att': '@txt.att.net',
                'tmobile': '@tmomail.net',
                'sprint': '@messaging.sprintpcs.com'
            })

            if not phone_numbers:
                logger.warning("No phone numbers configured for SMS")
                return False

            # Create short message for SMS
            short_message = f"ONNYX ALERT: {alert.title}\n{alert.severity.upper()}\n{datetime.now().strftime('%H:%M:%S')}"

            # Send via email-to-SMS
            email_config = self.channels.get('email', {}).config if 'email' in self.channels else {}
            if not email_config:
                logger.warning("Email configuration required for SMS")
                return False

            # Send to each phone number
            success_count = 0
            for phone_config in phone_numbers:
                phone = phone_config.get('number')
                carrier = phone_config.get('carrier', 'verizon')

                if phone and carrier in carrier_gateways:
                    sms_email = f"{phone}{carrier_gateways[carrier]}"

                    # Create SMS email
                    msg = MimeText(short_message)
                    msg['From'] = email_config.get('username')
                    msg['To'] = sms_email
                    msg['Subject'] = "ONNYX Alert"

                    # Send
                    try:
                        server = smtplib.SMTP(email_config.get('smtp_server', 'smtp.gmail.com'),
                                            email_config.get('smtp_port', 587))
                        server.starttls()
                        server.login(email_config.get('username'), email_config.get('password'))
                        server.send_message(msg)
                        server.quit()
                        success_count += 1
                    except Exception as e:
                        logger.error(f"Error sending SMS to {phone}: {e}")

            if success_count > 0:
                logger.info(f"📱 SMS alerts sent to {success_count} numbers")
                return True

            return False

        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            return False

    async def _send_discord(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert to Discord webhook."""
        try:
            config = channel.config
            webhook_url = config.get('webhook_url')

            if not webhook_url:
                logger.warning("Discord webhook URL not configured")
                return False

            # Create Discord embed
            color = 0xFF0000 if alert.severity == 'critical' else 0xFFFF00 if alert.severity == 'warning' else 0x0099FF

            embed = {
                "title": f"🚨 {alert.title}",
                "description": alert.message,
                "color": color,
                "timestamp": datetime.utcnow().isoformat(),
                "footer": {
                    "text": "Onnyx V6 Trading System"
                },
                "fields": [
                    {
                        "name": "Severity",
                        "value": alert.severity.upper(),
                        "inline": True
                    },
                    {
                        "name": "Alert ID",
                        "value": alert.alert_id,
                        "inline": True
                    }
                ]
            }

            payload = {
                "embeds": [embed]
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 204:
                        logger.info(f"💬 Discord alert sent: {alert.title}")
                        return True
                    else:
                        logger.error(f"Discord webhook failed: {response.status}")
                        return False

        except Exception as e:
            logger.error(f"Error sending Discord alert: {e}")
            return False

    async def _send_telegram(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert to Telegram bot."""
        try:
            config = channel.config
            bot_token = config.get('bot_token')
            chat_ids = config.get('chat_ids', [])

            if not bot_token or not chat_ids:
                logger.warning("Telegram configuration incomplete")
                return False

            # Format message for Telegram
            message = f"🚨 *{alert.title}*\n\n"
            message += f"🔥 Severity: *{alert.severity.upper()}*\n"
            message += f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            message += alert.message.replace('🚨', '').replace(alert.title, '').strip()

            # Send to each chat
            success_count = 0
            for chat_id in chat_ids:
                try:
                    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                    payload = {
                        "chat_id": chat_id,
                        "text": message,
                        "parse_mode": "Markdown"
                    }

                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=payload) as response:
                            if response.status == 200:
                                success_count += 1
                            else:
                                logger.error(f"Telegram send failed for chat {chat_id}: {response.status}")

                except Exception as e:
                    logger.error(f"Error sending to Telegram chat {chat_id}: {e}")

            if success_count > 0:
                logger.info(f"📱 Telegram alerts sent to {success_count} chats")
                return True

            return False

        except Exception as e:
            logger.error(f"Error sending Telegram alert: {e}")
            return False

    async def _send_webhook(self, alert: Alert, channel: AlertChannel) -> bool:
        """Send alert to custom webhook."""
        try:
            config = channel.config
            webhook_url = config.get('url')

            if not webhook_url:
                logger.warning("Webhook URL not configured")
                return False

            # Create webhook payload
            payload = {
                "alert": asdict(alert),
                "timestamp": time.time(),
                "system": "onnyx_v6"
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if 200 <= response.status < 300:
                        logger.info(f"🔗 Webhook alert sent: {alert.title}")
                        return True
                    else:
                        logger.error(f"Webhook failed: {response.status}")
                        return False

        except Exception as e:
            logger.error(f"Error sending webhook alert: {e}")
            return False

    async def _cleanup_old_alerts(self):
        """Clean up old alerts to prevent memory bloat."""
        try:
            current_time = time.time()
            cutoff_time = current_time - (7 * 24 * 3600)  # Keep 7 days

            # Remove old alerts
            self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]

        except Exception as e:
            logger.error(f"Error cleaning up alerts: {e}")

    def get_alert_status(self) -> Dict[str, Any]:
        """Get current alert system status."""
        try:
            current_time = time.time()
            one_hour_ago = current_time - 3600

            # Count recent alerts by severity
            recent_alerts = [a for a in self.alerts if a.timestamp > one_hour_ago]
            critical_count = len([a for a in recent_alerts if a.severity == 'critical'])
            warning_count = len([a for a in recent_alerts if a.severity == 'warning'])
            info_count = len([a for a in recent_alerts if a.severity == 'info'])

            # Channel status
            enabled_channels = [c.name for c in self.channels.values() if c.enabled]

            return {
                'total_alerts_24h': len([a for a in self.alerts if a.timestamp > current_time - 86400]),
                'recent_alerts_1h': len(recent_alerts),
                'critical_alerts_1h': critical_count,
                'warning_alerts_1h': warning_count,
                'info_alerts_1h': info_count,
                'enabled_channels': enabled_channels,
                'total_rules': len(self.rules),
                'enabled_rules': len([r for r in self.rules.values() if r.enabled]),
                'last_alert_time': max([a.timestamp for a in self.alerts]) if self.alerts else 0
            }

        except Exception as e:
            logger.error(f"Error getting alert status: {e}")
            return {'error': str(e)}

    async def test_channels(self) -> Dict[str, bool]:
        """Test all enabled channels with a test alert."""
        results = {}

        test_alert = Alert(
            alert_id=f"test_{int(time.time())}",
            rule_id="test",
            timestamp=time.time(),
            severity="info",
            title="Test Alert",
            message="This is a test alert from Onnyx V6 Alert System",
            data={}
        )

        for channel_name, channel in self.channels.items():
            if channel.enabled:
                logger.info(f"🧪 Testing channel: {channel_name}")
                results[channel_name] = await self._send_to_channel(test_alert, channel)
            else:
                results[channel_name] = False

        return results
