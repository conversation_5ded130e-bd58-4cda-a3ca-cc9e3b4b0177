# Port Configuration Guide

## 🔌 Current Port Setup

The Money Circle platform now uses **port 8087** consistently across all environments and configurations.

### **Unified Port Configuration**

| Environment | Port | Configuration Source |
|-------------|------|---------------------|
| Development | 8087 | `PORT` environment variable or config default |
| Production | 8087 | `PROD_PORT` environment variable or fallback to `PORT` |
| Testing | 8087 | Inherits from base configuration |

## 📁 Configuration Files

### **1. Environment Variables (.env)**
```bash
# Server Configuration
HOST=0.0.0.0
PORT=8087          # Primary port setting
PROD_PORT=8087     # Production-specific port (optional)
```

### **2. Config Classes (config.py)**
```python
# Base Configuration
PORT = int(os.getenv("PORT", 8087))  # Default: 8087

# Production Configuration  
PORT = int(os.getenv("PROD_PORT", os.getenv("PORT", 8087)))
# Priority: PROD_PORT > PORT > 8087
```

### **3. Application Startup**
The server uses `self.config.PORT` which resolves to:
- **Development**: Uses `PORT` environment variable (8087)
- **Production**: Uses `PROD_PORT` if set, otherwise `PORT` (8087)

## 🚀 Server Access Points

### **Development Mode**
```bash
python app.py
# or
python start_money_circle.py
```
**Access**: http://localhost:8087

### **Production Mode**
```bash
FLASK_ENV=production python app.py
```
**Access**: http://0.0.0.0:8087 (or your configured host)

### **Quick Start Scripts**
All scripts in the `scripts/` directory use port 8087:
```bash
python scripts/quick_start.py      # http://localhost:8087
python scripts/run_local.py        # http://localhost:8087
python scripts/working_server.py   # http://localhost:8087
```

## 🔧 Customizing Ports

### **For Development**
Set the `PORT` environment variable:
```bash
export PORT=8088
python app.py
```

### **For Production**
Set the `PROD_PORT` environment variable:
```bash
export PROD_PORT=8085
FLASK_ENV=production python app.py
```

### **In .env File**
```bash
# Change both for consistency
PORT=8088
PROD_PORT=8088
```

## 🌐 Network Configuration

### **Local Development**
- **Host**: `localhost` (127.0.0.1)
- **Port**: `8087`
- **URL**: http://localhost:8087

### **Production Deployment**
- **Host**: `0.0.0.0` (all interfaces)
- **Port**: `8087` (or custom via PROD_PORT)
- **URL**: http://your-domain.com:8087

### **Docker/Container Deployment**
```dockerfile
EXPOSE 8087
ENV PORT=8087
ENV PROD_PORT=8087
```

## 🔒 Security Considerations

### **Firewall Rules**
Ensure port 8087 is open in your firewall:
```bash
# Ubuntu/Debian
sudo ufw allow 8087

# CentOS/RHEL
sudo firewall-cmd --add-port=8087/tcp --permanent
```

### **Reverse Proxy (Nginx)**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8087;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 Troubleshooting

### **Port Already in Use**
```bash
# Check what's using port 8087
netstat -tulpn | grep 8087
# or
lsof -i :8087

# Kill process if needed
sudo kill -9 <PID>
```

### **Permission Denied (Ports < 1024)**
Ports below 1024 require root privileges. Use ports 8087+ for non-root deployment.

### **Configuration Conflicts**
If you see different ports in logs:
1. Check `.env` file for `PORT` and `PROD_PORT`
2. Check environment variables: `echo $PORT`
3. Verify config.py is using correct defaults

## 📊 Port History

| Version | Port | Notes |
|---------|------|-------|
| Legacy | 8084 | Old default in some scripts |
| Mixed | 8085 | Production default |
| Mixed | 8086 | Config default |
| Current | 8087 | Unified across all environments |

## ✅ Verification

Test your port configuration:
```bash
# Check config resolution
python -c "from config import get_config; print(f'Port: {get_config().PORT}')"

# Start server and verify
python app.py
# Should show: [SERVER] Money Circle running at http://localhost:8087
```

---

**Last Updated**: 2025-06-02  
**Current Standard**: Port 8087 for all environments
