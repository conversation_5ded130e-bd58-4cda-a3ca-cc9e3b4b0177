#!/usr/bin/env python3
"""
Test Performance Optimizer - Phase 10.3
Test the performance optimization system
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from optimization.performance_optimizer import PerformanceOptimizer

async def test_performance_optimizer():
    """Test the performance optimization system."""
    try:
        logger.info("🧪 Testing Performance Optimizer - Phase 10.3")
        logger.info("="*60)
        
        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Add performance optimization configuration
        config['performance_optimization'] = {
            'enable_caching': True,
            'enable_batching': True,
            'enable_threading': True,
            'cache_size_limit': 100,
            'batch_size': 5,
            'thread_pool_size': 2,
            'cache_ttl': 10,  # 10 seconds for testing
            'gc_interval': 30  # 30 seconds for testing
        }
        
        # Test 1: Initialize Performance Optimizer
        logger.info("\n🎯 Test 1: Initialize Performance Optimizer")
        logger.info("="*50)
        
        try:
            optimizer = PerformanceOptimizer(config)
            logger.info("✅ Performance Optimizer initialized successfully")
            logger.info(f"   ⚡ Caching enabled: {optimizer.enable_caching}")
            logger.info(f"   📦 Batching enabled: {optimizer.enable_batching}")
            logger.info(f"   🧵 Threading enabled: {optimizer.enable_threading}")
            logger.info(f"   💾 Cache size limit: {optimizer.cache_size_limit}")
            logger.info(f"   📊 Batch size: {optimizer.batch_size}")
            logger.info(f"   🔧 Thread pool size: {optimizer.thread_pool_size}")
        except Exception as e:
            logger.error(f"❌ Performance Optimizer initialization failed: {e}")
            return
        
        # Test 2: Initialize system components
        logger.info("\n🎯 Test 2: Initialize system components")
        logger.info("="*50)
        
        try:
            data_store = LiveDataStore(config)
            execution_controller = ExecutionController(config)
            dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
            
            # Set integrations
            optimizer.set_integrations(data_store, execution_controller, dashboard)
            logger.info("✅ System components integrated with Performance Optimizer")
        except Exception as e:
            logger.error(f"❌ System components integration failed: {e}")
            return
        
        # Test 3: Test signal processing optimization
        logger.info("\n🎯 Test 3: Test signal processing optimization")
        logger.info("="*50)
        
        try:
            # Create test signals
            test_signals = [
                {'id': i, 'symbol': 'DOGE/USDT:USDT', 'action': 'LONG', 'confidence': 0.8}
                for i in range(20)
            ]
            
            # Test without optimization (baseline)
            start_time = time.time()
            baseline_signals = test_signals.copy()
            baseline_time = (time.time() - start_time) * 1000
            
            # Test with optimization
            start_time = time.time()
            optimized_signals = optimizer.optimize_signal_processing(test_signals)
            optimized_time = (time.time() - start_time) * 1000
            
            logger.info(f"✅ Signal processing optimization:")
            logger.info(f"   📊 Signals processed: {len(optimized_signals)}")
            logger.info(f"   ⏱️ Baseline time: {baseline_time:.1f}ms")
            logger.info(f"   ⚡ Optimized time: {optimized_time:.1f}ms")
            
            # Test cache hits (run again)
            start_time = time.time()
            cached_signals = optimizer.optimize_signal_processing(test_signals)
            cached_time = (time.time() - start_time) * 1000
            
            logger.info(f"   🎯 Cached time: {cached_time:.1f}ms")
            logger.info(f"   📈 Cache entries: {len(optimizer.signal_cache)}")
            
        except Exception as e:
            logger.error(f"❌ Signal processing optimization test failed: {e}")
        
        # Test 4: Test dashboard data optimization
        logger.info("\n🎯 Test 4: Test dashboard data optimization")
        logger.info("="*50)
        
        try:
            # Create test dashboard data
            test_data = {
                'symbol': 'DOGE/USDT:USDT',
                'price': 0.179,
                'signals': [{'action': 'LONG', 'confidence': 0.8}],
                'metrics': {'win_rate': 65.5, 'pnl': 12.34},
                'timestamp': time.time()
            }
            
            # Test optimization
            start_time = time.time()
            optimized_data = optimizer.optimize_dashboard_data(test_data)
            optimization_time = (time.time() - start_time) * 1000
            
            logger.info(f"✅ Dashboard data optimization:")
            logger.info(f"   ⏱️ Optimization time: {optimization_time:.1f}ms")
            logger.info(f"   📊 Data optimized: {optimized_data.get('_performance', {}).get('optimization_applied', False)}")
            logger.info(f"   💾 Dashboard cache entries: {len(optimizer.dashboard_cache)}")
            
            # Test cache hit
            start_time = time.time()
            cached_data = optimizer.optimize_dashboard_data(test_data)
            cached_time = (time.time() - start_time) * 1000
            
            logger.info(f"   🎯 Cached retrieval time: {cached_time:.1f}ms")
            
        except Exception as e:
            logger.error(f"❌ Dashboard data optimization test failed: {e}")
        
        # Test 5: Test batch processing
        logger.info("\n🎯 Test 5: Test batch processing")
        logger.info("="*50)
        
        try:
            # Add items to batch queues
            for i in range(10):
                optimizer.add_to_signal_batch({'batch_signal': i})
                optimizer.add_to_dashboard_batch({'batch_dashboard': i})
            
            logger.info(f"✅ Batch processing:")
            logger.info(f"   📦 Signal batch queue: {len(optimizer.signal_batch_queue)} items")
            logger.info(f"   📊 Dashboard batch queue: {len(optimizer.dashboard_batch_queue)} items")
            
            # Process batches manually for testing
            if optimizer.signal_batch_queue:
                await optimizer._process_signal_batch()
                logger.info(f"   ✅ Signal batch processed, remaining: {len(optimizer.signal_batch_queue)}")
            
            if optimizer.dashboard_batch_queue:
                await optimizer._process_dashboard_batch()
                logger.info(f"   ✅ Dashboard batch processed, remaining: {len(optimizer.dashboard_batch_queue)}")
            
        except Exception as e:
            logger.error(f"❌ Batch processing test failed: {e}")
        
        # Test 6: Test performance monitoring (short run)
        logger.info("\n🎯 Test 6: Test performance monitoring")
        logger.info("="*50)
        
        try:
            # Start monitoring for a short period
            logger.info("🚀 Starting performance monitoring for 10 seconds...")
            
            # Create a task to stop monitoring after 10 seconds
            async def stop_after_delay():
                await asyncio.sleep(10)
                optimizer.stop_optimization()
                logger.info("⏹️ Stopping optimization after 10 seconds")
            
            # Start both monitoring and stop timer
            monitoring_task = asyncio.create_task(optimizer.start_optimization())
            stop_task = asyncio.create_task(stop_after_delay())
            
            # Wait for either to complete
            done, pending = await asyncio.wait(
                [monitoring_task, stop_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel any remaining tasks
            for task in pending:
                task.cancel()
            
            logger.info("✅ Performance monitoring test completed")
            
            # Check collected metrics
            if optimizer.metrics_history:
                latest_metrics = optimizer.metrics_history[-1]
                logger.info(f"   📊 Metrics collected: {len(optimizer.metrics_history)}")
                logger.info(f"   ⚡ Latest signal time: {latest_metrics.signal_processing_time_ms:.1f}ms")
                logger.info(f"   📱 Latest dashboard time: {latest_metrics.dashboard_response_time_ms:.1f}ms")
                logger.info(f"   💾 Memory usage: {latest_metrics.memory_usage_mb:.1f}MB")
                logger.info(f"   🎯 Cache hit rate: {latest_metrics.cache_hit_rate:.1f}%")
            else:
                logger.warning("⚠️ No performance metrics collected")
            
        except Exception as e:
            logger.error(f"❌ Performance monitoring test failed: {e}")
        
        # Test 7: Test performance benchmark
        logger.info("\n🎯 Test 7: Test performance benchmark")
        logger.info("="*50)
        
        try:
            benchmark_results = await optimizer.run_performance_benchmark()
            
            if 'error' not in benchmark_results:
                logger.info("✅ Performance benchmark completed:")
                
                # Signal processing results
                signal_results = benchmark_results.get('signal_processing', {})
                logger.info(f"   📊 Signal processing: {signal_results.get('time_ms', 0):.1f}ms")
                logger.info(f"      Signals/sec: {signal_results.get('signals_per_second', 0):.1f}")
                
                # Dashboard optimization results
                dashboard_results = benchmark_results.get('dashboard_optimization', {})
                logger.info(f"   📱 Dashboard optimization: {dashboard_results.get('time_ms', 0):.1f}ms")
                logger.info(f"      Data size: {dashboard_results.get('data_size_bytes', 0)} bytes")
                
                # Memory optimization results
                memory_results = benchmark_results.get('memory_optimization', {})
                logger.info(f"   💾 Memory freed: {memory_results.get('memory_freed_mb', 0):.1f}MB")
                
                # Overall results
                overall_results = benchmark_results.get('overall', {})
                logger.info(f"   🏆 Performance score: {overall_results.get('performance_score', 0)}/100")
                logger.info(f"   ⏱️ Benchmark duration: {overall_results.get('benchmark_duration_ms', 0):.1f}ms")
            else:
                logger.warning(f"⚠️ Benchmark failed: {benchmark_results['error']}")
                
        except Exception as e:
            logger.error(f"❌ Performance benchmark test failed: {e}")
        
        # Test 8: Test performance summary
        logger.info("\n🎯 Test 8: Test performance summary")
        logger.info("="*50)
        
        try:
            summary = optimizer.get_performance_summary()
            
            if 'error' not in summary:
                logger.info("✅ Performance summary:")
                
                metrics = summary.get('performance_metrics', {})
                logger.info(f"   📊 Avg signal processing: {metrics.get('avg_signal_processing_ms', 0):.1f}ms")
                logger.info(f"   📱 Avg dashboard response: {metrics.get('avg_dashboard_response_ms', 0):.1f}ms")
                logger.info(f"   💾 Avg memory usage: {metrics.get('avg_memory_usage_mb', 0):.1f}MB")
                logger.info(f"   🎯 Avg cache hit rate: {metrics.get('avg_cache_hit_rate_percent', 0):.1f}%")
                
                status = summary.get('optimization_status', {})
                logger.info(f"   ⚙️ Cache entries: {status.get('cache_entries', 0)}")
                logger.info(f"   📦 Batch queue size: {status.get('batch_queue_size', 0)}")
                
                health = summary.get('system_health', {})
                logger.info(f"   🏥 Performance score: {health.get('performance_score', 0)}/100")
                logger.info(f"   💾 Memory pressure: {health.get('memory_pressure', 'UNKNOWN')}")
                logger.info(f"   💻 CPU pressure: {health.get('cpu_pressure', 'UNKNOWN')}")
            else:
                logger.warning(f"⚠️ Performance summary failed: {summary['error']}")
                
        except Exception as e:
            logger.error(f"❌ Performance summary test failed: {e}")
        
        # Final Results
        logger.info("\n" + "="*60)
        logger.info("🏆 PERFORMANCE OPTIMIZER TEST RESULTS")
        logger.info("="*60)
        
        test_results = {
            'initialization': True,
            'component_integration': True,
            'signal_optimization': len(optimizer.signal_cache) > 0,
            'dashboard_optimization': len(optimizer.dashboard_cache) > 0,
            'batch_processing': len(optimizer.signal_batch_queue) == 0,  # Should be processed
            'performance_monitoring': len(optimizer.metrics_history) > 0,
            'benchmark_execution': 'error' not in benchmark_results if 'benchmark_results' in locals() else False,
            'summary_generation': 'error' not in summary if 'summary' in locals() else False
        }
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        logger.info("✅ TEST RESULTS:")
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        logger.info(f"\n📊 Overall Score: {passed_tests}/{total_tests} tests passed")
        logger.info(f"🎯 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 ALL PERFORMANCE OPTIMIZER TESTS PASSED!")
            logger.info("✅ Phase 10.3 - Performance Optimization: COMPLETE")
        elif passed_tests >= total_tests * 0.8:
            logger.info("\n✅ PERFORMANCE OPTIMIZER MOSTLY WORKING!")
            logger.info("⚠️ Some minor issues but core functionality operational")
        else:
            logger.warning("\n⚠️ PERFORMANCE OPTIMIZER NEEDS ATTENTION")
            logger.info("🔧 Check the failed tests above for specific issues")
        
        logger.info("\n🎯 What's Now Working:")
        logger.info("   ⚡ Signal processing optimization with caching")
        logger.info("   📱 Dashboard data optimization and caching")
        logger.info("   📦 Batch processing for improved throughput")
        logger.info("   🧵 Thread pool optimization for CPU-intensive tasks")
        logger.info("   💾 Memory optimization with garbage collection")
        logger.info("   📊 Real-time performance monitoring")
        logger.info("   🏃 Comprehensive performance benchmarking")
        logger.info("   📈 Performance summary and health scoring")
        logger.info("   🧹 Automatic cache cleanup and management")
        
        logger.info("\n🚀 Ready for Phase 10.4: Security Hardening!")
        
    except Exception as e:
        logger.error(f"❌ Performance Optimizer test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_performance_optimizer())
