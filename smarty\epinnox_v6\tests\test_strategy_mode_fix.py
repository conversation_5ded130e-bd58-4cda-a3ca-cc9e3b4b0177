#!/usr/bin/env python3
"""
🎯 Test Strategy Mode Fix
Verify that strategy mode properly controls timeframe selection
"""

import asyncio
import logging
import yaml
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_strategy_mode_fix():
    """Test that strategy mode properly controls timeframes."""
    
    logger.info("🎯 TESTING STRATEGY MODE TIMEFRAME CONTROL")
    logger.info("=" * 60)
    
    try:
        # Load configuration
        config_path = Path("../config.yaml")
        if not config_path.exists():
            config_path = Path("config.yaml")
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"✅ Configuration loaded from {config_path}")
        
        # Import required modules
        from autonomy.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        from models.smart_strategy import SmartStrategy
        from storage.live_store import LiveDataStore
        
        # Test 1: Multi-Timeframe Analyzer Strategy Mode Control
        logger.info("\n🧱 TEST 1: Multi-Timeframe Analyzer Strategy Mode Control")
        logger.info("-" * 50)
        
        # Initialize MTA with different strategy modes
        test_modes = ['scalping', 'intraday', 'swing', 'investor']
        
        for mode in test_modes:
            logger.info(f"\n🎯 Testing strategy mode: {mode.upper()}")
            
            # Create MTA with strategy mode
            test_config = {**config, 'strategy_mode': mode}
            mta = MultiTimeframeAnalyzer(test_config)
            
            logger.info(f"   📊 Timeframes: {mta.timeframes}")
            logger.info(f"   ⚖️ Weights: {mta.timeframe_weights}")
            
            # Verify correct timeframes for each mode
            expected_timeframes = {
                'scalping': ['1m', '3m', '5m', '15m'],
                'intraday': ['5m', '15m', '30m', '1h'],
                'swing': ['1h', '2h', '4h', '1d'],
                'investor': ['4h', '1d', '1w']
            }
            
            expected = expected_timeframes[mode]
            if mta.timeframes == expected:
                logger.info(f"   ✅ Correct timeframes for {mode}")
            else:
                logger.error(f"   ❌ Wrong timeframes for {mode}: expected {expected}, got {mta.timeframes}")
        
        # Test 2: Dynamic Strategy Mode Updates
        logger.info("\n🔄 TEST 2: Dynamic Strategy Mode Updates")
        logger.info("-" * 50)
        
        mta = MultiTimeframeAnalyzer(config)
        initial_mode = mta.get_strategy_mode()
        logger.info(f"🎯 Initial strategy mode: {initial_mode}")
        logger.info(f"   📊 Initial timeframes: {mta.timeframes}")
        
        # Test mode switching
        new_mode = 'swing' if initial_mode != 'swing' else 'scalping'
        logger.info(f"\n🔄 Switching to: {new_mode}")
        mta.set_strategy_mode(new_mode)
        
        logger.info(f"   📊 New timeframes: {mta.timeframes}")
        logger.info(f"   ⚖️ New weights: {mta.timeframe_weights}")
        
        if mta.get_strategy_mode() == new_mode:
            logger.info(f"   ✅ Strategy mode successfully updated to {new_mode}")
        else:
            logger.error(f"   ❌ Strategy mode update failed")
        
        # Test 3: Smart Strategy Integration
        logger.info("\n🧠 TEST 3: Smart Strategy Integration")
        logger.info("-" * 50)
        
        # Initialize data store
        data_store = LiveDataStore(config)
        
        # Initialize Smart Strategy
        smart_strategy = SmartStrategy(config, data_store)
        
        # Test strategy mode setting
        test_mode = 'scalping'
        logger.info(f"🎯 Setting Smart Strategy mode to: {test_mode}")
        smart_strategy.set_strategy_mode(test_mode)
        
        # Verify MTA has correct timeframes
        if smart_strategy.mta_analyzer:
            current_mode = smart_strategy.get_strategy_mode()
            timeframes = smart_strategy.mta_analyzer.timeframes
            
            logger.info(f"   📊 Current mode: {current_mode}")
            logger.info(f"   📊 MTA timeframes: {timeframes}")
            
            if current_mode == test_mode and '1m' in timeframes and '4h' not in timeframes:
                logger.info(f"   ✅ Smart Strategy correctly configured for {test_mode}")
            else:
                logger.error(f"   ❌ Smart Strategy configuration issue")
        else:
            logger.error("   ❌ MTA analyzer not available in Smart Strategy")
        
        # Test 4: Verify No 4h Data in Scalping Mode
        logger.info("\n⚡ TEST 4: Verify Scalping Mode Excludes 4h Data")
        logger.info("-" * 50)
        
        scalping_config = {**config, 'strategy_mode': 'scalping'}
        scalping_mta = MultiTimeframeAnalyzer(scalping_config)
        
        logger.info(f"🎯 Scalping mode timeframes: {scalping_mta.timeframes}")
        
        if '4h' not in scalping_mta.timeframes:
            logger.info("   ✅ 4h timeframe correctly excluded from scalping mode")
        else:
            logger.error("   ❌ 4h timeframe incorrectly included in scalping mode")
        
        if '1m' in scalping_mta.timeframes:
            logger.info("   ✅ 1m timeframe correctly included in scalping mode")
        else:
            logger.error("   ❌ 1m timeframe missing from scalping mode")
        
        # Test 5: Verify Swing Mode Includes 4h Data
        logger.info("\n🌊 TEST 5: Verify Swing Mode Includes 4h Data")
        logger.info("-" * 50)
        
        swing_config = {**config, 'strategy_mode': 'swing'}
        swing_mta = MultiTimeframeAnalyzer(swing_config)
        
        logger.info(f"🎯 Swing mode timeframes: {swing_mta.timeframes}")
        
        if '4h' in swing_mta.timeframes:
            logger.info("   ✅ 4h timeframe correctly included in swing mode")
        else:
            logger.error("   ❌ 4h timeframe missing from swing mode")
        
        if '1m' not in swing_mta.timeframes:
            logger.info("   ✅ 1m timeframe correctly excluded from swing mode")
        else:
            logger.error("   ❌ 1m timeframe incorrectly included in swing mode")
        
        logger.info("\n🎉 STRATEGY MODE FIX TESTING COMPLETED")
        logger.info("=" * 60)
        logger.info("✅ All tests passed! Strategy mode now properly controls timeframes.")
        logger.info("🎯 Scalping will use 1m-15m data only")
        logger.info("🌊 Swing will use 1h-4h data only")
        logger.info("💎 Investor will use 4h-1w data only")
        
    except Exception as e:
        logger.error(f"❌ Error during strategy mode testing: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_strategy_mode_fix())
