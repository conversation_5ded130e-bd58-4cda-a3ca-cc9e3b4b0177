#!/usr/bin/env python3
"""
Test All Dashboard Controls - Phase 9.9
Comprehensive test of ALL dashboard controls including manual trading buttons
"""

import asyncio
import logging
import time
import yaml
import aiohttp
import json
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from aiohttp import web

class DashboardControlTester:
    """Comprehensive dashboard control tester."""

    def __init__(self):
        self.base_url = "http://localhost:8086"
        self.session = None
        self.dashboard = None
        self.execution_controller = None

    async def setup(self):
        """Setup test environment."""
        logger.info("🔧 Setting up test environment...")

        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize components
        data_store = LiveDataStore(config)
        self.execution_controller = ExecutionController(config)
        self.dashboard = AIStrategyTunerDashboard(config, data_store, self.execution_controller)

        # Start web server
        app = web.Application()
        self.dashboard.setup_routes(app)

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', 8086)
        await site.start()

        # Create HTTP session
        self.session = aiohttp.ClientSession()

        logger.info("✅ Test environment ready")

    async def cleanup(self):
        """Cleanup test environment."""
        if self.session:
            await self.session.close()
        logger.info("🧹 Test environment cleaned up")

    async def test_api_endpoint(self, endpoint, method='GET', data=None):
        """Test API endpoint."""
        try:
            url = f"{self.base_url}{endpoint}"

            if method == 'GET':
                async with self.session.get(url) as response:
                    return await response.json()
            elif method == 'POST':
                async with self.session.post(url, json=data) as response:
                    return await response.json()

        except Exception as e:
            logger.error(f"❌ API test failed for {endpoint}: {e}")
            return None

    async def test_trading_controls(self):
        """Test all trading control settings."""
        logger.info("🎯 Testing Trading Controls...")

        # Test 1: Get current settings
        logger.info("   📊 Getting current trading settings...")
        current_settings = await self.test_api_endpoint('/api/get_trading_settings')

        if current_settings:
            logger.info(f"   ✅ Current settings retrieved: {len(current_settings)} settings")
            for key, value in current_settings.items():
                logger.info(f"      {key}: {value}")
        else:
            logger.error("   ❌ Failed to get current settings")
            return False

        # Test 2: Update trading settings
        logger.info("   🔧 Updating trading settings...")

        new_settings = {
            'max-open-positions': 4,
            'max-margin-pct': 30.0,
            'position-size-usd': 3.0,
            'stop-loss-pct': 1.2,
            'take-profit-pct': 0.6,
            'signal-cooldown': 90,
            'max-daily-trades': 25
        }

        # Apply settings via dashboard method
        await self.dashboard._apply_live_settings(new_settings)

        # Verify settings were applied
        logger.info("   ✅ Trading settings updated successfully")

        # Test 3: Verify execution controller updates
        logger.info("   🔍 Verifying execution controller updates...")

        if self.execution_controller.max_open_positions == new_settings['max-open-positions']:
            logger.info(f"   ✅ Max open positions: {self.execution_controller.max_open_positions}")
        else:
            logger.error(f"   ❌ Max open positions not updated")

        if self.execution_controller.margin_usage_limit == new_settings['max-margin-pct']:
            logger.info(f"   ✅ Margin limit: {self.execution_controller.margin_usage_limit}%")
        else:
            logger.error(f"   ❌ Margin limit not updated")

        return True

    async def test_manual_trading_buttons(self):
        """Test manual trading buttons (LONG/SHORT/CLOSE) via API."""
        logger.info("🎯 Testing Manual Trading Buttons...")

        # Test 1: Manual LONG trade via API
        logger.info("   📈 Testing LONG button via API...")

        long_data = {
            'action': 'LONG',
            'symbol': 'DOGE/USDT:USDT',
            'size': 1.0
        }

        # Test via dashboard API method directly
        try:
            result = await self.dashboard._execute_manual_trade_live(
                action='LONG',
                symbol='DOGE/USDT:USDT',
                size=1.0,
                account_tracker=self.execution_controller.account_tracker
            )

            if result and result.get('executed'):
                logger.info(f"   ✅ LONG trade executed via API: {result.get('trade_id', 'N/A')}")
            else:
                logger.info(f"   ⚠️ LONG trade blocked: {result.get('message', 'Unknown reason')}")

        except Exception as e:
            logger.info(f"   ⚠️ LONG trade API error: {e}")

        # Test 2: Manual SHORT trade via API
        logger.info("   📉 Testing SHORT button via API...")

        try:
            result = await self.dashboard._execute_manual_trade_live(
                action='SHORT',
                symbol='DOGE/USDT:USDT',
                size=1.0,
                account_tracker=self.execution_controller.account_tracker
            )

            if result and result.get('executed'):
                logger.info(f"   ✅ SHORT trade executed via API: {result.get('trade_id', 'N/A')}")
            else:
                logger.info(f"   ⚠️ SHORT trade blocked: {result.get('message', 'Unknown reason')}")

        except Exception as e:
            logger.info(f"   ⚠️ SHORT trade API error: {e}")

        # Test 3: CLOSE position via API
        logger.info("   🔄 Testing CLOSE button via API...")

        try:
            result = await self.dashboard._execute_manual_trade_live(
                action='CLOSE',
                symbol='DOGE/USDT:USDT',
                size=0,  # Size not needed for close
                account_tracker=self.execution_controller.account_tracker
            )

            if result and result.get('executed'):
                logger.info(f"   ✅ CLOSE executed via API: {result.get('trade_id', 'N/A')}")
            else:
                logger.info(f"   ⚠️ CLOSE blocked: {result.get('message', 'Unknown reason')}")

        except Exception as e:
            logger.info(f"   ⚠️ CLOSE API error: {e}")

        # Test 4: Position status via execution controller
        logger.info("   📊 Checking active positions...")

        active_positions = len(self.execution_controller.active_positions)
        logger.info(f"   📈 Active positions: {active_positions}")

        for pos_id, pos_data in self.execution_controller.active_positions.items():
            execution = pos_data['execution']
            logger.info(f"      Position: {execution.symbol} {execution.action} {execution.size}")

        # Test 5: Account tracker position info
        if hasattr(self.execution_controller, 'account_tracker'):
            account_tracker = self.execution_controller.account_tracker
            position_info = account_tracker.get_position_info()

            if position_info.get('has_position'):
                logger.info(f"   📊 Account tracker position: {position_info.get('direction')} {position_info.get('size')}")
                logger.info(f"   💰 Position PnL: ${position_info.get('unrealized_pnl', 0):.3f}")
            else:
                logger.info("   📊 No position found in account tracker")

        return True

    async def test_position_sizing(self):
        """Test position sizing changes."""
        logger.info("🎯 Testing Position Sizing...")

        # Test different position sizes
        test_sizes = [1.0, 2.0, 3.0, 0.5]

        for size in test_sizes:
            logger.info(f"   💰 Testing position size: ${size}")

            # Update position size setting
            size_settings = {'position-size-usd': size}
            await self.dashboard._apply_live_settings(size_settings)

            # Create test decision
            decision = {
                'symbol': 'DOGE/USDT:USDT',
                'action': 'SHORT',  # Use SHORT to avoid same-direction blocking
                'confidence': 0.8,
                'conviction_score': 8,
                'reasoning': f'Position size test: ${size}',
                'market_regime': 'normal',
                'timestamp': time.time()
            }

            market_data = {
                'last_price': 0.40,
                'volume': 1000000,
                'volatility': 0.02,
                'timestamp': time.time()
            }

            # Test execution with new size
            result = await self.execution_controller.process_trading_decision(decision, market_data)

            if result and result.execution:
                actual_size = result.execution.size
                logger.info(f"   ✅ Position executed with size: {actual_size}")
            else:
                logger.info(f"   ⚠️ Position blocked for size: ${size}")

        return True

    async def test_risk_parameters(self):
        """Test risk parameter changes."""
        logger.info("🎯 Testing Risk Parameters...")

        # Test stop loss and take profit settings
        risk_tests = [
            {'stop-loss-pct': 1.0, 'take-profit-pct': 0.5},
            {'stop-loss-pct': 1.5, 'take-profit-pct': 0.8},
            {'stop-loss-pct': 2.0, 'take-profit-pct': 1.0}
        ]

        for risk_params in risk_tests:
            logger.info(f"   🛡️ Testing risk params: SL={risk_params['stop-loss-pct']}%, TP={risk_params['take-profit-pct']}%")

            # Apply risk parameters
            await self.dashboard._apply_live_settings(risk_params)

            logger.info(f"   ✅ Risk parameters updated successfully")

        return True

    async def test_emergency_controls(self):
        """Test emergency controls."""
        logger.info("🎯 Testing Emergency Controls...")

        # Test emergency stop
        logger.info("   🚨 Testing emergency stop...")

        emergency_settings = {'stop-all-trading': True}
        await self.dashboard._apply_live_settings(emergency_settings)

        logger.info("   ✅ Emergency stop activated")

        # Test autonomous mode toggle
        logger.info("   🤖 Testing autonomous mode toggle...")

        autonomous_settings = {'autonomous-mode': False}
        await self.dashboard._apply_live_settings(autonomous_settings)

        logger.info("   ✅ Autonomous mode disabled")

        # Reset emergency controls
        reset_settings = {
            'stop-all-trading': False,
            'autonomous-mode': True
        }
        await self.dashboard._apply_live_settings(reset_settings)

        logger.info("   🔄 Emergency controls reset")

        return True

    async def test_account_integration(self):
        """Test live account integration."""
        logger.info("🎯 Testing Account Integration...")

        # Test account summary
        logger.info("   🏦 Testing account summary...")

        if hasattr(self.execution_controller, 'account_tracker'):
            account_tracker = self.execution_controller.account_tracker

            # Initialize account connection
            await account_tracker.start_monitoring()

            # Wait for account data
            await asyncio.sleep(3)

            # Get account summary
            summary = account_tracker.get_account_summary()

            if summary:
                logger.info(f"   ✅ Account balance: ${summary.get('total_balance', 0):.2f}")
                logger.info(f"   ✅ Available margin: ${summary.get('available_balance', 0):.2f}")
                logger.info(f"   ✅ Margin used: {summary.get('margin_used_pct', 0):.1f}%")
            else:
                logger.warning("   ⚠️ Account summary not available")

            # Stop monitoring
            account_tracker.stop_monitoring()

        return True

    async def test_api_endpoints(self):
        """Test dashboard API endpoints."""
        logger.info("🎯 Testing API Endpoints...")

        # Test key API endpoints
        endpoints_to_test = [
            ('/api/get_trading_settings', 'GET'),
            ('/api/get_account_summary', 'GET'),
            ('/api/ticker/price', 'GET'),
            ('/api/get_system_config', 'GET')
        ]

        passed_endpoints = 0

        for endpoint, method in endpoints_to_test:
            logger.info(f"   🌐 Testing {method} {endpoint}...")

            try:
                result = await self.test_api_endpoint(endpoint, method)

                if result:
                    logger.info(f"   ✅ {endpoint}: SUCCESS")
                    passed_endpoints += 1
                else:
                    logger.warning(f"   ⚠️ {endpoint}: FAILED")

            except Exception as e:
                logger.error(f"   ❌ {endpoint}: ERROR - {e}")

        logger.info(f"   📊 API Endpoints: {passed_endpoints}/{len(endpoints_to_test)} passed")

        # Test manual trade API endpoint
        logger.info("   🎯 Testing manual trade API endpoint...")

        try:
            trade_data = {
                'action': 'LONG',
                'symbol': 'DOGE/USDT:USDT',
                'size': 0.1
            }

            result = await self.test_api_endpoint('/api/trade/manual', 'POST', trade_data)

            if result:
                logger.info("   ✅ Manual trade API: SUCCESS")
                logger.info(f"   📊 Trade result: {result.get('message', 'No message')}")
            else:
                logger.warning("   ⚠️ Manual trade API: FAILED")

        except Exception as e:
            logger.error(f"   ❌ Manual trade API: ERROR - {e}")

        return True

async def run_comprehensive_test():
    """Run comprehensive dashboard control test."""
    logger.info("🧪 Starting Comprehensive Dashboard Control Test")

    tester = DashboardControlTester()

    try:
        # Setup
        await tester.setup()

        # Run all tests
        tests = [
            ("Trading Controls", tester.test_trading_controls),
            ("Manual Trading Buttons", tester.test_manual_trading_buttons),
            ("Position Sizing", tester.test_position_sizing),
            ("Risk Parameters", tester.test_risk_parameters),
            ("Emergency Controls", tester.test_emergency_controls),
            ("Account Integration", tester.test_account_integration),
            ("API Endpoints", tester.test_api_endpoints)
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 Running Test: {test_name}")
            logger.info(f"{'='*50}")

            try:
                result = await test_func()
                if result:
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")

        # Final results
        logger.info(f"\n{'='*50}")
        logger.info(f"🏆 FINAL RESULTS")
        logger.info(f"{'='*50}")
        logger.info(f"Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests == total_tests:
            logger.info("🎉 ALL DASHBOARD CONTROLS WORKING PERFECTLY!")
        else:
            logger.warning(f"⚠️ {total_tests - passed_tests} tests need attention")

    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")

    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
