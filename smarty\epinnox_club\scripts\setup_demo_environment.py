#!/usr/bin/env python3
"""
Master setup script for Money Circle demo environment.
Creates database tables, seeds demo data, and verifies the complete setup.
"""

import subprocess
import sys
import time
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_script(script_path, description):
    """Run a Python script and return success status."""
    try:
        logger.info(f"🚀 Running {description}...")
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            print(result.stdout)
            return True
        else:
            logger.error(f"❌ {description} failed")
            print(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"❌ Error running {description}: {e}")
        return False

def check_server_running():
    """Check if Money Circle server is running."""
    try:
        import requests
        response = requests.get('http://localhost:8084', timeout=5)
        return response.status_code == 200 or response.status_code == 302
    except:
        return False

def main():
    """Run the complete demo environment setup."""
    print("🎯 MONEY CIRCLE DEMO ENVIRONMENT SETUP")
    print("=" * 60)
    print("This script will:")
    print("1. Create all required database tables")
    print("2. Seed comprehensive demo data")
    print("3. Verify the demo environment")
    print("4. Test all dashboard features")
    print("=" * 60)
    
    # Check if server is running
    if not check_server_running():
        print("\n⚠️ WARNING: Money Circle server is not running!")
        print("Please start the server first:")
        print("   cd epinnox_club")
        print("   python start_money_circle.py")
        print("\nThen run this setup script again.")
        return 1
    
    print("✅ Money Circle server is running")
    
    # Step 1: Create database tables
    print("\n📝 STEP 1: Creating Database Tables")
    print("-" * 40)
    if not run_script("database/create_demo_tables.py", "Database table creation"):
        print("❌ Failed to create database tables")
        return 1
    
    # Step 2: Seed demo data
    print("\n🌱 STEP 2: Seeding Demo Data")
    print("-" * 40)
    if not run_script("database/seed_demo_data.py", "Demo data seeding"):
        print("❌ Failed to seed demo data")
        return 1
    
    # Step 3: Wait a moment for data to settle
    print("\n⏳ Waiting for data to settle...")
    time.sleep(2)
    
    # Step 4: Test demo environment
    print("\n🧪 STEP 3: Testing Demo Environment")
    print("-" * 40)
    if not run_script("test_demo_data.py", "Demo environment testing"):
        print("⚠️ Some demo features may need attention")
        # Don't fail here as partial functionality is acceptable
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 MONEY CIRCLE DEMO ENVIRONMENT SETUP COMPLETE!")
    print("=" * 60)
    
    print("\n📊 DEMO DATA SUMMARY:")
    print("✅ 18 diverse member accounts created")
    print("   - Usernames: trader_alex, crypto_sarah, quant_mike, etc.")
    print("   - Password: securepass123 (for all demo accounts)")
    print("   - Roles: Members, experienced traders, and admins")
    
    print("\n✅ 6 months of trading performance data")
    print("   - Daily returns, portfolio values, win rates")
    print("   - Realistic performance patterns and volatility")
    print("   - Portfolio values from $1,000 to $50,000")
    
    print("\n✅ 10 comprehensive trading strategies")
    print("   - Categories: Day trading, swing trading, crypto, forex, options")
    print("   - Performance history and follower relationships")
    print("   - Realistic risk levels and investment minimums")
    
    print("\n✅ Social features and analytics")
    print("   - Member connections and following relationships")
    print("   - Achievements and badges for members")
    print("   - Club-wide performance aggregations")
    
    print("\n🔐 DEMO LOGIN CREDENTIALS:")
    print("   Username: Any demo username (e.g., trader_alex)")
    print("   Password: securepass123")
    
    print("\n🌐 PLATFORM ACCESS:")
    print("   Main Platform: http://localhost:8084")
    print("   Personal Dashboard: http://localhost:8084/dashboard")
    print("   Strategy Marketplace: http://localhost:8084/club/strategies")
    print("   Member Directory: http://localhost:8084/club/members")
    print("   Club Analytics: http://localhost:8084/club/analytics")
    
    print("\n🎯 DEMO FEATURES AVAILABLE:")
    print("   📊 Personal Dashboard - Portfolio overview, performance charts")
    print("   🎯 Strategy Marketplace - Browse and follow trading strategies")
    print("   👥 Member Directory - Connect with other traders")
    print("   📈 Club Analytics - Comprehensive performance analytics")
    print("   🤝 Social Features - Follow members, view achievements")
    print("   📱 Responsive Design - Works on desktop and mobile")
    
    print("\n🚀 READY FOR DEMONSTRATION!")
    print("The Money Circle platform now contains rich, realistic demo data")
    print("suitable for showcasing to potential users or investors.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
