#!/usr/bin/env python3
"""
Multi-Symbol Autonomous Trader - Phase 3
Enhanced autonomous trading logic that manages positions across multiple symbols
simultaneously with intelligent risk allocation and dynamic symbol switching.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

from .market_scanner import MultiSymbolMarketScanner, MarketOpportunity
from .symbol_selector import DynamicSymbolSelector, SymbolScore

logger = logging.getLogger(__name__)

@dataclass
class MultiSymbolPosition:
    """Position information for multi-symbol trading."""
    symbol: str
    side: str  # 'long', 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    stop_loss: float
    take_profit: float
    entry_time: float
    max_hold_time: int
    confidence: float
    reasoning: str

@dataclass
class PortfolioMetrics:
    """Portfolio-level metrics."""
    total_capital: float
    allocated_capital: float
    available_capital: float
    total_positions: int
    total_pnl: float
    daily_pnl: float
    win_rate: float
    sharpe_ratio: float
    max_drawdown: float
    risk_utilization: float
    diversification_score: float
    performance_score: float

class MultiSymbolAutonomousTrader:
    """
    Enhanced autonomous trader that manages multiple symbols simultaneously.
    Integrates with the market scanner and symbol selector for optimal trading.
    """

    def __init__(self, config: Dict[str, Any], market_scanner: MultiSymbolMarketScanner,
                 symbol_selector: DynamicSymbolSelector, execution_controller=None):
        self.config = config
        self.market_scanner = market_scanner
        self.symbol_selector = symbol_selector
        self.execution_controller = execution_controller
        
        # Multi-symbol trading configuration
        self.max_concurrent_symbols = config.get('multi_symbol_trader', {}).get('max_symbols', 5)
        self.max_positions_per_symbol = config.get('multi_symbol_trader', {}).get('max_positions_per_symbol', 2)
        self.total_capital = config.get('multi_symbol_trader', {}).get('total_capital', 1000.0)
        self.max_risk_per_symbol = config.get('multi_symbol_trader', {}).get('max_risk_per_symbol', 0.05)  # 5%
        self.max_portfolio_risk = config.get('multi_symbol_trader', {}).get('max_portfolio_risk', 0.15)  # 15%
        
        # Position management
        self.active_positions: Dict[str, List[MultiSymbolPosition]] = {}
        self.symbol_allocations: Dict[str, float] = {}
        self.portfolio_metrics = PortfolioMetrics(
            total_capital=self.total_capital,
            allocated_capital=0.0,
            available_capital=self.total_capital,
            total_positions=0,
            total_pnl=0.0,
            daily_pnl=0.0,
            win_rate=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            risk_utilization=0.0,
            diversification_score=0.0,
            performance_score=0.0
        )
        
        # Trading state
        self.enabled = config.get('multi_symbol_trader', {}).get('enabled', True)
        self.last_rebalance_time = 0
        self.rebalance_interval = 300  # 5 minutes
        
        # Performance tracking
        self.trade_history: List[Dict[str, Any]] = []
        self.daily_stats = {
            'trades_executed': 0,
            'symbols_traded': set(),
            'total_pnl': 0.0,
            'best_trade': 0.0,
            'worst_trade': 0.0,
            'avg_hold_time': 0.0
        }
        
        self.running = False
        self.trading_task = None
        
        logger.info(f"🎯 Multi-Symbol Autonomous Trader initialized")
        logger.info(f"   💰 Total capital: ${self.total_capital:,.2f}")
        logger.info(f"   📊 Max symbols: {self.max_concurrent_symbols}")
        logger.info(f"   ⚖️ Max risk per symbol: {self.max_risk_per_symbol:.1%}")

    async def start_trading(self):
        """Start multi-symbol autonomous trading."""
        if self.running:
            logger.warning("Multi-symbol trader is already running")
            return
            
        if not self.enabled:
            logger.warning("Multi-symbol trader is disabled")
            return
            
        self.running = True
        self.trading_task = asyncio.create_task(self._trading_loop())
        logger.info("🚀 Multi-symbol autonomous trading started")

    async def stop_trading(self):
        """Stop multi-symbol autonomous trading."""
        self.running = False
        if self.trading_task:
            self.trading_task.cancel()
            try:
                await self.trading_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Multi-symbol autonomous trading stopped")

    async def _trading_loop(self):
        """Main multi-symbol trading loop."""
        while self.running:
            try:
                # Update portfolio metrics
                await self._update_portfolio_metrics()
                
                # Get market opportunities from scanner
                opportunities = self.market_scanner.get_market_opportunities(20)
                
                # Get symbol scores from selector
                symbol_scores = self.symbol_selector.get_symbol_scores()
                
                # Evaluate trading opportunities
                if opportunities and symbol_scores:
                    await self._evaluate_trading_opportunities(opportunities, symbol_scores)
                
                # Manage existing positions
                await self._manage_existing_positions()
                
                # Portfolio rebalancing
                if time.time() - self.last_rebalance_time > self.rebalance_interval:
                    await self._rebalance_portfolio()
                    self.last_rebalance_time = time.time()
                
                # Risk management
                await self._perform_risk_management()
                
                # Wait for next cycle
                await asyncio.sleep(15)  # 15-second cycle
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in multi-symbol trading loop: {e}")
                await asyncio.sleep(10)

    async def _update_portfolio_metrics(self):
        """Update portfolio-level metrics."""
        try:
            # Calculate allocated capital
            allocated_capital = 0.0
            total_positions = 0
            total_pnl = 0.0
            
            for symbol, positions in self.active_positions.items():
                for position in positions:
                    allocated_capital += position.size
                    total_positions += 1
                    total_pnl += position.unrealized_pnl
            
            # Update metrics
            self.portfolio_metrics.allocated_capital = allocated_capital
            self.portfolio_metrics.available_capital = self.total_capital - allocated_capital
            self.portfolio_metrics.total_positions = total_positions
            self.portfolio_metrics.total_pnl = total_pnl
            self.portfolio_metrics.risk_utilization = allocated_capital / self.total_capital
            
            # Calculate diversification score
            active_symbols = len([s for s, positions in self.active_positions.items() if positions])
            self.portfolio_metrics.diversification_score = min(active_symbols / self.max_concurrent_symbols, 1.0)
            
            # Calculate performance score
            self.portfolio_metrics.performance_score = await self._calculate_performance_score()
            
        except Exception as e:
            logger.error(f"❌ Error updating portfolio metrics: {e}")

    async def _evaluate_trading_opportunities(self, opportunities: List[MarketOpportunity],
                                            symbol_scores: Dict[str, SymbolScore]):
        """Evaluate and potentially act on trading opportunities."""
        try:
            # Filter opportunities by available capital and risk limits
            viable_opportunities = []
            
            for opportunity in opportunities:
                symbol = opportunity.symbol
                
                # Check if we can trade this symbol
                if not await self._can_trade_symbol(symbol):
                    continue
                
                # Check symbol score
                if symbol not in symbol_scores or symbol_scores[symbol].confidence < 0.6:
                    continue
                
                # Check risk limits
                if not await self._check_symbol_risk_limits(symbol):
                    continue
                
                viable_opportunities.append((opportunity, symbol_scores[symbol]))
            
            # Sort by combined score
            viable_opportunities.sort(
                key=lambda x: x[0].confidence * x[1].confidence * x[0].potential_return,
                reverse=True
            )
            
            # Execute top opportunities
            for opportunity, symbol_score in viable_opportunities[:3]:  # Top 3
                await self._execute_opportunity(opportunity, symbol_score)
                
        except Exception as e:
            logger.error(f"❌ Error evaluating trading opportunities: {e}")

    async def _can_trade_symbol(self, symbol: str) -> bool:
        """Check if we can trade a specific symbol."""
        # Check if we're already at max symbols
        active_symbols = len([s for s, positions in self.active_positions.items() if positions])
        if symbol not in self.active_positions and active_symbols >= self.max_concurrent_symbols:
            return False
        
        # Check positions per symbol limit
        current_positions = len(self.active_positions.get(symbol, []))
        if current_positions >= self.max_positions_per_symbol:
            return False
        
        return True

    async def _check_symbol_risk_limits(self, symbol: str) -> bool:
        """Check risk limits for a symbol."""
        # Calculate current symbol allocation
        current_allocation = self.symbol_allocations.get(symbol, 0.0)
        max_symbol_allocation = self.total_capital * self.max_risk_per_symbol
        
        if current_allocation >= max_symbol_allocation:
            return False
        
        # Check portfolio risk
        if self.portfolio_metrics.risk_utilization >= self.max_portfolio_risk:
            return False
        
        return True

    async def _execute_opportunity(self, opportunity: MarketOpportunity, symbol_score: SymbolScore):
        """Execute a trading opportunity."""
        try:
            symbol = opportunity.symbol
            
            # Calculate position size
            position_size = await self._calculate_position_size(symbol, opportunity, symbol_score)
            if position_size <= 0:
                return
            
            # Determine side
            side = 'long' if opportunity.potential_return > 0 else 'short'
            
            # Create position
            position = MultiSymbolPosition(
                symbol=symbol,
                side=side,
                size=position_size,
                entry_price=opportunity.entry_price,
                current_price=opportunity.entry_price,
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                stop_loss=opportunity.stop_loss,
                take_profit=opportunity.target_price,
                entry_time=time.time(),
                max_hold_time=self._calculate_max_hold_time(opportunity),
                confidence=opportunity.confidence * symbol_score.confidence,
                reasoning=f"{opportunity.opportunity_type}: {symbol_score.reasoning}"
            )
            
            # Add to active positions
            if symbol not in self.active_positions:
                self.active_positions[symbol] = []
            self.active_positions[symbol].append(position)
            
            # Update allocations
            self.symbol_allocations[symbol] = self.symbol_allocations.get(symbol, 0.0) + position_size
            
            # Execute through execution controller if available
            if self.execution_controller:
                await self._execute_through_controller(position)
            
            # Update statistics
            self.daily_stats['trades_executed'] += 1
            self.daily_stats['symbols_traded'].add(symbol)
            
            logger.info(f"✅ Executed {side} position: {symbol} size: {position_size:.2f} "
                       f"@ ${opportunity.entry_price:.4f} (confidence: {position.confidence:.2%})")
            
        except Exception as e:
            logger.error(f"❌ Error executing opportunity for {opportunity.symbol}: {e}")

    async def _calculate_position_size(self, symbol: str, opportunity: MarketOpportunity,
                                     symbol_score: SymbolScore) -> float:
        """Calculate optimal position size for a symbol."""
        try:
            # Available capital for this symbol
            current_allocation = self.symbol_allocations.get(symbol, 0.0)
            max_allocation = self.total_capital * self.max_risk_per_symbol
            available_for_symbol = max_allocation - current_allocation
            
            # Available portfolio capital
            available_portfolio = self.portfolio_metrics.available_capital
            
            # Use the smaller of the two
            available_capital = min(available_for_symbol, available_portfolio)
            
            if available_capital <= 0:
                return 0.0
            
            # Risk-based sizing
            combined_confidence = opportunity.confidence * symbol_score.confidence
            risk_factor = combined_confidence * 0.5  # Max 50% of available capital
            
            # Volatility adjustment
            metrics = self.market_scanner.get_symbol_metrics(symbol)
            if metrics:
                vol_adjustment = 1.0 - min(metrics.volatility / 0.1, 0.5)
                risk_factor *= vol_adjustment
            
            position_size = available_capital * risk_factor
            
            # Minimum and maximum limits
            min_size = self.total_capital * 0.005  # 0.5% minimum
            max_size = available_capital * 0.8     # 80% of available
            
            return max(min_size, min(position_size, max_size))
            
        except Exception as e:
            logger.error(f"❌ Error calculating position size for {symbol}: {e}")
            return 0.0

    def _calculate_max_hold_time(self, opportunity: MarketOpportunity) -> int:
        """Calculate maximum hold time based on opportunity type."""
        base_times = {
            'momentum': 3600,    # 1 hour
            'volatility': 1800,  # 30 minutes
            'trend': 7200,       # 2 hours
            'reversal': 1800     # 30 minutes
        }
        
        return base_times.get(opportunity.opportunity_type, 3600)

    async def _manage_existing_positions(self):
        """Manage existing positions (stop loss, take profit, time exits)."""
        try:
            current_time = time.time()
            positions_to_close = []
            
            for symbol, positions in self.active_positions.items():
                # Get current price
                metrics = self.market_scanner.get_symbol_metrics(symbol)
                current_price = metrics.price if metrics else 0.0
                
                for i, position in enumerate(positions):
                    # Update current price and PnL
                    position.current_price = current_price
                    if position.side == 'long':
                        position.unrealized_pnl = (current_price - position.entry_price) * (position.size / position.entry_price)
                    else:
                        position.unrealized_pnl = (position.entry_price - current_price) * (position.size / position.entry_price)
                    
                    position.unrealized_pnl_pct = position.unrealized_pnl / position.size if position.size > 0 else 0
                    
                    # Check exit conditions
                    should_close = False
                    close_reason = ""
                    
                    # Time-based exit
                    if current_time - position.entry_time > position.max_hold_time:
                        should_close = True
                        close_reason = "max_hold_time"
                    
                    # Stop loss
                    elif ((position.side == 'long' and current_price <= position.stop_loss) or
                          (position.side == 'short' and current_price >= position.stop_loss)):
                        should_close = True
                        close_reason = "stop_loss"
                    
                    # Take profit
                    elif ((position.side == 'long' and current_price >= position.take_profit) or
                          (position.side == 'short' and current_price <= position.take_profit)):
                        should_close = True
                        close_reason = "take_profit"
                    
                    if should_close:
                        positions_to_close.append((symbol, i, close_reason))
            
            # Close positions
            for symbol, position_index, reason in reversed(positions_to_close):
                await self._close_position(symbol, position_index, reason)
                
        except Exception as e:
            logger.error(f"❌ Error managing existing positions: {e}")

    async def _close_position(self, symbol: str, position_index: int, reason: str):
        """Close a specific position."""
        try:
            if symbol not in self.active_positions or position_index >= len(self.active_positions[symbol]):
                return
            
            position = self.active_positions[symbol][position_index]
            
            # Update statistics
            self.daily_stats['total_pnl'] += position.unrealized_pnl
            self.daily_stats['best_trade'] = max(self.daily_stats['best_trade'], position.unrealized_pnl)
            self.daily_stats['worst_trade'] = min(self.daily_stats['worst_trade'], position.unrealized_pnl)
            
            # Calculate hold time
            hold_time = time.time() - position.entry_time
            if self.daily_stats['avg_hold_time'] == 0:
                self.daily_stats['avg_hold_time'] = hold_time
            else:
                self.daily_stats['avg_hold_time'] = (self.daily_stats['avg_hold_time'] + hold_time) / 2
            
            # Add to trade history
            self.trade_history.append({
                'symbol': symbol,
                'side': position.side,
                'size': position.size,
                'entry_price': position.entry_price,
                'exit_price': position.current_price,
                'pnl': position.unrealized_pnl,
                'pnl_pct': position.unrealized_pnl_pct,
                'hold_time': hold_time,
                'close_reason': reason,
                'confidence': position.confidence,
                'timestamp': time.time()
            })
            
            # Update allocations
            self.symbol_allocations[symbol] -= position.size
            if self.symbol_allocations[symbol] <= 0:
                del self.symbol_allocations[symbol]
            
            # Remove position
            del self.active_positions[symbol][position_index]
            if not self.active_positions[symbol]:
                del self.active_positions[symbol]
            
            logger.info(f"🔒 Closed {position.side} position: {symbol} "
                       f"PnL: ${position.unrealized_pnl:.2f} ({position.unrealized_pnl_pct:.2%}) "
                       f"Reason: {reason}")
            
        except Exception as e:
            logger.error(f"❌ Error closing position {symbol}[{position_index}]: {e}")

    async def _rebalance_portfolio(self):
        """Rebalance portfolio allocations."""
        try:
            # Get top symbols from selector
            top_symbols = self.symbol_selector.get_top_symbols(self.max_concurrent_symbols)
            
            # Check if we need to exit any symbols not in top list
            symbols_to_exit = []
            for symbol in self.active_positions.keys():
                if symbol not in top_symbols:
                    symbols_to_exit.append(symbol)
            
            # Close positions in symbols we're exiting
            for symbol in symbols_to_exit:
                positions = self.active_positions[symbol].copy()
                for i in range(len(positions)):
                    await self._close_position(symbol, 0, "rebalance")  # Always close index 0
            
            logger.info(f"🔄 Portfolio rebalanced: {len(symbols_to_exit)} symbols exited")
            
        except Exception as e:
            logger.error(f"❌ Error rebalancing portfolio: {e}")

    async def _perform_risk_management(self):
        """Perform portfolio-level risk management."""
        try:
            # Check portfolio risk limits
            if self.portfolio_metrics.risk_utilization > self.max_portfolio_risk * 1.1:  # 10% buffer
                logger.warning(f"⚠️ Portfolio risk exceeded: {self.portfolio_metrics.risk_utilization:.1%}")
                await self._reduce_portfolio_risk()
            
            # Check individual symbol risk
            for symbol, allocation in self.symbol_allocations.items():
                symbol_risk = allocation / self.total_capital
                if symbol_risk > self.max_risk_per_symbol * 1.1:  # 10% buffer
                    logger.warning(f"⚠️ Symbol risk exceeded for {symbol}: {symbol_risk:.1%}")
                    await self._reduce_symbol_risk(symbol)
            
        except Exception as e:
            logger.error(f"❌ Error in risk management: {e}")

    async def _reduce_portfolio_risk(self):
        """Reduce overall portfolio risk."""
        # Close lowest performing positions
        all_positions = []
        for symbol, positions in self.active_positions.items():
            for i, position in enumerate(positions):
                all_positions.append((symbol, i, position.unrealized_pnl_pct))
        
        # Sort by performance (worst first)
        all_positions.sort(key=lambda x: x[2])
        
        # Close worst 25% of positions
        positions_to_close = max(1, len(all_positions) // 4)
        for symbol, index, _ in all_positions[:positions_to_close]:
            await self._close_position(symbol, index, "risk_management")

    async def _reduce_symbol_risk(self, symbol: str):
        """Reduce risk for a specific symbol."""
        if symbol in self.active_positions:
            positions = self.active_positions[symbol]
            if positions:
                # Close the largest position
                largest_pos_index = max(range(len(positions)), key=lambda i: positions[i].size)
                await self._close_position(symbol, largest_pos_index, "symbol_risk_management")

    async def _execute_through_controller(self, position: MultiSymbolPosition):
        """Execute position through execution controller."""
        # Placeholder for execution controller integration
        logger.debug(f"🔄 Would execute through controller: {position.symbol} {position.side} {position.size}")

    async def _calculate_performance_score(self) -> float:
        """Calculate overall performance score."""
        try:
            if not self.trade_history:
                return 0.5
            
            # Calculate win rate
            winning_trades = len([t for t in self.trade_history[-50:] if t['pnl'] > 0])
            total_trades = len(self.trade_history[-50:])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Calculate average return
            avg_return = np.mean([t['pnl_pct'] for t in self.trade_history[-50:]]) if self.trade_history else 0
            
            # Calculate risk-adjusted return (simplified Sharpe)
            returns = [t['pnl_pct'] for t in self.trade_history[-50:]]
            if len(returns) > 1:
                sharpe = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            else:
                sharpe = 0
            
            # Combine metrics
            performance_score = (
                0.4 * win_rate +
                0.3 * min(max(avg_return * 10, -1), 1) +  # Normalize to -1 to 1
                0.3 * min(max(sharpe / 2, -1), 1)         # Normalize to -1 to 1
            )
            
            return max(0, min(1, performance_score + 0.5))  # Shift to 0-1 range
            
        except Exception as e:
            logger.error(f"❌ Error calculating performance score: {e}")
            return 0.5

    def get_portfolio_status(self) -> Dict[str, Any]:
        """Get current portfolio status."""
        return {
            'enabled': self.enabled,
            'running': self.running,
            'portfolio_metrics': {
                'total_capital': self.portfolio_metrics.total_capital,
                'allocated_capital': self.portfolio_metrics.allocated_capital,
                'available_capital': self.portfolio_metrics.available_capital,
                'total_positions': self.portfolio_metrics.total_positions,
                'total_pnl': self.portfolio_metrics.total_pnl,
                'risk_utilization': self.portfolio_metrics.risk_utilization,
                'diversification_score': self.portfolio_metrics.diversification_score,
                'performance_score': self.portfolio_metrics.performance_score
            },
            'active_symbols': list(self.active_positions.keys()),
            'daily_stats': {
                'trades_executed': self.daily_stats['trades_executed'],
                'symbols_traded': len(self.daily_stats['symbols_traded']),
                'total_pnl': self.daily_stats['total_pnl'],
                'best_trade': self.daily_stats['best_trade'],
                'worst_trade': self.daily_stats['worst_trade'],
                'avg_hold_time': self.daily_stats['avg_hold_time']
            }
        }

    def get_active_positions(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all active positions."""
        result = {}
        for symbol, positions in self.active_positions.items():
            result[symbol] = [
                {
                    'side': pos.side,
                    'size': pos.size,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_pct': pos.unrealized_pnl_pct,
                    'confidence': pos.confidence,
                    'hold_time': time.time() - pos.entry_time,
                    'reasoning': pos.reasoning
                }
                for pos in positions
            ]
        return result
