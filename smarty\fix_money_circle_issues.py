#!/usr/bin/env python3
"""
Money Circle Platform Issue Fix Script
Addresses critical issues identified in the log analysis.
"""

import os
import sys
import json
import sqlite3
from datetime import datetime

def fix_database_issues():
    """Fix database connection and schema issues."""
    print("🔧 Fixing Database Issues...")
    
    db_path = "epinnox_club/data/money_circle.db"
    
    try:
        # Ensure database directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if users table exists and has required columns
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("  Creating users table...")
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    role TEXT DEFAULT 'member',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    agreement_accepted BOOLEAN DEFAULT FALSE,
                    agreement_accepted_at TIMESTAMP,
                    agreement_ip TEXT
                )
            """)
        
        # Check if sessions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'")
        if not cursor.fetchone():
            print("  Creating sessions table...")
            cursor.execute("""
                CREATE TABLE sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
        
        # Ensure epinnox user exists
        cursor.execute("SELECT id FROM users WHERE username = 'epinnox'")
        if not cursor.fetchone():
            print("  Creating epinnox user...")
            import bcrypt
            password_hash = bcrypt.hashpw('securepass123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cursor.execute("""
                INSERT INTO users (username, password_hash, email, role, agreement_accepted, agreement_accepted_at)
                VALUES ('epinnox', ?, '<EMAIL>', 'admin', TRUE, CURRENT_TIMESTAMP)
            """, (password_hash,))
        
        conn.commit()
        conn.close()
        
        print("  ✅ Database issues fixed")
        return True
        
    except Exception as e:
        print(f"  ❌ Database fix failed: {e}")
        return False

def fix_port_configuration():
    """Fix port configuration issues."""
    print("🔧 Fixing Port Configuration...")
    
    try:
        # Check if there are any hardcoded port references in static files
        static_dir = "epinnox_club/static"
        
        if os.path.exists(static_dir):
            for root, dirs, files in os.walk(static_dir):
                for file in files:
                    if file.endswith(('.js', '.html')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # Check for port 8084 references
                            if 'localhost:8084' in content:
                                print(f"  Found port 8084 reference in {file_path}")
                                # Replace with 8085
                                content = content.replace('localhost:8084', 'localhost:8085')
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write(content)
                                print(f"  ✅ Fixed port reference in {file_path}")
                        except Exception as e:
                            print(f"  ⚠️ Could not process {file_path}: {e}")
        
        print("  ✅ Port configuration checked")
        return True
        
    except Exception as e:
        print(f"  ❌ Port fix failed: {e}")
        return False

def clear_browser_cache_instructions():
    """Provide instructions for clearing browser cache."""
    print("🔧 Browser Cache Instructions...")
    print("  To fix port mismatch issues, please:")
    print("  1. Clear your browser cache (Ctrl+Shift+Delete)")
    print("  2. Close all browser tabs for localhost:8084")
    print("  3. Navigate directly to http://localhost:8085/dashboard")
    print("  4. Hard refresh the page (Ctrl+F5)")
    print("  ✅ Browser cache instructions provided")

def create_startup_script():
    """Create a startup script with proper configuration."""
    print("🔧 Creating Startup Script...")
    
    startup_script = """#!/usr/bin/env python3
\"\"\"
Money Circle Platform Startup Script
Ensures proper configuration and startup sequence.
\"\"\"

import os
import sys
import asyncio
from pathlib import Path

# Add the epinnox_club directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'epinnox_club'))

async def main():
    \"\"\"Main startup function.\"\"\"
    print("🚀 Starting Money Circle Platform...")
    print("=" * 50)
    
    # Import and start the application
    try:
        from app import MoneyCircleApp
        
        app = MoneyCircleApp()
        
        # Start the application on port 8085
        print("🌐 Starting server on http://localhost:8085")
        print("📊 Dashboard: http://localhost:8085/dashboard")
        print("🔐 Login: epinnox / securepass123")
        print("=" * 50)
        
        await app.start_server(host='localhost', port=8085)
        
    except Exception as e:
        print(f"❌ Startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
"""
    
    try:
        with open("start_money_circle.py", "w") as f:
            f.write(startup_script)
        
        # Make it executable on Unix systems
        if os.name != 'nt':
            os.chmod("start_money_circle.py", 0o755)
        
        print("  ✅ Startup script created: start_money_circle.py")
        return True
        
    except Exception as e:
        print(f"  ❌ Startup script creation failed: {e}")
        return False

def run_diagnostic_test():
    """Run the diagnostic test to verify fixes."""
    print("🔧 Running Diagnostic Test...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_money_circle_dashboard.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("  ✅ Diagnostic test passed")
            return True
        else:
            print("  ⚠️ Diagnostic test had issues")
            print(f"  Output: {result.stdout}")
            print(f"  Errors: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Diagnostic test failed: {e}")
        return False

def main():
    """Main fix function."""
    print("🚀 Money Circle Platform Issue Fix")
    print("=" * 50)
    print(f"⏰ Fix started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    fixes_applied = []
    
    # Apply fixes
    if fix_database_issues():
        fixes_applied.append("Database Issues")
    
    if fix_port_configuration():
        fixes_applied.append("Port Configuration")
    
    clear_browser_cache_instructions()
    fixes_applied.append("Browser Cache Instructions")
    
    if create_startup_script():
        fixes_applied.append("Startup Script")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 FIX SUMMARY")
    print("=" * 50)
    
    print(f"✅ Fixes Applied: {len(fixes_applied)}")
    for fix in fixes_applied:
        print(f"   • {fix}")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Restart the Money Circle platform")
    print("2. Use: python start_money_circle.py")
    print("3. Clear browser cache and navigate to http://localhost:8085")
    print("4. Run diagnostic test: python test_money_circle_dashboard.py")
    
    print("\n💡 If issues persist:")
    print("   • Check the log file: epinnox_club/logs/money_circle.log")
    print("   • Verify database permissions")
    print("   • Ensure no other services are using port 8085")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
