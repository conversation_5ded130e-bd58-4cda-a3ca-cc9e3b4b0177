#!/usr/bin/env python3
"""
Enhanced Decision Engine - Phase 4
Upgraded Multi-Timeframe Analyzer that scores and ranks all available symbols,
automatically selects the highest-scoring symbol for signal generation,
and handles position management across multiple concurrent trades.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

from .market_scanner import MultiSymbolMarketScanner, SymbolMetrics, MarketOpportunity
from .symbol_selector import DynamicSymbolSelector, SymbolScore
from .multi_symbol_trader import MultiSymbolAutonomousTrader

logger = logging.getLogger(__name__)

@dataclass
class DecisionContext:
    """Context for making trading decisions."""
    symbol: str
    timeframe: str
    market_conditions: str  # 'bullish', 'bearish', 'neutral', 'volatile'
    trend_strength: float
    volatility_level: str  # 'low', 'medium', 'high'
    liquidity_status: str  # 'excellent', 'good', 'poor'
    risk_level: str  # 'low', 'medium', 'high'
    confidence: float
    supporting_factors: List[str]
    warning_factors: List[str]

@dataclass
class EnhancedSignal:
    """Enhanced trading signal with comprehensive analysis."""
    symbol: str
    action: str  # 'LONG', 'SHORT', 'CLOSE_LONG', 'CLOSE_SHORT', 'WAIT'
    confidence: float
    strength: float  # Signal strength 0-1
    timeframe: str
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: float
    risk_reward_ratio: float
    expected_return: float
    max_hold_time: int
    decision_context: DecisionContext
    supporting_analysis: Dict[str, Any]
    timestamp: float

@dataclass
class MarketRegime:
    """Current market regime analysis."""
    regime_type: str  # 'trending', 'ranging', 'volatile', 'breakout'
    regime_strength: float
    dominant_timeframe: str
    key_levels: Dict[str, float]  # support, resistance, etc.
    market_sentiment: str  # 'bullish', 'bearish', 'neutral'
    volatility_regime: str  # 'low', 'medium', 'high'
    liquidity_regime: str  # 'normal', 'stressed', 'abundant'

class EnhancedDecisionEngine:
    """
    Enhanced decision engine that integrates all components for optimal
    multi-symbol trading decisions with advanced market analysis.
    """

    def __init__(self, config: Dict[str, Any], market_scanner: MultiSymbolMarketScanner,
                 symbol_selector: DynamicSymbolSelector, multi_symbol_trader: MultiSymbolAutonomousTrader):
        self.config = config
        self.market_scanner = market_scanner
        self.symbol_selector = symbol_selector
        self.multi_symbol_trader = multi_symbol_trader
        
        # Decision engine configuration
        self.decision_interval = config.get('enhanced_decision_engine', {}).get('decision_interval', 30)
        self.min_signal_confidence = config.get('enhanced_decision_engine', {}).get('min_confidence', 0.7)
        self.max_signals_per_cycle = config.get('enhanced_decision_engine', {}).get('max_signals_per_cycle', 3)
        self.regime_analysis_enabled = config.get('enhanced_decision_engine', {}).get('regime_analysis', True)
        
        # Market regime tracking
        self.current_market_regime: Optional[MarketRegime] = None
        self.regime_history: List[MarketRegime] = []
        
        # Decision tracking
        self.recent_signals: List[EnhancedSignal] = []
        self.decision_performance: Dict[str, float] = {}
        self.symbol_performance_tracking: Dict[str, List[float]] = {}
        
        # Advanced analysis components
        self.timeframe_weights = {
            '1m': 0.1, '5m': 0.15, '15m': 0.2, '1h': 0.25, '4h': 0.2, '1d': 0.1
        }
        
        # Performance metrics
        self.engine_stats = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'avg_confidence': 0.0,
            'avg_signal_strength': 0.0,
            'regime_accuracy': 0.0,
            'symbol_selection_accuracy': 0.0
        }
        
        self.running = False
        self.decision_task = None
        
        logger.info(f"🧠 Enhanced Decision Engine initialized")
        logger.info(f"   ⏱️ Decision interval: {self.decision_interval}s")
        logger.info(f"   🎯 Min confidence: {self.min_signal_confidence}")
        logger.info(f"   📊 Max signals per cycle: {self.max_signals_per_cycle}")

    async def start_engine(self):
        """Start the enhanced decision engine."""
        if self.running:
            logger.warning("Enhanced decision engine is already running")
            return
            
        self.running = True
        self.decision_task = asyncio.create_task(self._decision_loop())
        logger.info("🚀 Enhanced decision engine started")

    async def stop_engine(self):
        """Stop the enhanced decision engine."""
        self.running = False
        if self.decision_task:
            self.decision_task.cancel()
            try:
                await self.decision_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Enhanced decision engine stopped")

    async def _decision_loop(self):
        """Main decision engine loop."""
        while self.running:
            try:
                # Analyze current market regime
                if self.regime_analysis_enabled:
                    await self._analyze_market_regime()
                
                # Generate enhanced signals
                signals = await self._generate_enhanced_signals()
                
                # Process and execute signals
                if signals:
                    await self._process_signals(signals)
                
                # Update performance tracking
                await self._update_performance_tracking()
                
                # Clean up old data
                await self._cleanup_old_data()
                
                # Wait for next cycle
                await asyncio.sleep(self.decision_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in enhanced decision loop: {e}")
                await asyncio.sleep(10)

    async def _analyze_market_regime(self):
        """Analyze current market regime across all symbols."""
        try:
            # Get market data from scanner
            symbol_metrics = self.market_scanner.symbol_metrics
            opportunities = self.market_scanner.get_market_opportunities()
            
            if not symbol_metrics:
                return
            
            # Analyze overall market conditions
            avg_volatility = np.mean([m.volatility for m in symbol_metrics.values()])
            avg_trend_strength = np.mean([m.trend_strength for m in symbol_metrics.values()])
            avg_momentum = np.mean([m.momentum_score for m in symbol_metrics.values()])
            avg_volume_trend = np.mean([m.volume_trend for m in symbol_metrics.values()])
            
            # Determine regime type
            regime_type = self._determine_regime_type(avg_volatility, avg_trend_strength, avg_momentum)
            
            # Determine regime strength
            regime_strength = self._calculate_regime_strength(avg_trend_strength, avg_momentum, avg_volume_trend)
            
            # Determine dominant timeframe
            dominant_timeframe = self._determine_dominant_timeframe(opportunities)
            
            # Analyze market sentiment
            bullish_symbols = len([m for m in symbol_metrics.values() if m.price_change_pct > 0.02])
            bearish_symbols = len([m for m in symbol_metrics.values() if m.price_change_pct < -0.02])
            total_symbols = len(symbol_metrics)
            
            if bullish_symbols > bearish_symbols * 1.5:
                market_sentiment = 'bullish'
            elif bearish_symbols > bullish_symbols * 1.5:
                market_sentiment = 'bearish'
            else:
                market_sentiment = 'neutral'
            
            # Create market regime
            self.current_market_regime = MarketRegime(
                regime_type=regime_type,
                regime_strength=regime_strength,
                dominant_timeframe=dominant_timeframe,
                key_levels={},  # Would be populated with support/resistance levels
                market_sentiment=market_sentiment,
                volatility_regime='high' if avg_volatility > 0.04 else 'medium' if avg_volatility > 0.02 else 'low',
                liquidity_regime='normal'  # Simplified
            )
            
            # Add to history
            self.regime_history.append(self.current_market_regime)
            if len(self.regime_history) > 100:
                self.regime_history = self.regime_history[-50:]
            
            logger.debug(f"🌍 Market regime: {regime_type} ({market_sentiment}, strength: {regime_strength:.2f})")
            
        except Exception as e:
            logger.error(f"❌ Error analyzing market regime: {e}")

    def _determine_regime_type(self, volatility: float, trend_strength: float, momentum: float) -> str:
        """Determine the current market regime type."""
        if trend_strength > 0.7 and momentum > 0.6:
            return 'trending'
        elif volatility > 0.05 and trend_strength < 0.4:
            return 'volatile'
        elif trend_strength > 0.6 and volatility > 0.03:
            return 'breakout'
        else:
            return 'ranging'

    def _calculate_regime_strength(self, trend_strength: float, momentum: float, volume_trend: float) -> float:
        """Calculate the strength of the current regime."""
        return (trend_strength * 0.4 + momentum * 0.3 + min(volume_trend / 2, 1) * 0.3)

    def _determine_dominant_timeframe(self, opportunities: List[MarketOpportunity]) -> str:
        """Determine the dominant timeframe based on opportunities."""
        if not opportunities:
            return '1h'
        
        timeframe_counts = {}
        for opp in opportunities:
            tf = opp.timeframe
            timeframe_counts[tf] = timeframe_counts.get(tf, 0) + 1
        
        return max(timeframe_counts.keys(), key=lambda k: timeframe_counts[k]) if timeframe_counts else '1h'

    async def _generate_enhanced_signals(self) -> List[EnhancedSignal]:
        """Generate enhanced trading signals with comprehensive analysis."""
        try:
            signals = []
            
            # Get top opportunities and symbol scores
            opportunities = self.market_scanner.get_market_opportunities(10)
            symbol_scores = self.symbol_selector.get_symbol_scores()
            
            # Get current portfolio state
            portfolio_status = self.multi_symbol_trader.get_portfolio_status()
            active_symbols = portfolio_status.get('active_symbols', [])
            
            for opportunity in opportunities:
                symbol = opportunity.symbol
                
                # Skip if symbol not scored
                if symbol not in symbol_scores:
                    continue
                
                # Generate enhanced signal
                signal = await self._create_enhanced_signal(opportunity, symbol_scores[symbol], active_symbols)
                
                if signal and signal.confidence >= self.min_signal_confidence:
                    signals.append(signal)
            
            # Sort by confidence and strength
            signals.sort(key=lambda s: s.confidence * s.strength, reverse=True)
            
            # Limit number of signals
            signals = signals[:self.max_signals_per_cycle]
            
            if signals:
                logger.info(f"🧠 Generated {len(signals)} enhanced signals")
                for signal in signals[:2]:  # Log top 2
                    logger.info(f"   📈 {signal.symbol}: {signal.action} "
                              f"(confidence: {signal.confidence:.2%}, strength: {signal.strength:.2f})")
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ Error generating enhanced signals: {e}")
            return []

    async def _create_enhanced_signal(self, opportunity: MarketOpportunity, symbol_score: SymbolScore,
                                    active_symbols: List[str]) -> Optional[EnhancedSignal]:
        """Create an enhanced signal with comprehensive analysis."""
        try:
            symbol = opportunity.symbol
            
            # Get symbol metrics
            metrics = self.market_scanner.get_symbol_metrics(symbol)
            if not metrics:
                return None
            
            # Create decision context
            context = self._create_decision_context(opportunity, symbol_score, metrics)
            
            # Determine action
            action = self._determine_enhanced_action(opportunity, context, active_symbols)
            if not action:
                return None
            
            # Calculate enhanced confidence
            enhanced_confidence = self._calculate_enhanced_confidence(opportunity, symbol_score, context)
            
            # Calculate signal strength
            signal_strength = self._calculate_signal_strength(opportunity, symbol_score, metrics)
            
            # Calculate position size
            position_size = await self._calculate_enhanced_position_size(symbol, opportunity, enhanced_confidence)
            
            # Calculate risk-reward ratio
            risk_reward_ratio = self._calculate_risk_reward_ratio(opportunity)
            
            # Calculate expected return
            expected_return = opportunity.potential_return * enhanced_confidence
            
            # Create enhanced signal
            signal = EnhancedSignal(
                symbol=symbol,
                action=action,
                confidence=enhanced_confidence,
                strength=signal_strength,
                timeframe=opportunity.timeframe,
                entry_price=opportunity.entry_price,
                target_price=opportunity.target_price,
                stop_loss=opportunity.stop_loss,
                position_size=position_size,
                risk_reward_ratio=risk_reward_ratio,
                expected_return=expected_return,
                max_hold_time=self._calculate_enhanced_hold_time(opportunity, context),
                decision_context=context,
                supporting_analysis=self._create_supporting_analysis(opportunity, symbol_score, metrics),
                timestamp=time.time()
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Error creating enhanced signal for {opportunity.symbol}: {e}")
            return None

    def _create_decision_context(self, opportunity: MarketOpportunity, symbol_score: SymbolScore,
                               metrics: SymbolMetrics) -> DecisionContext:
        """Create decision context for the signal."""
        # Determine market conditions
        if metrics.trend_strength > 0.7 and metrics.price_change_pct > 0.02:
            market_conditions = 'bullish'
        elif metrics.trend_strength > 0.7 and metrics.price_change_pct < -0.02:
            market_conditions = 'bearish'
        elif metrics.volatility > 0.05:
            market_conditions = 'volatile'
        else:
            market_conditions = 'neutral'
        
        # Determine volatility level
        if metrics.volatility > 0.05:
            volatility_level = 'high'
        elif metrics.volatility > 0.02:
            volatility_level = 'medium'
        else:
            volatility_level = 'low'
        
        # Determine liquidity status
        if metrics.liquidity_score > 5:
            liquidity_status = 'excellent'
        elif metrics.liquidity_score > 2:
            liquidity_status = 'good'
        else:
            liquidity_status = 'poor'
        
        # Determine risk level
        if metrics.risk_score > 0.7:
            risk_level = 'high'
        elif metrics.risk_score > 0.4:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # Supporting factors
        supporting_factors = []
        if metrics.volume_trend > 1.2:
            supporting_factors.append('strong_volume')
        if metrics.trend_strength > 0.7:
            supporting_factors.append('strong_trend')
        if opportunity.volume_confirmation:
            supporting_factors.append('volume_confirmation')
        if symbol_score.recommendation in ['strong_buy', 'buy']:
            supporting_factors.append('positive_score')
        
        # Warning factors
        warning_factors = []
        if metrics.volatility > 0.06:
            warning_factors.append('high_volatility')
        if metrics.liquidity_score < 1:
            warning_factors.append('low_liquidity')
        if metrics.risk_score > 0.8:
            warning_factors.append('high_risk')
        
        return DecisionContext(
            symbol=opportunity.symbol,
            timeframe=opportunity.timeframe,
            market_conditions=market_conditions,
            trend_strength=metrics.trend_strength,
            volatility_level=volatility_level,
            liquidity_status=liquidity_status,
            risk_level=risk_level,
            confidence=opportunity.confidence * symbol_score.confidence,
            supporting_factors=supporting_factors,
            warning_factors=warning_factors
        )

    def _determine_enhanced_action(self, opportunity: MarketOpportunity, context: DecisionContext,
                                 active_symbols: List[str]) -> Optional[str]:
        """Determine the enhanced trading action."""
        # Check if we should close existing positions first
        if opportunity.symbol in active_symbols:
            # Check if conditions have changed significantly
            if len(context.warning_factors) > len(context.supporting_factors):
                return 'CLOSE_LONG'  # Simplified - would check actual position side
        
        # Determine new position action
        if context.market_conditions == 'bullish' and opportunity.potential_return > 0:
            return 'LONG'
        elif context.market_conditions == 'bearish' and opportunity.potential_return < 0:
            return 'SHORT'
        elif context.market_conditions == 'volatile' and opportunity.opportunity_type == 'volatility':
            return 'LONG'  # Simplified volatility trading
        else:
            return 'WAIT'

    def _calculate_enhanced_confidence(self, opportunity: MarketOpportunity, symbol_score: SymbolScore,
                                     context: DecisionContext) -> float:
        """Calculate enhanced confidence score."""
        base_confidence = opportunity.confidence * symbol_score.confidence
        
        # Regime alignment bonus
        regime_bonus = 0.0
        if self.current_market_regime:
            if (self.current_market_regime.regime_type == 'trending' and 
                opportunity.opportunity_type in ['trend', 'momentum']):
                regime_bonus = 0.1
            elif (self.current_market_regime.regime_type == 'volatile' and 
                  opportunity.opportunity_type == 'volatility'):
                regime_bonus = 0.1
        
        # Supporting factors bonus
        support_bonus = len(context.supporting_factors) * 0.05
        
        # Warning factors penalty
        warning_penalty = len(context.warning_factors) * 0.1
        
        enhanced_confidence = base_confidence + regime_bonus + support_bonus - warning_penalty
        
        return max(0, min(1, enhanced_confidence))

    def _calculate_signal_strength(self, opportunity: MarketOpportunity, symbol_score: SymbolScore,
                                 metrics: SymbolMetrics) -> float:
        """Calculate signal strength."""
        # Combine multiple strength indicators
        trend_strength = metrics.trend_strength
        momentum_strength = metrics.momentum_score
        volume_strength = min(metrics.volume_trend / 2, 1)
        opportunity_strength = opportunity.confidence
        
        signal_strength = (
            0.3 * trend_strength +
            0.25 * momentum_strength +
            0.2 * volume_strength +
            0.25 * opportunity_strength
        )
        
        return max(0, min(1, signal_strength))

    async def _calculate_enhanced_position_size(self, symbol: str, opportunity: MarketOpportunity,
                                              confidence: float) -> float:
        """Calculate enhanced position size."""
        # Get portfolio status
        portfolio_status = self.multi_symbol_trader.get_portfolio_status()
        available_capital = portfolio_status['portfolio_metrics']['available_capital']
        
        # Base size on confidence and available capital
        base_size = available_capital * 0.1  # 10% base allocation
        
        # Confidence scaling
        confidence_multiplier = confidence * 2  # 0-2x multiplier
        
        # Risk adjustment
        risk_adjustment = 1.0
        if opportunity.risk_level == 'high':
            risk_adjustment = 0.5
        elif opportunity.risk_level == 'low':
            risk_adjustment = 1.5
        
        position_size = base_size * confidence_multiplier * risk_adjustment
        
        # Limits
        min_size = available_capital * 0.01  # 1% minimum
        max_size = available_capital * 0.25  # 25% maximum
        
        return max(min_size, min(position_size, max_size))

    def _calculate_risk_reward_ratio(self, opportunity: MarketOpportunity) -> float:
        """Calculate risk-reward ratio."""
        if opportunity.stop_loss == 0:
            return 0
        
        potential_profit = abs(opportunity.target_price - opportunity.entry_price)
        potential_loss = abs(opportunity.entry_price - opportunity.stop_loss)
        
        return potential_profit / potential_loss if potential_loss > 0 else 0

    def _calculate_enhanced_hold_time(self, opportunity: MarketOpportunity, context: DecisionContext) -> int:
        """Calculate enhanced maximum hold time."""
        base_times = {
            'momentum': 3600,    # 1 hour
            'volatility': 1800,  # 30 minutes
            'trend': 7200,       # 2 hours
            'reversal': 1800     # 30 minutes
        }
        
        base_time = base_times.get(opportunity.opportunity_type, 3600)
        
        # Adjust based on market conditions
        if context.market_conditions in ['bullish', 'bearish']:
            base_time *= 1.5  # Hold longer in trending markets
        elif context.volatility_level == 'high':
            base_time *= 0.7  # Shorter holds in high volatility
        
        return int(base_time)

    def _create_supporting_analysis(self, opportunity: MarketOpportunity, symbol_score: SymbolScore,
                                  metrics: SymbolMetrics) -> Dict[str, Any]:
        """Create supporting analysis data."""
        return {
            'opportunity_type': opportunity.opportunity_type,
            'symbol_score': symbol_score.overall_score,
            'symbol_recommendation': symbol_score.recommendation,
            'volatility': metrics.volatility,
            'liquidity_score': metrics.liquidity_score,
            'trend_strength': metrics.trend_strength,
            'momentum_score': metrics.momentum_score,
            'volume_trend': metrics.volume_trend,
            'market_cap_rank': metrics.market_cap_rank,
            'price_change_24h': metrics.price_change_pct
        }

    async def _process_signals(self, signals: List[EnhancedSignal]):
        """Process and execute enhanced signals."""
        try:
            for signal in signals:
                # Store signal
                self.recent_signals.append(signal)
                
                # Update statistics
                self.engine_stats['total_decisions'] += 1
                
                # Execute signal (placeholder - would integrate with execution system)
                await self._execute_enhanced_signal(signal)
                
            # Keep recent signals manageable
            if len(self.recent_signals) > 100:
                self.recent_signals = self.recent_signals[-50:]
                
        except Exception as e:
            logger.error(f"❌ Error processing signals: {e}")

    async def _execute_enhanced_signal(self, signal: EnhancedSignal):
        """Execute an enhanced signal."""
        # Placeholder for signal execution
        logger.info(f"🎯 Enhanced signal: {signal.action} {signal.symbol} "
                   f"size: {signal.position_size:.2f} (confidence: {signal.confidence:.2%})")

    async def _update_performance_tracking(self):
        """Update performance tracking metrics."""
        try:
            if not self.recent_signals:
                return
            
            # Calculate average confidence
            recent_confidences = [s.confidence for s in self.recent_signals[-20:]]
            self.engine_stats['avg_confidence'] = np.mean(recent_confidences)
            
            # Calculate average signal strength
            recent_strengths = [s.strength for s in self.recent_signals[-20:]]
            self.engine_stats['avg_signal_strength'] = np.mean(recent_strengths)
            
        except Exception as e:
            logger.error(f"❌ Error updating performance tracking: {e}")

    async def _cleanup_old_data(self):
        """Clean up old data to prevent memory issues."""
        current_time = time.time()
        
        # Clean old signals (keep last 24 hours)
        self.recent_signals = [
            s for s in self.recent_signals 
            if current_time - s.timestamp < 86400
        ]

    def get_engine_status(self) -> Dict[str, Any]:
        """Get current engine status."""
        return {
            'running': self.running,
            'current_regime': {
                'type': self.current_market_regime.regime_type if self.current_market_regime else 'unknown',
                'strength': self.current_market_regime.regime_strength if self.current_market_regime else 0,
                'sentiment': self.current_market_regime.market_sentiment if self.current_market_regime else 'neutral'
            } if self.current_market_regime else None,
            'recent_signals_count': len(self.recent_signals),
            'engine_stats': self.engine_stats.copy()
        }

    def get_recent_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent enhanced signals."""
        return [
            {
                'symbol': s.symbol,
                'action': s.action,
                'confidence': s.confidence,
                'strength': s.strength,
                'timeframe': s.timeframe,
                'position_size': s.position_size,
                'expected_return': s.expected_return,
                'market_conditions': s.decision_context.market_conditions,
                'timestamp': s.timestamp
            }
            for s in self.recent_signals[-limit:]
        ]
