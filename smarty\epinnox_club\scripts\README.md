# Scripts Directory

This directory contains utility scripts, fix scripts, and maintenance tools for the Money Circle platform.

## 🔧 Utility Scripts

### Setup & Installation
- `install_dependencies.py` - Install required Python packages
- `setup_demo_environment.py` - Set up demo environment with sample data
- `setup_production_env.py` - Configure production environment
- `setup_phase3_production.py` - Phase 3 production setup
- `setup_real_credentials.py` - Configure real exchange credentials

### Database Management
- `migrate_database.py` - Database migration utilities
- `fix_database.py` - Database repair and maintenance
- `render_database_setup.py` - Render deployment database setup

### Authentication & Security
- `fix_authentication.py` - Authentication system fixes
- `fix_demo_auth.py` - Demo authentication fixes
- `get_csrf_token.py` - CSRF token utilities

### Data Management
- `backup_production.py` - Production backup utilities
- `cleanup_corrupted_exchanges.py` - Clean up corrupted exchange data
- `fix_demo_data.py` - Fix demo data issues
- `fix_critical_issues.py` - Critical system fixes

### Development & Testing
- `quick_start.py` - Quick development server start
- `quick_deployment_test.py` - Quick deployment testing
- `run_local.py` - Local development server
- `start_server_simple.py` - Simple server startup
- `working_server.py` - Working server configuration

### Maintenance
- `update_admin.py` - Admin user management
- `validate_documentation.py` - Documentation validation

### Build & Deployment
- `build.sh` - Build script for deployment

## 📋 Usage Examples

### Quick Start Development
```bash
# Install dependencies
python scripts/install_dependencies.py

# Set up demo environment
python scripts/setup_demo_environment.py

# Start local server
python scripts/run_local.py
```

### Production Setup
```bash
# Set up production environment
python scripts/setup_production_env.py

# Configure real credentials
python scripts/setup_real_credentials.py

# Run production setup
python scripts/setup_phase3_production.py
```

### Database Management
```bash
# Migrate database
python scripts/migrate_database.py

# Fix database issues
python scripts/fix_database.py

# Backup production data
python scripts/backup_production.py
```

### Troubleshooting
```bash
# Fix authentication issues
python scripts/fix_authentication.py

# Fix critical system issues
python scripts/fix_critical_issues.py

# Clean up corrupted data
python scripts/cleanup_corrupted_exchanges.py
```

## ⚠️ Important Notes

- Always backup your data before running fix scripts
- Test scripts in development environment first
- Some scripts require specific environment variables
- Check script documentation before running in production

## 🔗 Related Directories

- `../tests/` - Test scripts and validation tools
- `../docs/` - Documentation and guides
- `../deployment/` - Production deployment scripts
