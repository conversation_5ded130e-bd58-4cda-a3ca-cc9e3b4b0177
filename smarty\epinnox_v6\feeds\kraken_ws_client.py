#!/usr/bin/env python3
"""
Kraken WebSocket Client for Real-Time Market Data (EU-Friendly Alternative)
Epinnox V6 - Standalone AI Strategy Tuner
"""

import asyncio
import json
import logging
import time
from typing import Dict, Callable, Optional, Any
import websockets
from datetime import datetime

logger = logging.getLogger(__name__)

class KrakenWebSocketClient:
    """
    Real-time Kraken WebSocket client for market data streaming.
    EU-friendly alternative to HTX and Binance.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        # Kraken WebSocket is EU-accessible
        self.ws_url = "wss://ws.kraken.com"
        self.symbols = config['symbols']['enabled']
        
        # Connection management
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_interval = 5
        
        # Data handlers
        self.trade_handler: Optional[Callable] = None
        self.depth_handler: Optional[Callable] = None
        self.error_handler: Optional[Callable] = None
        
        # Statistics
        self.stats = {
            'messages_received': 0,
            'trades_processed': 0,
            'depth_updates': 0,
            'connection_time': None,
            'last_message_time': None
        }
        
        logger.info("Kraken WebSocket Client initialized")
    
    def set_trade_handler(self, handler: Callable[[Dict], None]):
        """Set handler for trade data."""
        self.trade_handler = handler
        logger.info("Trade handler registered")
    
    def set_depth_handler(self, handler: Callable[[Dict], None]):
        """Set handler for order book depth data."""
        self.depth_handler = handler
        logger.info("Depth handler registered")
    
    def set_error_handler(self, handler: Callable[[Exception], None]):
        """Set handler for connection errors."""
        self.error_handler = handler
        logger.info("Error handler registered")
    
    async def connect(self) -> bool:
        """
        Establish WebSocket connection to Kraken.
        Returns True if successful, False otherwise.
        """
        try:
            logger.info(f"Connecting to Kraken WebSocket: {self.ws_url}")
            
            self.websocket = await websockets.connect(
                self.ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            
            self.is_connected = True
            self.reconnect_attempts = 0
            self.stats['connection_time'] = datetime.now()
            
            # Subscribe to trade feeds
            await self._subscribe_to_feeds()
            
            logger.info("[OK] Connected to Kraken WebSocket successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to connect to Kraken WebSocket: {e}")
            self.is_connected = False
            if self.error_handler:
                try:
                    await self.error_handler(e)
                except Exception:
                    pass
            return False
    
    async def _subscribe_to_feeds(self):
        """Subscribe to trade and book feeds for configured symbols."""
        try:
            # Convert symbols to Kraken format
            kraken_symbols = []
            for symbol in self.symbols[:2]:  # Limit to 2 symbols for testing
                kraken_symbol = self._to_kraken_symbol(symbol)
                if kraken_symbol:
                    kraken_symbols.append(kraken_symbol)
            
            if not kraken_symbols:
                kraken_symbols = ["XBT/USD"]  # Default to Bitcoin
            
            # Subscribe to trade feed
            subscribe_message = {
                "event": "subscribe",
                "pair": kraken_symbols,
                "subscription": {
                    "name": "trade"
                }
            }
            
            await self.websocket.send(json.dumps(subscribe_message))
            logger.info(f"[SUBSCRIBE] Subscribed to Kraken trades: {kraken_symbols}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to Kraken feeds: {e}")
    
    def _to_kraken_symbol(self, standard_symbol: str) -> Optional[str]:
        """Convert standard symbol to Kraken format."""
        symbol_map = {
            'BTC-USDT': 'XBT/USDT',
            'ETH-USDT': 'ETH/USDT',
            'DOGE-USDT': 'DOGE/USDT',
            'SOL-USDT': 'SOL/USDT',
            'ADA-USDT': 'ADA/USDT'
        }
        return symbol_map.get(standard_symbol)
    
    def _from_kraken_symbol(self, kraken_symbol: str) -> str:
        """Convert Kraken symbol to standard format."""
        symbol_map = {
            'XBT/USDT': 'BTC-USDT',
            'ETH/USDT': 'ETH-USDT',
            'DOGE/USDT': 'DOGE-USDT',
            'SOL/USDT': 'SOL-USDT',
            'ADA/USDT': 'ADA-USDT',
            'XBT/USD': 'BTC-USDT'  # Fallback
        }
        return symbol_map.get(kraken_symbol, kraken_symbol.replace('/', '-'))
    
    async def listen(self):
        """
        Main listening loop for WebSocket messages.
        Handles incoming data and routes to appropriate handlers.
        """
        if not self.is_connected or not self.websocket:
            logger.error("[ERROR] WebSocket not connected")
            return
        
        logger.info("[LISTEN] Starting Kraken WebSocket message listener")
        
        try:
            async for message in self.websocket:
                await self._process_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.warning("[WARN] Kraken WebSocket connection closed")
            self.is_connected = False
            await self._handle_disconnect()
            
        except Exception as e:
            logger.error(f"[ERROR] Error in Kraken WebSocket listener: {e}")
            self.is_connected = False
            if self.error_handler:
                try:
                    await self.error_handler(e)
                except Exception:
                    pass
    
    async def _process_message(self, raw_message):
        """Process incoming WebSocket message."""
        try:
            data = json.loads(raw_message)
            self.stats['messages_received'] += 1
            self.stats['last_message_time'] = datetime.now()
            
            # Handle different message types
            if isinstance(data, list) and len(data) >= 4:
                # Trade data format: [channelID, trade_data, "trade", "pair"]
                if data[2] == "trade":
                    await self._handle_trade_data(data)
            elif isinstance(data, dict):
                # Status messages
                if data.get('event') == 'subscriptionStatus':
                    logger.info(f"[STATUS] Kraken subscription: {data.get('status', 'unknown')}")
                elif data.get('event') == 'systemStatus':
                    logger.info(f"[STATUS] Kraken system: {data.get('status', 'unknown')}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error processing Kraken message: {e}")
    
    async def _handle_trade_data(self, data):
        """Handle trade data from Kraken."""
        if not self.trade_handler:
            return
        
        try:
            # Kraken trade format: [channelID, [[price, volume, time, side, orderType, misc]], "trade", "pair"]
            trades = data[1]
            pair = data[3]
            symbol = self._from_kraken_symbol(pair)
            
            for trade in trades:
                price = float(trade[0])
                volume = float(trade[1])
                timestamp = int(float(trade[2]) * 1000)  # Convert to milliseconds
                side = 'buy' if trade[3] == 'b' else 'sell'
                
                trade_data = {
                    'symbol': symbol,
                    'price': price,
                    'quantity': volume,
                    'side': side,
                    'timestamp': timestamp,
                    'trade_id': f"kraken_{timestamp}_{hash(str(trade))}"
                }
                
                await self.trade_handler(trade_data)
                self.stats['trades_processed'] += 1
            
        except Exception as e:
            logger.error(f"[ERROR] Error handling Kraken trade data: {e}")
    
    async def _handle_disconnect(self):
        """Handle WebSocket disconnection and attempt reconnection."""
        self.is_connected = False
        
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.warning(f"[RECONNECT] Attempting Kraken reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
            
            await asyncio.sleep(self.reconnect_interval)
            
            if await self.connect():
                # Restart listening
                asyncio.create_task(self.listen())
            else:
                await self._handle_disconnect()
        else:
            logger.error("[ERROR] Max Kraken reconnection attempts reached. Giving up.")
            if self.error_handler:
                try:
                    await self.error_handler(Exception("Max reconnection attempts reached"))
                except Exception:
                    pass
    
    async def disconnect(self):
        """Gracefully disconnect from WebSocket."""
        if self.websocket and self.is_connected:
            logger.info("[DISCONNECT] Disconnecting from Kraken WebSocket")
            await self.websocket.close()
            self.is_connected = False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection and processing statistics."""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'subscribed_symbols': len(self.symbols),
            'exchange': 'Kraken'
        }
