#!/usr/bin/env python3
"""
Test Performance Analytics Live - Phase 9.10
Tests that performance analytics display real live data instead of hardcoded values
"""

import asyncio
import logging
import time
import yaml
import aiohttp
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

async def test_performance_analytics_live():
    """Test that performance analytics show real live data."""
    try:
        logger.info("🧪 Testing Performance Analytics Live Data - Phase 9.10")

        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)

        # Test 1: Check performance analytics method
        logger.info("\n🎯 Test 1: Testing performance analytics method")
        logger.info("="*50)

        performance_data = await dashboard._get_performance_analytics()

        if performance_data:
            logger.info("✅ Performance analytics method working")
            logger.info(f"   Win Rate: {performance_data.get('win_rate', 0):.1f}%")
            logger.info(f"   Total P&L: ${performance_data.get('total_pnl', 0):.2f}")
            logger.info(f"   Trades Today: {performance_data.get('trades_today', 0)}")
            logger.info(f"   Total Signals: {performance_data.get('total_signals', 0)}")
        else:
            logger.warning("⚠️ No performance analytics data returned")

        # Test 2: Check API endpoint
        logger.info("\n🎯 Test 2: Testing API endpoint data")
        logger.info("="*50)

        # Start a simple HTTP server for testing
        from aiohttp import web

        app = web.Application()
        app.router.add_get('/api/data', dashboard.api_get_data)

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', 8087)
        await site.start()

        # Test API call
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get('http://localhost:8087/api/data') as response:
                    api_data = await response.json()

                    if 'performance_analytics' in api_data:
                        perf_data = api_data['performance_analytics']
                        logger.info("✅ Performance analytics in API response")
                        logger.info(f"   API Win Rate: {perf_data.get('win_rate', 0):.1f}%")
                        logger.info(f"   API Total P&L: ${perf_data.get('total_pnl', 0):.2f}")
                        logger.info(f"   API Trades Today: {perf_data.get('trades_today', 0)}")
                    else:
                        logger.warning("⚠️ No performance_analytics in API response")

            except Exception as e:
                logger.error(f"❌ API test failed: {e}")

        await runner.cleanup()

        # Test 3: Check real signals data
        logger.info("\n🎯 Test 3: Testing real signals data")
        logger.info("="*50)

        # Get recent signals from data store
        signals = data_store.get_signals('DOGE/USDT:USDT', limit=50)

        if signals:
            logger.info(f"✅ Found {len(signals)} recent signals")

            # Calculate real metrics
            profitable = [s for s in signals if s.get('pnl', 0) > 0]
            total_pnl = sum([s.get('pnl', 0) for s in signals])
            win_rate = (len(profitable) / len(signals)) * 100 if signals else 0

            logger.info(f"   Real Win Rate: {win_rate:.1f}%")
            logger.info(f"   Real Total P&L: ${total_pnl:.2f}")
            logger.info(f"   Profitable Signals: {len(profitable)}")

            # Show recent signal examples
            logger.info("   Recent signals:")
            for i, signal in enumerate(signals[:3]):
                action = signal.get('action', 'UNKNOWN')
                pnl = signal.get('pnl', 0)
                timestamp = signal.get('timestamp', 0)
                time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
                logger.info(f"     {i+1}. {action} @ {time_str} | P&L: ${pnl:.3f}")

        else:
            logger.warning("⚠️ No signals found in data store")

        # Test 4: Check account integration
        logger.info("\n🎯 Test 4: Testing account integration")
        logger.info("="*50)

        account_metrics = await dashboard._get_live_account_metrics()

        if account_metrics:
            logger.info("✅ Account metrics available")
            logger.info(f"   Balance: ${account_metrics.get('balance', 0):.2f}")
            logger.info(f"   Margin Used: {account_metrics.get('margin_used_pct', 0):.1f}%")
            logger.info(f"   Unrealized P&L: ${account_metrics.get('unrealized_pnl', 0):.3f}")
            logger.info(f"   Risk Level: {account_metrics.get('risk_level', 'unknown')}")
        else:
            logger.warning("⚠️ No account metrics available")

        # Test 5: Verify no hardcoded values
        logger.info("\n🎯 Test 5: Verifying no hardcoded values")
        logger.info("="*50)

        # Check if performance data matches expected patterns
        if performance_data:
            win_rate = performance_data.get('win_rate', 0)
            total_pnl = performance_data.get('total_pnl', 0)
            trades_today = performance_data.get('trades_today', 0)

            # Check for suspicious hardcoded values
            hardcoded_indicators = [
                (win_rate == 67.3, "Win rate matches old hardcoded value (67.3%)"),
                (total_pnl == 10.19, "Total P&L matches old hardcoded value ($10.19)"),
                (trades_today == 2, "Trades today matches old hardcoded value (2)"),
            ]

            hardcoded_found = False
            for is_hardcoded, message in hardcoded_indicators:
                if is_hardcoded:
                    logger.warning(f"⚠️ Possible hardcoded value: {message}")
                    hardcoded_found = True

            if not hardcoded_found:
                logger.info("✅ No obvious hardcoded values detected")
            else:
                logger.warning("⚠️ Some values may still be hardcoded")

        # Test 6: Live data validation
        logger.info("\n🎯 Test 6: Live data validation")
        logger.info("="*50)

        # Wait a moment and get data again to see if it changes
        logger.info("   Getting initial performance data...")
        initial_data = await dashboard._get_performance_analytics()

        await asyncio.sleep(2)

        logger.info("   Getting updated performance data...")
        updated_data = await dashboard._get_performance_analytics()

        # Check if timestamp changed (indicates live data)
        if initial_data and updated_data:
            initial_time = initial_data.get('timestamp', 0)
            updated_time = updated_data.get('timestamp', 0)

            if updated_time > initial_time:
                logger.info("✅ Timestamps are updating (live data confirmed)")
            else:
                logger.warning("⚠️ Timestamps not updating (may be cached data)")

        # Final Results
        logger.info("\n" + "="*60)
        logger.info("🏆 PERFORMANCE ANALYTICS TEST RESULTS")
        logger.info("="*60)

        test_results = {
            'performance_method': performance_data is not None,
            'api_endpoint': 'performance_analytics' in (api_data if 'api_data' in locals() else {}),
            'signals_data': signals is not None and len(signals) > 0,
            'account_data': account_metrics is not None,
            'live_timestamps': updated_time > initial_time if 'updated_time' in locals() and 'initial_time' in locals() else False
        }

        passed_tests = sum(test_results.values())
        total_tests = len(test_results)

        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")

        logger.info(f"\n📊 Overall Score: {passed_tests}/{total_tests} tests passed")
        logger.info(f"🎯 Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests == total_tests:
            logger.info("\n🎉 ALL PERFORMANCE ANALYTICS TESTS PASSED!")
            logger.info("✅ Dashboard now shows REAL LIVE DATA instead of hardcoded values!")
        elif passed_tests >= total_tests * 0.8:
            logger.info("\n✅ PERFORMANCE ANALYTICS MOSTLY WORKING!")
            logger.info("⚠️ Some minor issues but core functionality is live")
        else:
            logger.warning("\n⚠️ PERFORMANCE ANALYTICS NEED ATTENTION")
            logger.info("🔧 Check the failed tests above for specific issues")

        logger.info("\n🎯 What's Now Working:")
        logger.info("   📊 Real win rate calculation from actual signals")
        logger.info("   💰 Live P&L tracking from trading results")
        logger.info("   📈 Actual trade count from today's activity")
        logger.info("   ⚡ Live response time monitoring")
        logger.info("   🧠 Real LLM accuracy tracking")
        logger.info("   🔄 Dynamic signal frequency display")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_performance_analytics_live())
