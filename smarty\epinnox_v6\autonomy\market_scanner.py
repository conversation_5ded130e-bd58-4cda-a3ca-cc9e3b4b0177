#!/usr/bin/env python3
"""
Multi-Symbol Market Scanner - Phase 1
Autonomous scanning and evaluation of all available HTX futures symbols
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

@dataclass
class SymbolMetrics:
    """Comprehensive metrics for a trading symbol."""
    symbol: str
    price: float
    volume_24h: float
    price_change_24h: float
    price_change_pct: float
    volatility: float
    liquidity_score: float
    trend_strength: float
    momentum_score: float
    volume_trend: float
    spread_pct: float
    market_cap_rank: int
    trading_score: float
    risk_score: float
    opportunity_score: float
    last_updated: float

@dataclass
class MarketOpportunity:
    """Identified trading opportunity."""
    symbol: str
    opportunity_type: str  # 'breakout', 'reversal', 'momentum', 'volatility'
    confidence: float
    potential_return: float
    risk_level: str  # 'low', 'medium', 'high'
    timeframe: str
    entry_price: float
    target_price: float
    stop_loss: float
    volume_confirmation: bool
    technical_alignment: float
    timestamp: float

class MultiSymbolMarketScanner:
    """
    Autonomous multi-symbol market scanner for HTX futures.
    Continuously scans all available symbols and identifies trading opportunities.
    """

    def __init__(self, config: Dict[str, Any], exchange_client=None):
        self.config = config
        self.exchange_client = exchange_client

        # Scanner configuration
        self.scan_interval = config.get('market_scanner', {}).get('scan_interval', 30)  # seconds
        self.min_volume_24h = config.get('market_scanner', {}).get('min_volume_24h', 1000000)  # $1M
        self.min_price_change = config.get('market_scanner', {}).get('min_price_change', 0.02)  # 2%
        self.max_symbols_to_track = config.get('market_scanner', {}).get('max_symbols', 50)

        # Data storage
        self.symbol_metrics: Dict[str, SymbolMetrics] = {}
        self.market_opportunities: List[MarketOpportunity] = []
        self.symbol_rankings: List[str] = []
        self.last_scan_time = 0

        # Performance tracking
        self.scan_stats = {
            'total_scans': 0,
            'symbols_scanned': 0,
            'opportunities_found': 0,
            'avg_scan_time': 0,
            'last_scan_duration': 0
        }

        # Symbol universe (HTX futures symbols)
        self.symbol_universe = [
            'BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT', 'SOL/USDT:USDT',
            'ADA/USDT:USDT', 'XRP/USDT:USDT', 'MATIC/USDT:USDT', 'AVAX/USDT:USDT',
            'DOT/USDT:USDT', 'LINK/USDT:USDT', 'UNI/USDT:USDT', 'LTC/USDT:USDT',
            'BCH/USDT:USDT', 'ATOM/USDT:USDT', 'FIL/USDT:USDT', 'TRX/USDT:USDT',
            'ETC/USDT:USDT', 'XLM/USDT:USDT', 'ALGO/USDT:USDT', 'VET/USDT:USDT'
        ]

        self.running = False
        self.scan_task = None

        logger.info(f"🔍 Multi-Symbol Market Scanner initialized")
        logger.info(f"   📊 Tracking {len(self.symbol_universe)} symbols")
        logger.info(f"   ⏱️ Scan interval: {self.scan_interval}s")
        logger.info(f"   💰 Min volume: ${self.min_volume_24h:,.0f}")

    async def start_scanning(self):
        """Start the autonomous market scanning process."""
        if self.running:
            logger.warning("Market scanner is already running")
            return

        self.running = True
        self.scan_task = asyncio.create_task(self._scan_loop())
        logger.info("🚀 Market scanner started")

    async def stop_scanning(self):
        """Stop the market scanning process."""
        self.running = False
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Market scanner stopped")

    async def _scan_loop(self):
        """Main scanning loop."""
        while self.running:
            try:
                scan_start = time.time()

                # Perform comprehensive market scan
                await self._perform_market_scan()

                # Update performance stats
                scan_duration = time.time() - scan_start
                self.scan_stats['last_scan_duration'] = scan_duration
                self.scan_stats['total_scans'] += 1

                # Calculate average scan time
                if self.scan_stats['total_scans'] > 0:
                    self.scan_stats['avg_scan_time'] = (
                        (self.scan_stats['avg_scan_time'] * (self.scan_stats['total_scans'] - 1) + scan_duration) /
                        self.scan_stats['total_scans']
                    )

                logger.info(f"🔍 Market scan completed in {scan_duration:.2f}s "
                          f"({len(self.symbol_metrics)} symbols, {len(self.market_opportunities)} opportunities)")

                # Wait for next scan
                await asyncio.sleep(self.scan_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in market scan loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _perform_market_scan(self):
        """Perform comprehensive market scan of all symbols."""
        try:
            # Clear old opportunities
            self.market_opportunities.clear()

            # Scan each symbol
            scan_tasks = []
            for symbol in self.symbol_universe:
                task = asyncio.create_task(self._scan_symbol(symbol))
                scan_tasks.append(task)

            # Wait for all scans to complete
            results = await asyncio.gather(*scan_tasks, return_exceptions=True)

            # Process results
            valid_metrics = []
            for i, result in enumerate(results):
                if isinstance(result, SymbolMetrics):
                    valid_metrics.append(result)
                    self.symbol_metrics[result.symbol] = result
                elif isinstance(result, Exception):
                    logger.warning(f"❌ Failed to scan {self.symbol_universe[i]}: {result}")

            # Update statistics
            self.scan_stats['symbols_scanned'] = len(valid_metrics)

            # Rank symbols by trading potential
            await self._rank_symbols()

            # Identify market opportunities
            await self._identify_opportunities()

            self.last_scan_time = time.time()

        except Exception as e:
            logger.error(f"❌ Error in market scan: {e}")

    async def _scan_symbol(self, symbol: str) -> Optional[SymbolMetrics]:
        """Scan individual symbol and calculate comprehensive metrics."""
        try:
            if not self.exchange_client:
                # Return mock data for testing
                return self._generate_mock_metrics(symbol)

            # Fetch real market data
            ticker = await self.exchange_client.fetch_ticker(symbol)
            ohlcv_1h = await self.exchange_client.fetch_ohlcv(symbol, '1h', limit=24)
            ohlcv_1d = await self.exchange_client.fetch_ohlcv(symbol, '1d', limit=7)

            if not ticker or not ohlcv_1h:
                return None

            # Calculate comprehensive metrics
            price = float(ticker['last'])
            volume_24h = float(ticker['quoteVolume']) if ticker['quoteVolume'] else 0
            price_change_24h = float(ticker['change']) if ticker['change'] else 0
            price_change_pct = float(ticker['percentage']) / 100 if ticker['percentage'] else 0

            # Calculate volatility (24h)
            if len(ohlcv_1h) >= 24:
                closes = np.array([float(candle[4]) for candle in ohlcv_1h[-24:]])
                volatility = np.std(closes) / np.mean(closes)
            else:
                volatility = 0.02  # Default 2%

            # Calculate liquidity score
            liquidity_score = min(volume_24h / self.min_volume_24h, 10.0)

            # Calculate trend strength
            trend_strength = self._calculate_trend_strength(ohlcv_1h)

            # Calculate momentum score
            momentum_score = self._calculate_momentum_score(ohlcv_1h)

            # Calculate volume trend
            volume_trend = self._calculate_volume_trend(ohlcv_1h)

            # Estimate spread (simplified)
            spread_pct = 0.001  # Default 0.1% for futures

            # Market cap rank (simplified - based on volume)
            market_cap_rank = self._estimate_market_cap_rank(symbol, volume_24h)

            # Calculate composite scores
            trading_score = self._calculate_trading_score(
                volatility, liquidity_score, trend_strength, momentum_score, volume_trend
            )

            risk_score = self._calculate_risk_score(volatility, liquidity_score, spread_pct)

            opportunity_score = self._calculate_opportunity_score(
                trading_score, risk_score, price_change_pct, volume_trend
            )

            return SymbolMetrics(
                symbol=symbol,
                price=price,
                volume_24h=volume_24h,
                price_change_24h=price_change_24h,
                price_change_pct=price_change_pct,
                volatility=volatility,
                liquidity_score=liquidity_score,
                trend_strength=trend_strength,
                momentum_score=momentum_score,
                volume_trend=volume_trend,
                spread_pct=spread_pct,
                market_cap_rank=market_cap_rank,
                trading_score=trading_score,
                risk_score=risk_score,
                opportunity_score=opportunity_score,
                last_updated=time.time()
            )

        except Exception as e:
            logger.error(f"❌ Error scanning symbol {symbol}: {e}")
            return None

    def _generate_mock_metrics(self, symbol: str) -> SymbolMetrics:
        """Generate mock metrics for testing when no exchange client is available."""
        # Generate realistic mock data based on symbol
        base_price = {'BTC': 45000, 'ETH': 2500, 'DOGE': 0.08, 'SOL': 100, 'ADA': 0.5}.get(
            symbol.split('/')[0], 100
        )

        # Add some randomness
        price_variation = np.random.uniform(-0.05, 0.05)
        price = base_price * (1 + price_variation)

        volume_24h = np.random.uniform(5000000, 50000000)  # $5M - $50M
        price_change_pct = np.random.uniform(-0.08, 0.08)  # -8% to +8%
        volatility = np.random.uniform(0.01, 0.06)  # 1% to 6%

        return SymbolMetrics(
            symbol=symbol,
            price=price,
            volume_24h=volume_24h,
            price_change_24h=price * price_change_pct,
            price_change_pct=price_change_pct,
            volatility=volatility,
            liquidity_score=min(volume_24h / self.min_volume_24h, 10.0),
            trend_strength=np.random.uniform(0.3, 0.9),
            momentum_score=np.random.uniform(0.2, 0.8),
            volume_trend=np.random.uniform(0.8, 1.5),
            spread_pct=0.001,
            market_cap_rank=np.random.randint(1, 100),
            trading_score=np.random.uniform(0.4, 0.9),
            risk_score=np.random.uniform(0.2, 0.7),
            opportunity_score=np.random.uniform(0.3, 0.8),
            last_updated=time.time()
        )

    def _calculate_trend_strength(self, ohlcv_data: List) -> float:
        """Calculate trend strength from OHLCV data."""
        if len(ohlcv_data) < 10:
            return 0.5

        closes = np.array([float(candle[4]) for candle in ohlcv_data[-20:]])

        # Calculate moving averages
        sma_short = np.mean(closes[-5:])
        sma_long = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes)

        # Trend direction and strength
        trend_direction = 1 if sma_short > sma_long else -1
        trend_magnitude = abs(sma_short - sma_long) / sma_long

        # Normalize to 0-1 scale
        return min(trend_magnitude * 10, 1.0)

    def _calculate_momentum_score(self, ohlcv_data: List) -> float:
        """Calculate momentum score from price action."""
        if len(ohlcv_data) < 5:
            return 0.5

        closes = np.array([float(candle[4]) for candle in ohlcv_data[-10:]])

        # Calculate rate of change
        if len(closes) >= 5:
            roc = (closes[-1] - closes[-5]) / closes[-5]
            momentum = min(abs(roc) * 20, 1.0)  # Normalize
        else:
            momentum = 0.5

        return momentum

    def _calculate_volume_trend(self, ohlcv_data: List) -> float:
        """Calculate volume trend."""
        if len(ohlcv_data) < 10:
            return 1.0

        volumes = np.array([float(candle[5]) for candle in ohlcv_data[-20:]])

        # Compare recent vs historical volume
        recent_volume = np.mean(volumes[-5:])
        historical_volume = np.mean(volumes[-20:-5]) if len(volumes) >= 20 else np.mean(volumes[:-5])

        if historical_volume > 0:
            volume_ratio = recent_volume / historical_volume
        else:
            volume_ratio = 1.0

        return min(volume_ratio, 3.0)  # Cap at 3x

    def _estimate_market_cap_rank(self, symbol: str, volume_24h: float) -> int:
        """Estimate market cap rank based on symbol and volume."""
        # Simplified ranking based on known major cryptocurrencies
        major_coins = {
            'BTC/USDT:USDT': 1, 'ETH/USDT:USDT': 2, 'USDT/USDT:USDT': 3,
            'BNB/USDT:USDT': 4, 'SOL/USDT:USDT': 5, 'XRP/USDT:USDT': 6,
            'USDC/USDT:USDT': 7, 'ADA/USDT:USDT': 8, 'AVAX/USDT:USDT': 9,
            'DOGE/USDT:USDT': 10, 'TRX/USDT:USDT': 11, 'DOT/USDT:USDT': 12,
            'MATIC/USDT:USDT': 13, 'LTC/USDT:USDT': 14, 'LINK/USDT:USDT': 15
        }

        if symbol in major_coins:
            return major_coins[symbol]

        # Estimate based on volume
        if volume_24h > 100000000:  # $100M+
            return np.random.randint(16, 30)
        elif volume_24h > 50000000:  # $50M+
            return np.random.randint(31, 50)
        elif volume_24h > 10000000:  # $10M+
            return np.random.randint(51, 100)
        else:
            return np.random.randint(101, 500)

    def _calculate_trading_score(self, volatility: float, liquidity_score: float,
                               trend_strength: float, momentum_score: float,
                               volume_trend: float) -> float:
        """Calculate composite trading score."""
        # Weighted combination of factors
        weights = {
            'volatility': 0.25,      # Higher volatility = more trading opportunities
            'liquidity': 0.30,       # Higher liquidity = easier execution
            'trend': 0.20,           # Stronger trends = clearer signals
            'momentum': 0.15,        # Higher momentum = better follow-through
            'volume': 0.10           # Volume confirmation
        }

        # Normalize volatility (optimal range 2-5%)
        vol_score = 1.0 - abs(volatility - 0.035) / 0.035 if volatility > 0 else 0
        vol_score = max(0, min(1, vol_score))

        # Normalize liquidity (already 0-10, convert to 0-1)
        liq_score = min(liquidity_score / 5.0, 1.0)

        # Volume trend score (optimal around 1.2-1.5x)
        vol_trend_score = 1.0 - abs(volume_trend - 1.35) / 1.35
        vol_trend_score = max(0, min(1, vol_trend_score))

        trading_score = (
            weights['volatility'] * vol_score +
            weights['liquidity'] * liq_score +
            weights['trend'] * trend_strength +
            weights['momentum'] * momentum_score +
            weights['volume'] * vol_trend_score
        )

        return max(0, min(1, trading_score))

    def _calculate_risk_score(self, volatility: float, liquidity_score: float,
                            spread_pct: float) -> float:
        """Calculate risk score (higher = more risky)."""
        # Risk factors
        vol_risk = min(volatility / 0.1, 1.0)  # Normalize to 10% max
        liquidity_risk = max(0, 1.0 - liquidity_score / 5.0)  # Lower liquidity = higher risk
        spread_risk = min(spread_pct / 0.01, 1.0)  # Normalize to 1% max

        # Weighted risk score
        risk_score = (
            0.5 * vol_risk +
            0.3 * liquidity_risk +
            0.2 * spread_risk
        )

        return max(0, min(1, risk_score))

    def _calculate_opportunity_score(self, trading_score: float, risk_score: float,
                                   price_change_pct: float, volume_trend: float) -> float:
        """Calculate overall opportunity score."""
        # Risk-adjusted trading score
        risk_adjusted_score = trading_score * (1.0 - risk_score * 0.5)

        # Momentum bonus
        momentum_bonus = min(abs(price_change_pct) * 10, 0.2)  # Up to 20% bonus

        # Volume confirmation bonus
        volume_bonus = min((volume_trend - 1.0) * 0.1, 0.1) if volume_trend > 1.0 else 0

        opportunity_score = risk_adjusted_score + momentum_bonus + volume_bonus

        return max(0, min(1, opportunity_score))

    async def _rank_symbols(self):
        """Rank symbols by trading potential."""
        if not self.symbol_metrics:
            return

        # Sort by opportunity score
        ranked_symbols = sorted(
            self.symbol_metrics.items(),
            key=lambda x: x[1].opportunity_score,
            reverse=True
        )

        self.symbol_rankings = [symbol for symbol, _ in ranked_symbols[:self.max_symbols_to_track]]

        logger.debug(f"📊 Symbol rankings updated: Top 5: {self.symbol_rankings[:5]}")

    async def _identify_opportunities(self):
        """Identify specific trading opportunities."""
        opportunities = []

        for symbol, metrics in self.symbol_metrics.items():
            # Skip low-quality symbols
            if metrics.liquidity_score < 1.0 or metrics.trading_score < 0.4:
                continue

            # Identify different types of opportunities
            opps = await self._analyze_symbol_opportunities(symbol, metrics)
            opportunities.extend(opps)

        # Sort by confidence and potential return
        opportunities.sort(key=lambda x: x.confidence * x.potential_return, reverse=True)

        # Keep top opportunities
        self.market_opportunities = opportunities[:20]
        self.scan_stats['opportunities_found'] = len(self.market_opportunities)

        if self.market_opportunities:
            logger.info(f"🎯 Found {len(self.market_opportunities)} trading opportunities")
            for opp in self.market_opportunities[:3]:  # Log top 3
                logger.info(f"   📈 {opp.symbol}: {opp.opportunity_type} "
                          f"(confidence: {opp.confidence:.2%}, return: {opp.potential_return:.2%})")

    async def _analyze_symbol_opportunities(self, symbol: str, metrics: SymbolMetrics) -> List[MarketOpportunity]:
        """Analyze specific opportunities for a symbol."""
        opportunities = []

        # Momentum opportunity
        if metrics.momentum_score > 0.6 and metrics.volume_trend > 1.2:
            opportunities.append(MarketOpportunity(
                symbol=symbol,
                opportunity_type='momentum',
                confidence=metrics.momentum_score * 0.8,
                potential_return=metrics.volatility * 2,  # 2x volatility as potential
                risk_level='medium' if metrics.risk_score < 0.6 else 'high',
                timeframe='1h',
                entry_price=metrics.price,
                target_price=metrics.price * (1 + metrics.volatility * 1.5),
                stop_loss=metrics.price * (1 - metrics.volatility * 0.8),
                volume_confirmation=metrics.volume_trend > 1.2,
                technical_alignment=metrics.trend_strength,
                timestamp=time.time()
            ))

        # Volatility opportunity
        if metrics.volatility > 0.03 and metrics.liquidity_score > 2.0:
            opportunities.append(MarketOpportunity(
                symbol=symbol,
                opportunity_type='volatility',
                confidence=min(metrics.volatility * 15, 0.9),
                potential_return=metrics.volatility * 1.5,
                risk_level='high' if metrics.volatility > 0.05 else 'medium',
                timeframe='15m',
                entry_price=metrics.price,
                target_price=metrics.price * (1 + metrics.volatility),
                stop_loss=metrics.price * (1 - metrics.volatility * 0.6),
                volume_confirmation=metrics.volume_trend > 1.0,
                technical_alignment=metrics.trading_score,
                timestamp=time.time()
            ))

        # Trend opportunity
        if metrics.trend_strength > 0.7 and abs(metrics.price_change_pct) > 0.02:
            direction = 1 if metrics.price_change_pct > 0 else -1
            opportunities.append(MarketOpportunity(
                symbol=symbol,
                opportunity_type='trend',
                confidence=metrics.trend_strength * 0.9,
                potential_return=abs(metrics.price_change_pct) * 2,
                risk_level='low' if metrics.risk_score < 0.4 else 'medium',
                timeframe='4h',
                entry_price=metrics.price,
                target_price=metrics.price * (1 + direction * metrics.volatility * 2),
                stop_loss=metrics.price * (1 - direction * metrics.volatility),
                volume_confirmation=metrics.volume_trend > 1.1,
                technical_alignment=metrics.trend_strength,
                timestamp=time.time()
            ))

        return opportunities

    def get_top_symbols(self, limit: int = 10) -> List[str]:
        """Get top-ranked symbols for trading."""
        return self.symbol_rankings[:limit]

    def get_symbol_metrics(self, symbol: str) -> Optional[SymbolMetrics]:
        """Get metrics for a specific symbol."""
        return self.symbol_metrics.get(symbol)

    def get_market_opportunities(self, limit: int = 10) -> List[MarketOpportunity]:
        """Get current market opportunities."""
        return self.market_opportunities[:limit]

    def get_scan_stats(self) -> Dict[str, Any]:
        """Get scanner performance statistics."""
        return self.scan_stats.copy()

    def is_symbol_active(self, symbol: str) -> bool:
        """Check if a symbol is actively being tracked."""
        return symbol in self.symbol_rankings[:10]  # Top 10 are considered active
