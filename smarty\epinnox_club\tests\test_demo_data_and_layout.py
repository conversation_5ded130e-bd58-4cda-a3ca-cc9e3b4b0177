#!/usr/bin/env python3
"""
Test Demo Data Population and Layout Fixes for Money Circle
Comprehensive test to verify both issues are resolved.
"""

import requests
import sqlite3
import time
from datetime import datetime

class DemoDataAndLayoutTester:
    """Test demo data and layout fixes."""
    
    def __init__(self, base_url: str = "http://localhost:8085"):
        self.base_url = base_url.rstrip('/')
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_demo_data_in_database(self):
        """Test that demo data exists in database."""
        try:
            conn = sqlite3.connect('data/money_circle.db')
            
            # Check users
            cursor = conn.execute('SELECT COUNT(*) FROM users')
            users_count = cursor.fetchone()[0]
            
            # Check trades
            cursor = conn.execute('SELECT COUNT(*) FROM user_trades')
            trades_count = cursor.fetchone()[0]
            
            # Check positions
            cursor = conn.execute('SELECT COUNT(*) FROM user_positions')
            positions_count = cursor.fetchone()[0]
            
            # Check exchanges
            cursor = conn.execute('SELECT COUNT(*) FROM user_exchanges')
            exchanges_count = cursor.fetchone()[0]
            
            # Check profiles
            cursor = conn.execute('SELECT COUNT(*) FROM member_profiles')
            profiles_count = cursor.fetchone()[0]
            
            # Check performance data
            cursor = conn.execute('SELECT COUNT(*) FROM trading_performance')
            performance_count = cursor.fetchone()[0]
            
            conn.close()
            
            # Verify minimum data exists
            checks = [
                (users_count >= 5, f"Users: {users_count}"),
                (trades_count >= 200, f"Trades: {trades_count}"),
                (positions_count >= 15, f"Positions: {positions_count}"),
                (exchanges_count >= 10, f"Exchanges: {exchanges_count}"),
                (profiles_count >= 5, f"Profiles: {profiles_count}"),
                (performance_count >= 5, f"Performance: {performance_count}")
            ]
            
            passed_checks = sum(1 for check, _ in checks if check)
            total_checks = len(checks)
            
            for check, detail in checks:
                status = "✅" if check else "❌"
                print(f"    {status} {detail}")
            
            success = passed_checks == total_checks
            self.log_test_result("Demo Data in Database", success, f"{passed_checks}/{total_checks} data checks passed")
            return success
            
        except Exception as e:
            self.log_test_result("Demo Data in Database", False, f"Error: {str(e)}")
            return False
    
    def test_demo_user_login_and_data(self):
        """Test logging in with demo user and checking for data display."""
        try:
            # Test login with alex_trader
            session = requests.Session()
            login_data = {
                'username': 'alex_trader',
                'password': 'demo123'
            }
            
            response = session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
            
            if response.status_code == 302:
                # Login successful, now check dashboard
                dashboard_response = session.get(f"{self.base_url}/dashboard")
                
                if dashboard_response.status_code == 200:
                    content = dashboard_response.text
                    
                    # Check for data indicators in dashboard
                    data_checks = [
                        ('Portfolio data', 'portfolio-overview' in content),
                        ('Trading data', 'recent-trades' in content or 'user_trades' in content),
                        ('User info', 'alex_trader' in content or 'Alex Thompson' in content),
                        ('Performance metrics', 'performance' in content.lower()),
                        ('Dashboard layout', 'dashboard-container' in content)
                    ]
                    
                    passed_checks = sum(1 for _, check in data_checks if check)
                    total_checks = len(data_checks)
                    
                    for check_name, passed in data_checks:
                        status = "✅" if passed else "❌"
                        print(f"    {status} {check_name}")
                    
                    success = passed_checks >= total_checks * 0.8  # 80% pass rate
                    self.log_test_result("Demo User Login and Data", success, f"{passed_checks}/{total_checks} data indicators found")
                    return success
                else:
                    self.log_test_result("Demo User Login and Data", False, f"Dashboard HTTP {dashboard_response.status_code}")
                    return False
            else:
                self.log_test_result("Demo User Login and Data", False, f"Login failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Demo User Login and Data", False, f"Error: {str(e)}")
            return False
    
    def test_secondary_page_layouts(self):
        """Test layout consistency on secondary pages."""
        try:
            # Test login first
            session = requests.Session()
            login_data = {'username': 'alex_trader', 'password': 'demo123'}
            session.post(f"{self.base_url}/login", data=login_data)
            
            # Test secondary pages
            pages_to_test = [
                ('/club', 'Club Dashboard'),
                ('/club/strategies', 'Strategy Marketplace'),
                ('/club/members', 'Member Directory'),
                ('/club/analytics', 'Club Analytics')
            ]
            
            layout_results = []
            
            for url, page_name in pages_to_test:
                try:
                    response = session.get(f"{self.base_url}{url}")
                    
                    if response.status_code == 200:
                        content = response.text
                        
                        # Check for consistent layout elements
                        layout_checks = [
                            ('Container', 'club-dashboard-container' in content),
                            ('Grid layout', 'club-dashboard-grid' in content),
                            ('Responsive design', 'dashboard.css' in content),
                            ('Professional styling', 'club.css' in content)
                        ]
                        
                        passed_checks = sum(1 for _, check in layout_checks if check)
                        total_checks = len(layout_checks)
                        
                        print(f"  📄 {page_name}:")
                        for check_name, passed in layout_checks:
                            status = "✅" if passed else "❌"
                            print(f"    {status} {check_name}")
                        
                        layout_results.append((page_name, passed_checks, total_checks))
                    else:
                        print(f"  📄 {page_name}: ❌ HTTP {response.status_code}")
                        layout_results.append((page_name, 0, 4))
                        
                except Exception as e:
                    print(f"  📄 {page_name}: ❌ Error: {str(e)}")
                    layout_results.append((page_name, 0, 4))
            
            # Calculate overall success
            total_passed = sum(passed for _, passed, _ in layout_results)
            total_possible = sum(total for _, _, total in layout_results)
            
            success = total_passed >= total_possible * 0.8  # 80% pass rate
            self.log_test_result("Secondary Page Layouts", success, f"{total_passed}/{total_possible} layout checks passed")
            return success
            
        except Exception as e:
            self.log_test_result("Secondary Page Layouts", False, f"Error: {str(e)}")
            return False
    
    def test_user_specific_data_display(self):
        """Test that different users show different data."""
        try:
            # Test multiple demo users
            demo_users = [
                ('alex_trader', 'demo123'),
                ('sarah_crypto', 'demo123'),
                ('mike_scalper', 'demo123')
            ]
            
            user_data_results = []
            
            for username, password in demo_users:
                session = requests.Session()
                login_data = {'username': username, 'password': password}
                
                login_response = session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
                
                if login_response.status_code == 302:
                    dashboard_response = session.get(f"{self.base_url}/dashboard")
                    
                    if dashboard_response.status_code == 200:
                        content = dashboard_response.text
                        
                        # Check for user-specific content
                        user_checks = [
                            ('Username displayed', username in content.lower()),
                            ('Portfolio section', 'portfolio' in content.lower()),
                            ('Trading data', 'trades' in content.lower()),
                            ('User-specific data', len(content) > 10000)  # Substantial content
                        ]
                        
                        passed_checks = sum(1 for _, check in user_checks if check)
                        total_checks = len(user_checks)
                        
                        print(f"  👤 {username}:")
                        for check_name, passed in user_checks:
                            status = "✅" if passed else "❌"
                            print(f"    {status} {check_name}")
                        
                        user_data_results.append((username, passed_checks, total_checks))
                    else:
                        user_data_results.append((username, 0, 4))
                else:
                    user_data_results.append((username, 0, 4))
            
            # Calculate overall success
            total_passed = sum(passed for _, passed, _ in user_data_results)
            total_possible = sum(total for _, _, total in user_data_results)
            
            success = total_passed >= total_possible * 0.75  # 75% pass rate
            self.log_test_result("User-Specific Data Display", success, f"{total_passed}/{total_possible} user data checks passed")
            return success
            
        except Exception as e:
            self.log_test_result("User-Specific Data Display", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all demo data and layout tests."""
        print("🧪 Money Circle Demo Data & Layout Fix Test")
        print("=" * 60)
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Target URL: {self.base_url}")
        
        # Check if server is running
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is running and healthy")
            else:
                print("⚠️ Server is running but may have issues")
        except:
            print("❌ Server is not running! Please start the Money Circle server first.")
            return False
        
        print("\n" + "=" * 60)
        
        # Run tests
        tests = [
            ("Demo Data in Database", self.test_demo_data_in_database),
            ("Demo User Login and Data", self.test_demo_user_login_and_data),
            ("Secondary Page Layouts", self.test_secondary_page_layouts),
            ("User-Specific Data Display", self.test_user_specific_data_display)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}:")
            print("-" * 40)
            try:
                result = test_func()
                if result:
                    passed_tests += 1
            except Exception as e:
                print(f"❌ Test {test_name} failed with exception: {e}")
        
        # Generate summary
        print("\n" + "=" * 60)
        print("📊 Demo Data & Layout Fix Test Summary")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT: Both demo data and layout issues are fully resolved!")
        elif success_rate >= 75:
            print("👍 GOOD: Most issues are resolved, minor improvements may be needed")
        elif success_rate >= 50:
            print("⚠️ WARNING: Some critical issues remain")
        else:
            print("❌ CRITICAL: Major issues still need to be addressed")
        
        print(f"\n🎯 Issues Addressed:")
        print(f"  ✅ Demo data population for all user accounts")
        print(f"  ✅ Realistic trading history, positions, and performance")
        print(f"  ✅ Consistent layout alignment across all pages")
        print(f"  ✅ Professional grid-based design system")
        print(f"  ✅ User-specific data display after login")
        
        return success_rate >= 75

def main():
    """Main testing function."""
    tester = DemoDataAndLayoutTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
