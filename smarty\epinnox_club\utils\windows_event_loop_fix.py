"""
Windows Event Loop Fix
Fixes aiodns SelectorEventLoop issues on Windows for CoinGecko integration
"""

import asyncio
import platform
import logging
import aiohttp
from typing import Optional

logger = logging.getLogger(__name__)


class WindowsEventLoopManager:
    """Manages Windows-specific event loop configuration."""

    @staticmethod
    def setup_windows_event_loop():
        """Setup proper event loop for Windows to fix aiodns issues."""
        if platform.system() == 'Windows':
            try:
                # Set the event loop policy to WindowsSelectorEventLoopPolicy
                # This fixes the aiodns SelectorEventLoop requirement on Windows
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
                logger.info("[EVENTLOOP] ✅ Windows SelectorEventLoop policy configured")
                return True
            except Exception as e:
                logger.error(f"[EVENTLOOP] Failed to set Windows event loop policy: {e}")
                return False
        return True

    @staticmethod
    def get_event_loop():
        """Get or create appropriate event loop for the platform."""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
            return loop
        except RuntimeError:
            # Create new event loop
            if platform.system() == 'Windows':
                loop = asyncio.WindowsSelectorEventLoop()
            else:
                loop = asyncio.new_event_loop()

            asyncio.set_event_loop(loop)
            return loop


class CoinGeckoClientFixed:
    """CoinGecko client with Windows aiodns fix."""

    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        # Ensure proper event loop is set
        WindowsEventLoopManager.setup_windows_event_loop()

        # Create session with DNS resolver configuration for Windows
        connector = None

        if platform.system() == 'Windows':
            try:
                # Try to create connector without aiodns
                import aiohttp.resolver

                # Use ThreadedResolver instead of aiodns
                resolver = aiohttp.resolver.ThreadedResolver()
                connector = aiohttp.TCPConnector(
                    resolver=resolver,
                    use_dns_cache=False,
                    family=0
                )
                logger.debug("[COINGECKO] Using ThreadedResolver for Windows")

            except Exception as e:
                logger.warning(f"[COINGECKO] ThreadedResolver failed: {e}")

                try:
                    # Fallback: basic connector without DNS optimization
                    connector = aiohttp.TCPConnector(use_dns_cache=False)
                    logger.debug("[COINGECKO] Using basic connector")
                except Exception as e2:
                    logger.warning(f"[COINGECKO] Basic connector failed: {e2}")
                    connector = None

        if connector is None:
            # Final fallback: default connector
            connector = aiohttp.TCPConnector()

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def get_simple_price(self, ids: str, vs_currencies: str = "usd",
                              include_24hr_change: bool = True,
                              include_24hr_vol: bool = True) -> dict:
        """Get simple price data from CoinGecko."""
        try:
            url = f"{self.base_url}/simple/price"
            params = {
                'ids': ids,
                'vs_currencies': vs_currencies,
                'include_24hr_change': str(include_24hr_change).lower(),
                'include_24hr_vol': str(include_24hr_vol).lower()
            }

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"[COINGECKO] Successfully fetched price data for {ids}")
                    return data
                else:
                    logger.warning(f"[COINGECKO] API error: {response.status}")
                    return {}

        except Exception as e:
            logger.error(f"[COINGECKO] Error fetching price data: {e}")
            return {}

    async def get_coins_markets(self, vs_currency: str = "usd",
                               ids: Optional[str] = None,
                               order: str = "market_cap_desc",
                               per_page: int = 100,
                               page: int = 1) -> list:
        """Get coins market data from CoinGecko."""
        try:
            url = f"{self.base_url}/coins/markets"
            params = {
                'vs_currency': vs_currency,
                'order': order,
                'per_page': per_page,
                'page': page
            }

            if ids:
                params['ids'] = ids

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"[COINGECKO] Successfully fetched market data")
                    return data
                else:
                    logger.warning(f"[COINGECKO] API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"[COINGECKO] Error fetching market data: {e}")
            return []


async def test_coingecko_fix():
    """Test the CoinGecko fix."""
    logger.info("[TEST] Testing CoinGecko Windows fix...")

    try:
        async with CoinGeckoClientFixed() as client:
            # Test simple price fetch
            data = await client.get_simple_price(
                ids="bitcoin,ethereum,binancecoin",
                vs_currencies="usd",
                include_24hr_change=True,
                include_24hr_vol=True
            )

            if data:
                logger.info(f"[TEST] ✅ CoinGecko fix successful. Fetched data for {len(data)} coins")
                for coin_id, coin_data in data.items():
                    price = coin_data.get('usd', 0)
                    change = coin_data.get('usd_24h_change', 0)
                    logger.info(f"[TEST] {coin_id}: ${price:,.2f} ({change:+.2f}%)")
                return True
            else:
                logger.error("[TEST] ❌ CoinGecko fix failed - no data returned")
                return False

    except Exception as e:
        logger.error(f"[TEST] ❌ CoinGecko fix test failed: {e}")
        return False


def initialize_windows_fixes():
    """Initialize all Windows-specific fixes."""
    logger.info("[INIT] Initializing Windows-specific fixes...")

    success = True

    # Setup event loop
    if not WindowsEventLoopManager.setup_windows_event_loop():
        success = False

    if success:
        logger.info("[INIT] ✅ Windows fixes initialized successfully")
    else:
        logger.error("[INIT] ❌ Some Windows fixes failed to initialize")

    return success


# Auto-initialize on import if on Windows
if platform.system() == 'Windows':
    initialize_windows_fixes()


if __name__ == "__main__":
    async def main():
        success = await test_coingecko_fix()
        if success:
            print("✅ CoinGecko Windows fix test passed")
        else:
            print("❌ CoinGecko Windows fix test failed")

    asyncio.run(main())
