"""
Database Schema Fixer
Comprehensive schema validation and repair for Money Circle platform
"""

import sqlite3
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseSchemaFixer:
    """Fix database schema inconsistencies and missing columns."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None
        
    def connect(self) -> bool:
        """Connect to database."""
        try:
            self.conn = sqlite3.connect(self.db_path, timeout=30.0)
            self.conn.row_factory = sqlite3.Row
            
            # Enable optimizations
            self.conn.execute("PRAGMA journal_mode=WAL")
            self.conn.execute("PRAGMA synchronous=NORMAL")
            self.conn.execute("PRAGMA foreign_keys=ON")
            
            return True
        except Exception as e:
            logger.error(f"[SCHEMA] Database connection failed: {e}")
            return False
    
    def get_table_columns(self, table_name: str) -> List[str]:
        """Get list of columns for a table."""
        try:
            cursor = self.conn.execute(f"PRAGMA table_info({table_name})")
            return [row[1] for row in cursor.fetchall()]
        except Exception:
            return []
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        try:
            cursor = self.conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return cursor.fetchone() is not None
        except Exception:
            return False
    
    def add_column_if_missing(self, table_name: str, column_name: str, column_def: str) -> bool:
        """Add column to table if it doesn't exist."""
        try:
            columns = self.get_table_columns(table_name)
            if column_name not in columns:
                self.conn.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_def}")
                logger.info(f"[SCHEMA] Added column {column_name} to {table_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"[SCHEMA] Failed to add column {column_name} to {table_name}: {e}")
            return False
    
    def fix_notifications_table(self):
        """Fix notifications table schema."""
        logger.info("[SCHEMA] Fixing notifications table...")
        
        # Ensure notifications table exists
        if not self.table_exists('notifications'):
            self.conn.execute("""
                CREATE TABLE notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    data TEXT,
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP,
                    expires_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            logger.info("[SCHEMA] Created notifications table")
        else:
            # Add missing columns
            self.add_column_if_missing('notifications', 'expires_at', 'expires_at TIMESTAMP')
            self.add_column_if_missing('notifications', 'read_at', 'read_at TIMESTAMP')
            self.add_column_if_missing('notifications', 'data', 'data TEXT')
    
    def fix_strategy_proposals_table(self):
        """Fix strategy_proposals table schema."""
        logger.info("[SCHEMA] Fixing strategy_proposals table...")
        
        if not self.table_exists('strategy_proposals'):
            self.conn.execute("""
                CREATE TABLE strategy_proposals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    creator_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    strategy_type TEXT DEFAULT 'general',
                    risk_level TEXT CHECK(risk_level IN ('low', 'medium', 'high')) DEFAULT 'medium',
                    expected_return REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    time_horizon TEXT DEFAULT 'medium_term',
                    min_investment REAL DEFAULT 1000.0,
                    performance_fee REAL DEFAULT 20.0,
                    management_fee REAL DEFAULT 2.0,
                    status TEXT CHECK(status IN ('proposed', 'active', 'paused', 'closed')) DEFAULT 'proposed',
                    votes_for INTEGER DEFAULT 0,
                    votes_against INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    activated_at TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            logger.info("[SCHEMA] Created strategy_proposals table")
        else:
            # Add missing columns
            self.add_column_if_missing('strategy_proposals', 'title', 'title TEXT NOT NULL DEFAULT ""')
            self.add_column_if_missing('strategy_proposals', 'strategy_type', 'strategy_type TEXT DEFAULT "general"')
            self.add_column_if_missing('strategy_proposals', 'is_active', 'is_active BOOLEAN DEFAULT FALSE')
            self.add_column_if_missing('strategy_proposals', 'max_drawdown', 'max_drawdown REAL DEFAULT 0.0')
    
    def fix_trading_performance_table(self):
        """Fix trading_performance table schema."""
        logger.info("[SCHEMA] Fixing trading_performance table...")
        
        if not self.table_exists('trading_performance'):
            self.conn.execute("""
                CREATE TABLE trading_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    portfolio_value REAL DEFAULT 0.0,
                    total_return REAL DEFAULT 0.0,
                    daily_return REAL DEFAULT 0.0,
                    win_rate REAL DEFAULT 0.0,
                    trade_size REAL DEFAULT 0.0,
                    trades_count INTEGER DEFAULT 0,
                    max_drawdown REAL DEFAULT 0.0,
                    sharpe_ratio REAL DEFAULT 0.0,
                    volatility REAL DEFAULT 0.0,
                    total_pnl REAL DEFAULT 0.0,
                    performance REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(user_id, date)
                )
            """)
            logger.info("[SCHEMA] Created trading_performance table")
        else:
            # Add missing columns
            self.add_column_if_missing('trading_performance', 'trade_size', 'trade_size REAL DEFAULT 0.0')
            self.add_column_if_missing('trading_performance', 'max_drawdown', 'max_drawdown REAL DEFAULT 0.0')
            self.add_column_if_missing('trading_performance', 'total_pnl', 'total_pnl REAL DEFAULT 0.0')
            self.add_column_if_missing('trading_performance', 'performance', 'performance REAL DEFAULT 0.0')
    
    def fix_member_profiles_table(self):
        """Fix member_profiles table schema."""
        logger.info("[SCHEMA] Fixing member_profiles table...")
        
        if not self.table_exists('member_profiles'):
            self.conn.execute("""
                CREATE TABLE member_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER UNIQUE NOT NULL,
                    display_name TEXT,
                    bio TEXT,
                    trading_style TEXT,
                    risk_tolerance TEXT CHECK(risk_tolerance IN ('conservative', 'moderate', 'aggressive')),
                    preferred_assets TEXT,
                    public_stats BOOLEAN DEFAULT TRUE,
                    joined_strategies INTEGER DEFAULT 0,
                    total_votes INTEGER DEFAULT 0,
                    reputation_score REAL DEFAULT 0.0,
                    total_pnl REAL DEFAULT 0.0,
                    performance REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            logger.info("[SCHEMA] Created member_profiles table")
        else:
            # Add missing columns
            self.add_column_if_missing('member_profiles', 'total_pnl', 'total_pnl REAL DEFAULT 0.0')
            self.add_column_if_missing('member_profiles', 'performance', 'performance REAL DEFAULT 0.0')
    
    def fix_user_sessions_table(self):
        """Fix user_sessions table schema."""
        logger.info("[SCHEMA] Fixing user_sessions table...")
        
        if not self.table_exists('user_sessions'):
            self.conn.execute("""
                CREATE TABLE user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_id TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    ip_address TEXT,
                    user_agent TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            logger.info("[SCHEMA] Created user_sessions table")
        else:
            # Add missing columns
            self.add_column_if_missing('user_sessions', 'expires_at', 'expires_at TIMESTAMP')
    
    def create_missing_indexes(self):
        """Create missing database indexes for performance."""
        logger.info("[SCHEMA] Creating missing indexes...")
        
        indexes = [
            ("idx_notifications_user_id", "CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications (user_id)"),
            ("idx_notifications_expires_at", "CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications (expires_at)"),
            ("idx_strategy_proposals_creator", "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_creator ON strategy_proposals (creator_id)"),
            ("idx_strategy_proposals_status", "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_status ON strategy_proposals (status)"),
            ("idx_trading_performance_user", "CREATE INDEX IF NOT EXISTS idx_trading_performance_user ON trading_performance (user_id)"),
            ("idx_trading_performance_date", "CREATE INDEX IF NOT EXISTS idx_trading_performance_date ON trading_performance (date)"),
            ("idx_user_sessions_user_id", "CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions (user_id)"),
            ("idx_user_sessions_expires", "CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions (expires_at)"),
        ]
        
        for index_name, sql in indexes:
            try:
                self.conn.execute(sql)
                logger.debug(f"[SCHEMA] Created index: {index_name}")
            except Exception as e:
                logger.warning(f"[SCHEMA] Failed to create index {index_name}: {e}")
    
    def run_comprehensive_fix(self) -> bool:
        """Run comprehensive schema fixes."""
        if not self.connect():
            return False
        
        try:
            logger.info("[SCHEMA] Starting comprehensive database schema fix...")
            
            # Fix all tables
            self.fix_notifications_table()
            self.fix_strategy_proposals_table()
            self.fix_trading_performance_table()
            self.fix_member_profiles_table()
            self.fix_user_sessions_table()
            
            # Create indexes
            self.create_missing_indexes()
            
            # Commit all changes
            self.conn.commit()
            
            logger.info("[SCHEMA] ✅ Database schema fix completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"[SCHEMA] Schema fix failed: {e}")
            if self.conn:
                self.conn.rollback()
            return False
        finally:
            if self.conn:
                self.conn.close()


def fix_database_schema(db_path: str) -> bool:
    """Fix database schema issues."""
    fixer = DatabaseSchemaFixer(db_path)
    return fixer.run_comprehensive_fix()


if __name__ == "__main__":
    # Run schema fix on main database
    db_path = "data/money_circle.db"
    success = fix_database_schema(db_path)
    if success:
        print("✅ Database schema fixed successfully")
    else:
        print("❌ Database schema fix failed")
