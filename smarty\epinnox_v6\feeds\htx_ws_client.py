#!/usr/bin/env python3
"""
HTX Futures WebSocket Client for Real-Time Market Data
Epinnox V6 - Standalone AI Strategy Tuner
"""

import asyncio
import json
import gzip
import logging
import time
from typing import Dict, List, Callable, Optional, Any
import websockets
from datetime import datetime

logger = logging.getLogger(__name__)

class HTXWebSocketClient:
    """
    Real-time HTX Futures WebSocket client for market data streaming.
    Handles connection management, subscriptions, and data parsing.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ws_url = config['htx_websocket']['url']
        self.channels = config['htx_websocket']['channels']
        self.symbols = config['symbols']['enabled']

        # Connection management
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = config['htx_websocket']['max_reconnect_attempts']
        self.reconnect_interval = config['htx_websocket']['reconnect_interval']

        # Data handlers
        self.trade_handler: Optional[Callable] = None
        self.depth_handler: Optional[Callable] = None
        self.error_handler: Optional[Callable] = None

        # Statistics
        self.stats = {
            'messages_received': 0,
            'trades_processed': 0,
            'depth_updates': 0,
            'connection_time': None,
            'last_message_time': None
        }

        logger.info("HTX WebSocket Client initialized")

    def set_trade_handler(self, handler: Callable[[Dict], None]):
        """Set handler for trade data."""
        self.trade_handler = handler
        logger.info("Trade handler registered")

    def set_depth_handler(self, handler: Callable[[Dict], None]):
        """Set handler for order book depth data."""
        self.depth_handler = handler
        logger.info("Depth handler registered")

    def set_error_handler(self, handler: Callable[[Exception], None]):
        """Set handler for connection errors."""
        self.error_handler = handler
        logger.info("Error handler registered")

    async def connect(self) -> bool:
        """
        Establish WebSocket connection to HTX Futures with fallback endpoints.
        Returns True if successful, False otherwise.
        """
        # 🔧 FIX: Try multiple HTX endpoints due to geo-blocking
        htx_endpoints = [
            "wss://api-usdt.linear.contract.huobi.pro/ws",  # Primary
            "wss://api-aws.huobi.pro/ws",                   # Alternative 1
            "wss://api.huobi.pro/ws",                       # Alternative 2
            "wss://api-cloud.huobi.co.kr/ws"                # Alternative 3 (Korea)
        ]

        for endpoint in htx_endpoints:
            try:
                logger.info(f"🔄 Trying HTX endpoint: {endpoint}")

                self.websocket = await websockets.connect(
                    endpoint,
                    ping_interval=self.config['htx_websocket']['ping_interval'],
                    ping_timeout=10,
                    close_timeout=10
                )

                self.is_connected = True
                self.reconnect_attempts = 0
                self.stats['connection_time'] = datetime.now()
                self.ws_url = endpoint  # Update to working endpoint

                logger.info(f"✅ Connected to HTX WebSocket successfully: {endpoint}")

                # Subscribe to all channels for all symbols
                await self._subscribe_to_channels()

                return True

            except Exception as e:
                logger.warning(f"⚠️ Failed to connect to {endpoint}: {e}")
                continue

        # All endpoints failed
        logger.error("❌ All HTX WebSocket endpoints failed - geo-blocking likely")
        self.is_connected = False
        if self.error_handler:
            try:
                if asyncio.iscoroutinefunction(self.error_handler):
                    await self.error_handler(Exception("All HTX endpoints failed"))
                else:
                    self.error_handler(Exception("All HTX endpoints failed"))
            except Exception:
                pass
        return False

    async def _subscribe_to_channels(self):
        """Subscribe to all configured channels for all symbols."""
        for symbol in self.symbols:
            # Convert symbol format (BTC-USDT -> btcusdt)
            htx_symbol = symbol.replace('-', '').lower()

            for channel in self.channels:
                if channel == "trade.detail":
                    sub_msg = {
                        "sub": f"market.{htx_symbol}.trade.detail",
                        "id": f"trade_{htx_symbol}_{int(time.time())}"
                    }
                elif channel == "depth.step0":
                    sub_msg = {
                        "sub": f"market.{htx_symbol}.depth.step0",
                        "id": f"depth_{htx_symbol}_{int(time.time())}"
                    }
                else:
                    continue

                await self._send_message(sub_msg)
                logger.info(f"📡 Subscribed to {channel} for {symbol}")

                # Small delay between subscriptions
                await asyncio.sleep(0.1)

    async def _send_message(self, message: Dict):
        """Send message to WebSocket."""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.send(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message: {e}")
                self.is_connected = False

    async def listen(self):
        """
        Main listening loop for WebSocket messages.
        Handles incoming data and routes to appropriate handlers.
        """
        if not self.is_connected or not self.websocket:
            logger.error("WebSocket not connected")
            return

        logger.info("🎧 Starting WebSocket message listener")

        try:
            async for message in self.websocket:
                await self._process_message(message)

        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.is_connected = False
            await self._handle_disconnect()

        except Exception as e:
            logger.error(f"Error in WebSocket listener: {e}")
            self.is_connected = False
            if self.error_handler:
                self.error_handler(e)

    async def _process_message(self, raw_message):
        """Process incoming WebSocket message."""
        try:
            # HTX sends gzipped messages
            if isinstance(raw_message, bytes):
                message = gzip.decompress(raw_message).decode('utf-8')
            else:
                message = raw_message

            data = json.loads(message)
            self.stats['messages_received'] += 1
            self.stats['last_message_time'] = datetime.now()

            # Handle ping/pong
            if 'ping' in data:
                pong_msg = {"pong": data['ping']}
                await self._send_message(pong_msg)
                return

            # Handle subscription confirmations
            if 'subbed' in data:
                logger.info(f"✅ Subscription confirmed: {data['subbed']}")
                return

            # Handle market data
            if 'ch' in data and 'tick' in data:
                await self._handle_market_data(data)

        except Exception as e:
            logger.error(f"Error processing message: {e}")

    async def _handle_market_data(self, data: Dict):
        """Route market data to appropriate handlers."""
        channel = data['ch']
        tick_data = data['tick']
        timestamp = data.get('ts', int(time.time() * 1000))

        # Extract symbol from channel (market.btcusdt.trade.detail -> BTC-USDT)
        symbol_part = channel.split('.')[1]
        symbol = self._normalize_symbol(symbol_part)

        if 'trade.detail' in channel and self.trade_handler:
            # Process trade data
            if 'data' in tick_data:
                for trade in tick_data['data']:
                    trade_data = {
                        'symbol': symbol,
                        'price': float(trade['price']),
                        'quantity': float(trade['amount']),
                        'side': trade['direction'],  # 'buy' or 'sell'
                        'timestamp': trade['ts'],
                        'trade_id': trade['id']
                    }

                    await self.trade_handler(trade_data)
                    self.stats['trades_processed'] += 1

        elif 'depth.step0' in channel and self.depth_handler:
            # Process order book depth data
            depth_data = {
                'symbol': symbol,
                'bids': [[float(bid[0]), float(bid[1])] for bid in tick_data.get('bids', [])],
                'asks': [[float(ask[0]), float(ask[1])] for ask in tick_data.get('asks', [])],
                'timestamp': timestamp
            }

            await self.depth_handler(depth_data)
            self.stats['depth_updates'] += 1

    def _normalize_symbol(self, htx_symbol: str) -> str:
        """Convert HTX symbol format to standard format."""
        # btcusdt -> BTC-USDT
        symbol_map = {
            'btcusdt': 'BTC-USDT',
            'ethusdt': 'ETH-USDT',
            'dogeusdt': 'DOGE-USDT',
            'solusdt': 'SOL-USDT',
            'adausdt': 'ADA-USDT'
        }
        return symbol_map.get(htx_symbol.lower(), htx_symbol.upper())

    async def _handle_disconnect(self):
        """Handle WebSocket disconnection and attempt reconnection."""
        self.is_connected = False

        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.warning(f"🔄 Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")

            await asyncio.sleep(self.reconnect_interval)

            if await self.connect():
                # Restart listening
                asyncio.create_task(self.listen())
            else:
                await self._handle_disconnect()
        else:
            logger.error("❌ Max reconnection attempts reached. Giving up.")
            if self.error_handler:
                self.error_handler(Exception("Max reconnection attempts reached"))

    async def disconnect(self):
        """Gracefully disconnect from WebSocket."""
        if self.websocket and self.is_connected:
            logger.info("🔌 Disconnecting from HTX WebSocket")
            await self.websocket.close()
            self.is_connected = False

    def get_stats(self) -> Dict[str, Any]:
        """Get connection and processing statistics."""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'subscribed_symbols': len(self.symbols),
            'subscribed_channels': len(self.channels)
        }
