/**
 * Enhanced Dashboard JavaScript for Epinnox V6 AI Strategy Tuner
 * Includes signal tracking, performance analysis, and chart integration
 */

// Global state
let dashboardState = {
    ws: null,
    currentSymbol: 'BTC-USDT',
    lastUpdateTime: 0,
    signalChart: null,
    pnlChart: null,
    isConnected: false,
    signalData: [],
    priceData: [],
    performanceData: {}
};

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Enhanced Dashboard...');
    
    initializeWebSocket();
    initializeEventListeners();
    initializeCharts();
    loadInitialData();
    
    console.log('✅ Dashboard initialization complete');
});

/**
 * WebSocket connection for real-time updates
 */
function initializeWebSocket() {
    const wsUrl = `ws://${window.location.host}/ws`;
    
    dashboardState.ws = new WebSocket(wsUrl);
    
    dashboardState.ws.onopen = function() {
        console.log('🔗 WebSocket connected');
        dashboardState.isConnected = true;
        updateConnectionStatus(true);
    };
    
    dashboardState.ws.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            handleRealtimeUpdate(data);
        } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
        }
    };
    
    dashboardState.ws.onclose = function() {
        console.log('🔌 WebSocket disconnected, attempting reconnection...');
        dashboardState.isConnected = false;
        updateConnectionStatus(false);
        
        // Attempt reconnection after 3 seconds
        setTimeout(initializeWebSocket, 3000);
    };
    
    dashboardState.ws.onerror = function(error) {
        console.error('❌ WebSocket error:', error);
    };
}

/**
 * Initialize event listeners for UI interactions
 */
function initializeEventListeners() {
    // Symbol selector
    const symbolSelector = document.getElementById('symbol-selector');
    if (symbolSelector) {
        symbolSelector.addEventListener('change', handleSymbolChange);
    }
    
    // Preset selector and apply button
    const presetSelector = document.getElementById('preset-selector');
    const applyPresetBtn = document.getElementById('apply-preset-btn');
    if (applyPresetBtn) {
        applyPresetBtn.addEventListener('click', handlePresetApply);
    }
    
    // Settings button
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettingsModal);
    }
    
    // Settings modal controls
    setupSettingsModal();
    
    // Keyboard shortcuts
    setupKeyboardShortcuts();
    
    console.log('🎛️ Event listeners initialized');
}

/**
 * Initialize Chart.js charts
 */
function initializeCharts() {
    // Initialize price chart with signals
    const priceChartCanvas = document.getElementById('signal-chart');
    if (priceChartCanvas && window.chartManager) {
        window.chartManager.initialize();
        console.log('📊 Charts initialized via ChartManager');
    } else {
        console.warn('⚠️ Chart canvas or ChartManager not found');
    }
}

/**
 * Load initial data from API
 */
async function loadInitialData() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        
        if (data) {
            handleRealtimeUpdate(data);
            console.log('📊 Initial data loaded');
        }
    } catch (error) {
        console.error('❌ Error loading initial data:', error);
    }
}

/**
 * Handle real-time data updates
 */
function handleRealtimeUpdate(data) {
    try {
        // Update current symbol if changed
        if (data.current_symbol && data.current_symbol !== dashboardState.currentSymbol) {
            dashboardState.currentSymbol = data.current_symbol;
            updateSymbolSelector(data.current_symbol);
        }
        
        // Update status indicator
        updateStatusIndicator(data.strategy_running);
        
        // Update all dashboard panels
        if (data.model_outputs) {
            updateModelOutputs(data.model_outputs);
        }
        
        if (data.recent_signals) {
            updateRecentSignals(data.recent_signals);
            updateSignalChart(data.recent_signals);
        }
        
        if (data.signal_timeline) {
            updateSignalTimeline(data.signal_timeline);
        }
        
        if (data.llm_decisions) {
            updateLLMDecisions(data.llm_decisions);
        }
        
        if (data.performance) {
            updatePerformanceMetrics(data.performance);
            dashboardState.performanceData = data.performance;
        }
        
        // Update charts if ChartManager is available
        if (window.chartManager && window.chartManager.isInitialized) {
            if (data.price_data) {
                window.chartManager.updatePriceChart(data.price_data, data.recent_signals || []);
            }
            
            if (data.pnl_data) {
                window.chartManager.updatePnLChart(data.pnl_data);
            }
        }
        
        dashboardState.lastUpdateTime = Date.now();
        
    } catch (error) {
        console.error('❌ Error handling real-time update:', error);
    }
}

/**
 * Update model outputs panel
 */
function updateModelOutputs(outputs) {
    const container = document.getElementById('model-outputs');
    if (!container) return;
    
    if (!outputs || Object.keys(outputs).length === 0) {
        container.innerHTML = '<div class="no-data">No model outputs available</div>';
        return;
    }
    
    container.innerHTML = '';
    
    Object.entries(outputs).forEach(([model, data]) => {
        const div = document.createElement('div');
        div.className = 'model-output';
        
        const confidence = data.confidence || 0;
        const signal = data.signal || data.action || 'N/A';
        
        div.innerHTML = `
            <span class="model-name">${model.toUpperCase()}</span>
            <span class="model-value">${signal}</span>
            <div class="confidence-bar">
                <div class="confidence-fill" style="width: ${confidence * 100}%"></div>
            </div>
        `;
        
        container.appendChild(div);
    });
}

/**
 * Update recent signals panel with enhanced tracking
 */
function updateRecentSignals(signals) {
    const container = document.getElementById('recent-signals');
    if (!container) return;
    
    if (!signals || signals.length === 0) {
        container.innerHTML = '<div class="no-data">No recent signals</div>';
        return;
    }
    
    container.innerHTML = '';
    
    signals.slice(0, 10).forEach(signal => {
        const div = document.createElement('div');
        
        // Add status class for styling
        const statusClass = signal.status_class || 'signal-pending';
        div.className = `signal-item ${statusClass}`;
        
        const action = signal.action || signal.direction || signal.signal || 'WAIT';
        const actionClass = action.toLowerCase().includes('long') ? 'signal-long' :
                          action.toLowerCase().includes('short') ? 'signal-short' : 'signal-wait';
        
        // Format P&L display
        const pnl = signal.pnl || signal.unrealized_pnl || 0;
        const pnlClass = pnl > 0 ? 'positive' : pnl < 0 ? 'negative' : 'neutral';
        const pnlText = pnl !== 0 ? `${pnl > 0 ? '+' : ''}$${pnl.toFixed(2)}` : '--';
        
        // Status display
        const status = signal.status_text || 'PENDING';
        const statusDisplayClass = status.toLowerCase();
        
        div.innerHTML = `
            <span class="signal-time">${signal.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
            <span class="signal-symbol">${signal.symbol || dashboardState.currentSymbol}</span>
            <span class="signal-action ${actionClass}">${action}</span>
            <span class="signal-confidence">${Math.round((signal.confidence || 0) * 100)}%</span>
            <span class="signal-status ${statusDisplayClass}">${status}</span>
            <span class="signal-pnl ${pnlClass}">${pnlText}</span>
        `;
        
        container.appendChild(div);
    });
}

/**
 * Update signal timeline
 */
function updateSignalTimeline(timeline) {
    const container = document.getElementById('signal-timeline');
    if (!container) return;
    
    if (!timeline || timeline.length === 0) {
        container.innerHTML = '<div class="no-data">No signal timeline data</div>';
        return;
    }
    
    container.innerHTML = '';
    
    timeline.slice(0, 15).forEach(signal => {
        const div = document.createElement('div');
        div.className = 'signal-item';
        
        const action = signal.action || signal.signal || 'WAIT';
        const actionClass = action.toLowerCase().includes('long') ? 'signal-long' :
                          action.toLowerCase().includes('short') ? 'signal-short' : 'signal-wait';
        
        const pnl = signal.pnl || 0;
        const pnlColor = pnl > 0 ? 'var(--accent-green)' : pnl < 0 ? 'var(--accent-red)' : 'var(--text-muted)';
        
        div.innerHTML = `
            <span class="signal-time">${signal.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
            <span class="signal-symbol">${signal.symbol || dashboardState.currentSymbol}</span>
            <span class="signal-action ${actionClass}">${action}</span>
            <span style="color: ${pnlColor}; font-size: 9px;">${pnl > 0 ? '+' : ''}${pnl.toFixed(2)}</span>
        `;
        
        container.appendChild(div);
    });
}

/**
 * Update LLM decisions panel
 */
function updateLLMDecisions(decisions) {
    const container = document.getElementById('llm-decisions');
    if (!container) return;
    
    if (!decisions || decisions.length === 0) {
        container.innerHTML = '<div class="no-data">No LLM decisions available</div>';
        return;
    }
    
    container.innerHTML = '';
    
    decisions.slice(0, 5).forEach(decision => {
        const div = document.createElement('div');
        div.className = 'llm-decision';
        
        const confidence = decision.confidence || 0;
        const reasoning = decision.reasoning || decision.message || decision.decision || 'Processing...';
        
        div.innerHTML = `
            <div class="llm-header">
                <span class="llm-timestamp">${decision.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
                <span class="llm-confidence">${Math.round(confidence * 100)}%</span>
            </div>
            <div class="llm-reasoning" onclick="toggleLLMReasoning(this)">${reasoning}</div>
        `;
        
        container.appendChild(div);
    });
}

/**
 * Update performance metrics with enhanced analytics
 */
function updatePerformanceMetrics(metrics) {
    const container = document.getElementById('performance-metrics');
    if (!container) return;
    
    if (!metrics) {
        container.innerHTML = '<div class="no-data">No performance data</div>';
        return;
    }
    
    container.innerHTML = '';
    
    // Enhanced metrics display
    const metricsToShow = [
        { label: 'Total Signals', value: metrics.total_signals || 0 },
        { label: 'Win Rate', value: `${(metrics.win_rate || 0).toFixed(1)}%` },
        { label: 'Total P&L', value: `$${(metrics.total_pnl || 0).toFixed(2)}` },
        { label: 'Unrealized P&L', value: `$${(metrics.current_unrealized_pnl || 0).toFixed(2)}` },
        { label: 'Active Positions', value: metrics.active_positions || 0 },
        { label: 'Profitable', value: metrics.profitable_signals || 0 },
        { label: 'Stopped Out', value: metrics.stopped_out_signals || 0 },
        { label: 'Pending', value: metrics.pending_signals || 0 }
    ];
    
    metricsToShow.forEach(metric => {
        const div = document.createElement('div');
        div.className = 'performance-metric';
        div.innerHTML = `
            <span class="metric-label">${metric.label}</span>
            <span class="metric-value">${metric.value}</span>
        `;
        container.appendChild(div);
    });
}

/**
 * Update signal chart with new data
 */
function updateSignalChart(signals) {
    if (window.chartManager && window.chartManager.isInitialized) {
        signals.forEach(signal => {
            if (signal.timestamp && signal.price) {
                const chartSignal = {
                    x: signal.timestamp * 1000, // Convert to milliseconds
                    y: signal.price,
                    direction: signal.action || signal.direction,
                    confidence: signal.confidence || 0,
                    status: signal.status || 'pending',
                    pnl: signal.pnl || 0,
                    signal_id: signal.signal_id || `${signal.symbol}_${Date.now()}`
                };
                
                window.chartManager.addSignal(chartSignal);
            }
        });
    }
}

/**
 * Handle symbol change
 */
async function handleSymbolChange() {
    const selector = document.getElementById('symbol-selector');
    const newSymbol = selector.value;
    
    try {
        const response = await fetch('/api/symbol/switch', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ symbol: newSymbol })
        });
        
        const result = await response.json();
        if (result.success) {
            dashboardState.currentSymbol = newSymbol;
            
            // Update chart symbol
            if (window.chartManager) {
                window.chartManager.updateSymbol(newSymbol);
            }
            
            console.log(`📊 Switched to ${newSymbol}`);
        }
    } catch (error) {
        console.error('❌ Error switching symbol:', error);
    }
}

/**
 * Handle preset application
 */
async function handlePresetApply() {
    const presetSelector = document.getElementById('preset-selector');
    const preset = presetSelector.value;
    
    if (!preset) return;
    
    try {
        const response = await fetch('/api/presets/apply', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ preset: preset, symbol: dashboardState.currentSymbol })
        });
        
        const result = await response.json();
        if (result.success) {
            console.log(`⚙️ Applied ${preset} preset`);
        }
    } catch (error) {
        console.error('❌ Error applying preset:', error);
    }
}

/**
 * Utility functions
 */
function updateConnectionStatus(connected) {
    // Update UI to show connection status
    const indicator = document.querySelector('.real-data-indicator');
    if (indicator) {
        indicator.textContent = connected ? 'LIVE' : 'OFFLINE';
        indicator.style.backgroundColor = connected ? 'var(--accent-green)' : 'var(--accent-red)';
    }
}

function updateSymbolSelector(symbol) {
    const selector = document.getElementById('symbol-selector');
    if (selector && selector.value !== symbol) {
        selector.value = symbol;
    }
}

function updateStatusIndicator(running) {
    const statusIndicator = document.getElementById('status-indicator');
    if (statusIndicator) {
        if (running) {
            statusIndicator.className = 'status-indicator status-running';
            statusIndicator.innerHTML = '<span>●</span><span>Running</span>';
        } else {
            statusIndicator.className = 'status-indicator status-stopped';
            statusIndicator.innerHTML = '<span>●</span><span>Stopped</span>';
        }
    }
}

function toggleLLMReasoning(element) {
    element.classList.toggle('expanded');
}

// Export for global access
window.dashboardState = dashboardState;
