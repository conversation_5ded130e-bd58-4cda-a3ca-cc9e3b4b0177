2025-06-07 15:22:47,207 - monitoring.alert_system - WARNING - Email functionality not available - install email packages if needed
2025-06-07 15:22:47,267 - __main__ - INFO - ================================================================================
2025-06-07 15:22:47,269 - __main__ - INFO - ================================================================================
2025-06-07 15:22:47,272 - __main__ - INFO - ================================================================================
2025-06-07 15:22:47,273 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,301 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,303 - storage.live_store - INFO - State loaded from cache file
2025-06-07 15:22:47,303 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-07 15:22:47,305 - execution.trade_executor - INFO - Risk-Aware Trade Executor initialized in live_trading mode
2025-06-07 15:22:47,305 - execution.order_logic - INFO - Adaptive Order Logic initialized
2025-06-07 15:22:47,305 - execution.execution_memory - INFO - Execution Memory initialized with 19 historical executions
2025-06-07 15:22:47,305 - execution.post_trade_reflection - INFO - Post-Trade Reflection system initialized
2025-06-07 15:22:47,809 - monitoring.account_tracker - INFO -    Account Balance: $15.0
2025-06-07 15:22:47,809 - monitoring.account_tracker - INFO -    Max Position: $1.0
2025-06-07 15:22:47,810 - monitoring.account_tracker - INFO -    Leverage: 20.0x
2025-06-07 15:22:47,810 - execution.execution_controller - INFO - Execution Intelligence Controller initialized
2025-06-07 15:22:47,812 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-07 15:22:47,814 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,837 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,844 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,855 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:47,864 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:50,896 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:22:50,941 - ccxt.base.exchange - WARNING - huobi requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-06-07 15:22:50,942 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x0000020B2DBEDF40>, 938728.968)]', '[(<aiohttp.client_proto.ResponseHandler object at 0x0000020B2DEF8940>, 938729.25)]']
connector: <aiohttp.connector.TCPConnector object at 0x0000020B2DD1CE20>
2025-06-07 15:22:50,943 - asyncio - ERROR - Fatal error on SSL transport
protocol: <asyncio.sslproto.SSLProtocol object at 0x0000020B2DC20EE0>
transport: <_ProactorSocketTransport fd=-1 read=<_OverlappedFuture cancelled>>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\sslproto.py", line 690, in _process_write_backlog
    self._transport.write(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 359, in write
    self._loop_writing(data=bytes(data))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 395, in _loop_writing
    self._write_fut = self._loop._proactor.send(self._sock, data)
AttributeError: 'NoneType' object has no attribute 'send'
2025-06-07 15:22:50,947 - asyncio - ERROR - Fatal error on SSL transport
protocol: <asyncio.sslproto.SSLProtocol object at 0x0000020B2DD35AC0>
transport: <_ProactorSocketTransport fd=-1 read=<_OverlappedFuture cancelled>>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\sslproto.py", line 690, in _process_write_backlog
    self._transport.write(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 359, in write
    self._loop_writing(data=bytes(data))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 395, in _loop_writing
    self._write_fut = self._loop._proactor.send(self._sock, data)
AttributeError: 'NoneType' object has no attribute 'send'
