#!/usr/bin/env python3
"""
Debug script to check orchestrator status and data flow.
"""

import sqlite3
import time
import json
from datetime import datetime
import subprocess
import sys

def check_database_activity():
    """Check recent database activity."""
    print("🔍 CHECKING DATABASE ACTIVITY")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/bus.db')
        cursor = conn.cursor()
        
        # Check total messages
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_messages = cursor.fetchone()[0]
        print(f"📊 Total messages in database: {total_messages:,}")
        
        # Check recent messages (last 5 minutes)
        recent_time = time.time() - 300
        cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (recent_time,))
        recent_messages = cursor.fetchone()[0]
        print(f"📈 Messages in last 5 minutes: {recent_messages}")
        
        # Check recent streams
        cursor.execute("""
            SELECT DISTINCT stream, COUNT(*) as count 
            FROM messages 
            WHERE ts > ? 
            GROUP BY stream 
            ORDER BY count DESC 
            LIMIT 10
        """, (recent_time,))
        
        recent_streams = cursor.fetchall()
        print(f"\n📡 Recent streams (last 5 minutes):")
        for stream, count in recent_streams:
            print(f"  • {stream}: {count} messages")
        
        # Check for AI model streams
        cursor.execute("""
            SELECT DISTINCT stream 
            FROM messages 
            WHERE ts > ? AND (
                stream LIKE '%model%' OR 
                stream LIKE '%llm%' OR 
                stream LIKE '%signal%' OR
                stream LIKE '%prediction%'
            )
            ORDER BY stream
        """, (recent_time,))
        
        ai_streams = cursor.fetchall()
        print(f"\n🧠 AI-related streams (last 5 minutes):")
        if ai_streams:
            for stream, in ai_streams:
                print(f"  • {stream}")
        else:
            print("  ❌ No AI-related streams found")
        
        # Check last message
        cursor.execute("SELECT MAX(ts), stream, payload FROM messages")
        last_message = cursor.fetchone()
        if last_message and last_message[0]:
            last_time = datetime.fromtimestamp(last_message[0])
            print(f"\n⏰ Last message: {last_time.strftime('%H:%M:%S')} on {last_message[1]}")
            
            # Show payload if it's small
            try:
                payload = json.loads(last_message[2]) if isinstance(last_message[2], str) else last_message[2]
                if len(str(payload)) < 200:
                    print(f"   Payload: {payload}")
            except:
                pass
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

def check_orchestrator_process():
    """Check if orchestrator process is running."""
    print("\n🔍 CHECKING ORCHESTRATOR PROCESS")
    print("=" * 50)
    
    try:
        # Check for python processes running orchestrator
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            python_processes = []
            
            for line in lines[1:]:  # Skip header
                if line.strip():
                    parts = line.split('","')
                    if len(parts) >= 2:
                        pid = parts[1].replace('"', '')
                        python_processes.append(pid)
            
            print(f"🐍 Python processes found: {len(python_processes)}")
            for pid in python_processes:
                print(f"  • PID: {pid}")
        
        # Try to find orchestrator specifically
        result = subprocess.run(
            ['wmic', 'process', 'where', 'name="python.exe"', 'get', 'ProcessId,CommandLine', '/format:csv'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            orchestrator_found = False
            
            for line in lines:
                if 'orchestrator.py' in line:
                    orchestrator_found = True
                    print(f"🎯 Orchestrator process found:")
                    print(f"   {line}")
            
            if not orchestrator_found:
                print("❌ No orchestrator.py process found")
        
    except Exception as e:
        print(f"❌ Error checking processes: {e}")

def test_orchestrator_startup():
    """Test orchestrator startup manually."""
    print("\n🔍 TESTING ORCHESTRATOR STARTUP")
    print("=" * 50)
    
    try:
        print("🚀 Starting orchestrator manually...")
        
        # Start orchestrator with debug output
        cmd = [
            sys.executable, "orchestrator.py",
            "--debug",
            "--strategy", "smart_integrated", 
            "--symbol", "BTC-USDT",
            "--testnet"
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Start process and capture initial output
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ Waiting for startup (10 seconds)...")
        
        # Wait a bit and check output
        time.sleep(10)
        
        if process.poll() is None:
            print("✅ Process is still running")
            
            # Try to get some output
            try:
                stdout, stderr = process.communicate(timeout=2)
                if stdout:
                    print("📤 STDOUT:")
                    print(stdout[:1000])  # First 1000 chars
                if stderr:
                    print("📤 STDERR:")
                    print(stderr[:1000])  # First 1000 chars
            except subprocess.TimeoutExpired:
                print("⏳ Process still running, no immediate output")
            
            # Terminate the test process
            process.terminate()
            print("🛑 Test process terminated")
            
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Process exited with code: {process.returncode}")
            if stdout:
                print("📤 STDOUT:")
                print(stdout)
            if stderr:
                print("📤 STDERR:")
                print(stderr)
        
    except Exception as e:
        print(f"❌ Error testing orchestrator: {e}")

def main():
    """Main diagnostic function."""
    print("🧠 AI STRATEGY TUNER - ORCHESTRATOR DIAGNOSTIC")
    print("=" * 70)
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all checks
    check_database_activity()
    check_orchestrator_process()
    test_orchestrator_startup()
    
    print("\n" + "=" * 70)
    print("🎯 DIAGNOSTIC COMPLETE")

if __name__ == "__main__":
    main()
