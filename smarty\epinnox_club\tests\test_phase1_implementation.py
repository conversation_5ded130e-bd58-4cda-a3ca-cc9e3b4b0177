#!/usr/bin/env python3
"""
Phase 1 Implementation Test Suite
Tests admin dashboard, role-based navigation, and database stability
"""

import asyncio
import aiohttp
import json
import sqlite3
import sys
from datetime import datetime

class Phase1TestSuite:
    def __init__(self):
        self.base_url = "http://localhost:8086"
        self.session = None
        self.test_results = []
        
    async def run_all_tests(self):
        """Run comprehensive Phase 1 tests."""
        print("🧪 Starting Phase 1 Implementation Test Suite")
        print("=" * 60)
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            # Test 1: Database Connection Stability
            await self.test_database_connections()
            
            # Test 2: Admin Dashboard Backend
            await self.test_admin_dashboard_backend()
            
            # Test 3: Role-Based Navigation APIs
            await self.test_role_based_navigation()
            
            # Test 4: User Management APIs
            await self.test_user_management_apis()
            
            # Test 5: System Health Monitoring
            await self.test_system_health()
            
        # Generate test report
        self.generate_test_report()
        
    async def test_database_connections(self):
        """Test database connection stability."""
        print("\n📊 Testing Database Connection Stability...")
        
        try:
            # Test direct database connection
            conn = sqlite3.connect('data/money_circle.db')
            cursor = conn.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            conn.close()
            
            self.test_results.append({
                'test': 'Database Connection',
                'status': 'PASS',
                'details': f'Successfully connected, found {user_count} users'
            })
            print(f"  ✅ Database connection: {user_count} users found")
            
        except Exception as e:
            self.test_results.append({
                'test': 'Database Connection',
                'status': 'FAIL',
                'details': str(e)
            })
            print(f"  ❌ Database connection failed: {e}")
    
    async def test_admin_dashboard_backend(self):
        """Test admin dashboard backend functionality."""
        print("\n🛡️ Testing Admin Dashboard Backend...")
        
        # Test admin dashboard access (should redirect to login)
        try:
            async with self.session.get(f"{self.base_url}/admin") as resp:
                if resp.status == 302:
                    self.test_results.append({
                        'test': 'Admin Dashboard Access Control',
                        'status': 'PASS',
                        'details': 'Correctly redirects unauthenticated users'
                    })
                    print("  ✅ Admin access control working")
                else:
                    self.test_results.append({
                        'test': 'Admin Dashboard Access Control',
                        'status': 'FAIL',
                        'details': f'Unexpected status: {resp.status}'
                    })
                    print(f"  ❌ Admin access control failed: {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Admin Dashboard Access Control',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Admin dashboard test error: {e}")
    
    async def test_role_based_navigation(self):
        """Test role-based navigation APIs."""
        print("\n🧭 Testing Role-Based Navigation APIs...")
        
        # Test current user API (should require authentication)
        try:
            async with self.session.get(f"{self.base_url}/api/user/current") as resp:
                if resp.status == 401:
                    self.test_results.append({
                        'test': 'Current User API Auth',
                        'status': 'PASS',
                        'details': 'Correctly requires authentication'
                    })
                    print("  ✅ Current user API authentication working")
                else:
                    self.test_results.append({
                        'test': 'Current User API Auth',
                        'status': 'FAIL',
                        'details': f'Unexpected status: {resp.status}'
                    })
                    print(f"  ❌ Current user API auth failed: {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Current User API Auth',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Current user API test error: {e}")
        
        # Test notification count API (should require authentication)
        try:
            async with self.session.get(f"{self.base_url}/api/notifications/count") as resp:
                if resp.status == 401:
                    self.test_results.append({
                        'test': 'Notification Count API Auth',
                        'status': 'PASS',
                        'details': 'Correctly requires authentication'
                    })
                    print("  ✅ Notification count API authentication working")
                else:
                    self.test_results.append({
                        'test': 'Notification Count API Auth',
                        'status': 'FAIL',
                        'details': f'Unexpected status: {resp.status}'
                    })
                    print(f"  ❌ Notification count API auth failed: {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Notification Count API Auth',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Notification count API test error: {e}")
    
    async def test_user_management_apis(self):
        """Test user management API endpoints."""
        print("\n👥 Testing User Management APIs...")
        
        # Test get users API (should require admin)
        try:
            async with self.session.get(f"{self.base_url}/api/admin/users") as resp:
                if resp.status == 403:
                    self.test_results.append({
                        'test': 'User Management API Auth',
                        'status': 'PASS',
                        'details': 'Correctly requires admin access'
                    })
                    print("  ✅ User management API authentication working")
                else:
                    self.test_results.append({
                        'test': 'User Management API Auth',
                        'status': 'FAIL',
                        'details': f'Unexpected status: {resp.status}'
                    })
                    print(f"  ❌ User management API auth failed: {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'User Management API Auth',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ User management API test error: {e}")
    
    async def test_system_health(self):
        """Test system health monitoring."""
        print("\n🔧 Testing System Health Monitoring...")
        
        # Test server responsiveness
        try:
            start_time = datetime.now()
            async with self.session.get(f"{self.base_url}/") as resp:
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds() * 1000
                
                if resp.status == 200 and response_time < 1000:
                    self.test_results.append({
                        'test': 'Server Responsiveness',
                        'status': 'PASS',
                        'details': f'Response time: {response_time:.2f}ms'
                    })
                    print(f"  ✅ Server responsive: {response_time:.2f}ms")
                else:
                    self.test_results.append({
                        'test': 'Server Responsiveness',
                        'status': 'FAIL',
                        'details': f'Status: {resp.status}, Time: {response_time:.2f}ms'
                    })
                    print(f"  ❌ Server responsiveness issue: {resp.status}, {response_time:.2f}ms")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Server Responsiveness',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Server responsiveness test error: {e}")
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        print("\n" + "=" * 60)
        print("📋 PHASE 1 IMPLEMENTATION TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Errors: {error_tests} ⚠️")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDetailed Results:")
        print("-" * 40)
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌" if result['status'] == 'FAIL' else "⚠️"
            print(f"{status_icon} {result['test']}: {result['status']}")
            print(f"   {result['details']}")
        
        # Overall assessment
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED - Phase 1 implementation complete!")
        elif passed_tests >= total_tests * 0.8:
            print("\n✅ MOSTLY SUCCESSFUL - Phase 1 implementation nearly complete")
        else:
            print("\n⚠️ NEEDS ATTENTION - Phase 1 implementation requires fixes")

async def main():
    """Run the test suite."""
    test_suite = Phase1TestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
