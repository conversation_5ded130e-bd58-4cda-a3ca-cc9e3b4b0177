#!/usr/bin/env python3
"""
Risk-Aware Trade Executor
Executes or simulates trades with position sizing tied to confidence, conviction_score, market_regime_factor
"""

import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ExecutionMode(Enum):
    SIMULATION = "simulation"
    PAPER_TRADING = "paper_trading"
    LIVE_TRADING = "live_trading"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LIMIT = "stop_limit"
    ICEBERG = "iceberg"

@dataclass
class TradeExecution:
    """Represents a trade execution result."""
    symbol: str
    action: str
    size: float
    entry_price: float
    execution_price: float
    slippage: float
    latency_ms: float
    order_type: str
    confidence: float
    conviction_score: int
    market_regime: str
    timestamp: float
    execution_id: str
    status: str  # 'filled', 'partial', 'rejected', 'pending'
    fill_quality: float  # 0-1 score based on slippage and timing

class RiskAwareTradeExecutor:
    """
    Executes trades with intelligent position sizing and risk management
    based on AI confidence, conviction scores, and market regime factors.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.execution_config = config.get('execution', {})
        self.mode = ExecutionMode(self.execution_config.get('mode', 'simulation'))

        # Risk parameters
        self.max_position_size = self.execution_config.get('max_position_size', 1000.0)
        self.base_position_size = self.execution_config.get('base_position_size', 100.0)
        self.confidence_multiplier = self.execution_config.get('confidence_multiplier', 2.0)
        self.conviction_multiplier = self.execution_config.get('conviction_multiplier', 1.5)
        self.regime_multipliers = self.execution_config.get('regime_multipliers', {
            'trending_up': 1.2,
            'trending_down': 1.2,
            'ranging': 0.8,
            'high_volatility': 0.6,
            'unknown': 0.5
        })

        # Execution tracking
        self.active_positions = {}
        self.execution_history = []
        self.total_executed_value = 0.0

        logger.info(f"Risk-Aware Trade Executor initialized in {self.mode.value} mode")

    async def execute_decision(self, decision: Dict[str, Any], market_data: Dict[str, Any]) -> Optional[TradeExecution]:
        """
        Execute a trading decision with risk-aware position sizing.

        Args:
            decision: LLM trading decision with confidence, conviction, etc.
            market_data: Current market data for execution

        Returns:
            TradeExecution object if executed, None if skipped
        """
        try:
            symbol = decision.get('symbol', 'BTC-USDT')
            action = decision.get('action', decision.get('final_decision', 'WAIT'))
            confidence = decision.get('confidence', 0.0)
            conviction_score = decision.get('conviction_score', 1)
            market_regime = decision.get('market_regime', 'unknown')

            # Skip WAIT decisions
            if action.upper() == 'WAIT':
                logger.debug(f"Skipping WAIT decision for {symbol}")
                return None

            # Calculate position size based on risk factors
            position_size = self._calculate_position_size(
                confidence, conviction_score, market_regime, symbol
            )

            if position_size <= 0:
                logger.warning(f"Position size too small for {symbol}: {position_size}")
                return None

            # Get current price and determine order type
            current_price = market_data.get('last_price', 0.0)
            if current_price <= 0:
                logger.error(f"Invalid price for {symbol}: {current_price}")
                return None

            # Execute the trade
            execution = await self._execute_trade(
                symbol=symbol,
                action=action,
                size=position_size,
                current_price=current_price,
                decision=decision,
                market_data=market_data
            )

            if execution:
                self.execution_history.append(execution)
                self.total_executed_value += execution.size * execution.execution_price
                logger.info(f"✅ Trade executed: {execution.symbol} {execution.action} "
                          f"{execution.size:.2f} @ ${execution.execution_price:.2f}")

            return execution

        except Exception as e:
            logger.error(f"Error executing decision: {e}")
            return None

    def _calculate_position_size(self, confidence: float, conviction_score: int,
                               market_regime: str, symbol: str) -> float:
        """Calculate position size based on risk factors."""
        try:
            # Defensive checks for None values
            if confidence is None:
                logger.warning("Confidence is None, using default 0.5")
                confidence = 0.5

            if conviction_score is None:
                logger.warning("Conviction score is None, using default 3")
                conviction_score = 3

            if market_regime is None:
                logger.warning("Market regime is None, using default 'unknown'")
                market_regime = 'unknown'

            # Clamp values to valid ranges
            confidence = max(0.0, min(1.0, confidence))
            conviction_score = max(1, min(5, conviction_score))

            # Start with base position size
            size = self.base_position_size

            # Apply confidence multiplier (0-1 confidence -> 0.5-1.5x multiplier)
            confidence_factor = 0.5 + (confidence * self.confidence_multiplier)
            size *= confidence_factor

            # Apply conviction multiplier (1-5 conviction -> 0.8-1.4x multiplier)
            conviction_factor = 0.8 + ((conviction_score - 1) / 4) * 0.6
            size *= conviction_factor

            # Apply market regime multiplier with defensive check
            regime_factor = self.regime_multipliers.get(market_regime, 0.5)
            regime_factor = regime_factor if regime_factor is not None else 0.5
            size *= regime_factor

            # Check position limits
            current_position = self.active_positions.get(symbol, 0.0)
            current_position = current_position if current_position is not None else 0.0
            max_additional = self.max_position_size - abs(current_position)
            size = min(size, max_additional)

            # Minimum size check
            min_size = self.execution_config.get('min_position_size', 10.0)
            if size < min_size:
                return 0.0

            # Ensure size is positive
            size = max(0.0, size)

            logger.debug(f"Position size calculation for {symbol}: "
                        f"base={self.base_position_size}, confidence={confidence_factor:.2f}, "
                        f"conviction={conviction_factor:.2f}, regime={regime_factor:.2f}, "
                        f"final={size:.2f}")

            return size

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

    async def _execute_trade(self, symbol: str, action: str, size: float,
                           current_price: float, decision: Dict[str, Any],
                           market_data: Dict[str, Any]) -> Optional[TradeExecution]:
        """Execute the actual trade based on mode."""
        try:
            start_time = time.time()
            execution_id = f"{symbol}_{int(time.time() * 1000)}"

            if self.mode == ExecutionMode.SIMULATION:
                # Simulate execution with realistic slippage
                execution = await self._simulate_execution(
                    symbol, action, size, current_price, decision, market_data, execution_id
                )
            elif self.mode == ExecutionMode.PAPER_TRADING:
                # Paper trading with more realistic fills
                execution = await self._paper_trade_execution(
                    symbol, action, size, current_price, decision, market_data, execution_id
                )
            elif self.mode == ExecutionMode.LIVE_TRADING:
                # Live trading execution (placeholder for real exchange integration)
                execution = await self._live_trade_execution(
                    symbol, action, size, current_price, decision, market_data, execution_id
                )
            else:
                logger.error(f"Unknown execution mode: {self.mode}")
                return None

            if execution:
                execution.latency_ms = (time.time() - start_time) * 1000
                execution.fill_quality = self._calculate_fill_quality(execution)

                # Update active positions
                if execution.status == 'filled':
                    position_change = execution.size if action.upper() in ['LONG', 'BUY'] else -execution.size
                    self.active_positions[symbol] = self.active_positions.get(symbol, 0.0) + position_change

            return execution

        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    async def _simulate_execution(self, symbol: str, action: str, size: float,
                                current_price: float, decision: Dict[str, Any],
                                market_data: Dict[str, Any], execution_id: str) -> TradeExecution:
        """Simulate trade execution with realistic slippage."""
        try:
            # Defensive checks for None values
            if current_price is None or current_price <= 0:
                logger.warning(f"Invalid current price: {current_price}, using default")
                current_price = 50000.0  # Default BTC price

            if size is None or size <= 0:
                logger.warning(f"Invalid size: {size}, skipping simulation")
                return None

            # Simulate slippage based on market conditions with defensive checks
            volatility = market_data.get('volatility', 0.01)
            volatility = volatility if volatility is not None else 0.01
            volatility = max(0.0, min(1.0, volatility))  # Clamp volatility

            volume = market_data.get('volume_24h', 1000000)
            volume = volume if volume is not None else 1000000
            volume = max(1000, volume)  # Ensure minimum volume

            # Higher volatility and lower volume = more slippage
            base_slippage = 0.0005  # 0.05%
            volatility_slippage = volatility * 0.1
            volume_slippage = max(0, (1000000 - volume) / 10000000 * 0.001)

            total_slippage = base_slippage + volatility_slippage + volume_slippage
            total_slippage = min(total_slippage, 0.01)  # Cap at 1%

            # Apply slippage direction based on action
            slippage_direction = 1 if action.upper() in ['LONG', 'BUY'] else -1
            execution_price = current_price * (1 + (total_slippage * slippage_direction))

            # Simulate small delay
            await asyncio.sleep(0.01)

            return TradeExecution(
                symbol=symbol,
                action=action,
                size=size,
                entry_price=current_price,
                execution_price=execution_price,
                slippage=total_slippage,
                latency_ms=0,  # Will be set by caller
                order_type=OrderType.MARKET.value,
                confidence=decision.get('confidence', 0.0),
                conviction_score=decision.get('conviction_score', 1),
                market_regime=decision.get('market_regime', 'unknown'),
                timestamp=time.time(),
                execution_id=execution_id,
                status='filled',
                fill_quality=0  # Will be calculated by caller
            )

        except Exception as e:
            logger.error(f"Error in simulation execution: {e}")
            return None

    async def _paper_trade_execution(self, symbol: str, action: str, size: float,
                                   current_price: float, decision: Dict[str, Any],
                                   market_data: Dict[str, Any], execution_id: str) -> TradeExecution:
        """Paper trading with more realistic market impact."""
        try:
            # More sophisticated slippage model for paper trading
            market_impact = self._calculate_market_impact(size, market_data)
            execution_price = current_price * (1 + market_impact)

            # Simulate partial fills for large orders
            fill_ratio = 1.0
            if size > self.execution_config.get('large_order_threshold', 500.0):
                fill_ratio = 0.7 + (0.3 * (1 - min(size / 1000, 1)))

            filled_size = size * fill_ratio
            status = 'filled' if fill_ratio >= 0.95 else 'partial'

            await asyncio.sleep(0.05)  # Slightly longer delay

            return TradeExecution(
                symbol=symbol,
                action=action,
                size=filled_size,
                entry_price=current_price,
                execution_price=execution_price,
                slippage=abs(execution_price - current_price) / current_price,
                latency_ms=0,
                order_type=OrderType.LIMIT.value if size > 200 else OrderType.MARKET.value,
                confidence=decision.get('confidence', 0.0),
                conviction_score=decision.get('conviction_score', 1),
                market_regime=decision.get('market_regime', 'unknown'),
                timestamp=time.time(),
                execution_id=execution_id,
                status=status,
                fill_quality=0
            )

        except Exception as e:
            logger.error(f"Error in paper trade execution: {e}")
            return None

    async def _live_trade_execution(self, symbol: str, action: str, size: float,
                                  current_price: float, decision: Dict[str, Any],
                                  market_data: Dict[str, Any], execution_id: str) -> TradeExecution:
        """Live trading execution (placeholder for real exchange integration)."""
        logger.warning("Live trading not implemented - falling back to paper trading")
        return await self._paper_trade_execution(
            symbol, action, size, current_price, decision, market_data, execution_id
        )

    def _calculate_market_impact(self, size: float, market_data: Dict[str, Any]) -> float:
        """Calculate market impact based on order size and market conditions."""
        try:
            # Defensive checks for None values
            if size is None or size <= 0:
                logger.warning(f"Invalid size for market impact: {size}")
                return 0.001

            volume_24h = market_data.get('volume_24h', 1000000)
            volume_24h = volume_24h if volume_24h is not None else 1000000
            volume_24h = max(1000, volume_24h)  # Ensure minimum volume

            # Calculate order ratio with defensive checks
            minute_volume = volume_24h / 24 / 60
            minute_volume = max(1, minute_volume)  # Prevent division by zero
            order_ratio = size / minute_volume

            # Square root impact model
            impact = 0.001 * (order_ratio ** 0.5)
            return min(impact, 0.005)  # Cap at 0.5%

        except Exception as e:
            logger.error(f"Error calculating market impact: {e}")
            return 0.001

    def _calculate_fill_quality(self, execution: TradeExecution) -> float:
        """Calculate fill quality score (0-1) based on slippage and timing."""
        try:
            # Quality decreases with slippage and latency
            slippage_penalty = min(execution.slippage * 100, 1.0)  # 0-1 based on slippage %
            latency_penalty = min(execution.latency_ms / 1000, 0.5)  # 0-0.5 based on latency

            quality = 1.0 - slippage_penalty - latency_penalty
            return max(quality, 0.0)

        except Exception as e:
            logger.error(f"Error calculating fill quality: {e}")
            return 0.5

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        try:
            if not self.execution_history:
                return {'total_executions': 0}

            total_executions = len(self.execution_history)
            avg_slippage = sum(e.slippage for e in self.execution_history) / total_executions
            avg_latency = sum(e.latency_ms for e in self.execution_history) / total_executions
            avg_fill_quality = sum(e.fill_quality for e in self.execution_history) / total_executions

            filled_orders = [e for e in self.execution_history if e.status == 'filled']
            fill_rate = len(filled_orders) / total_executions if total_executions > 0 else 0

            return {
                'total_executions': total_executions,
                'fill_rate': fill_rate,
                'avg_slippage': avg_slippage,
                'avg_latency_ms': avg_latency,
                'avg_fill_quality': avg_fill_quality,
                'total_executed_value': self.total_executed_value,
                'active_positions': dict(self.active_positions),
                'execution_mode': self.mode.value
            }

        except Exception as e:
            logger.error(f"Error getting execution stats: {e}")
            return {'error': str(e)}
