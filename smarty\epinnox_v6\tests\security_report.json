{"report_timestamp": 1749326659.2492373, "report_date": "2025-06-07T15:04:19.249237", "security_scan": {"timestamp": 1749326659.2492373, "scan_duration_ms": 0.0, "vulnerabilities": [{"type": "insecure_file_permissions", "severity": "medium", "description": "Security key file has insecure permissions", "recommendation": "Set file permissions to 600 (owner read/write only)"}], "recommendations": [{"type": "ip_whitelist", "priority": "medium", "description": "Consider enabling IP whitelisting for production", "action": "Configure trusted IP addresses"}], "security_score": 90, "status": "excellent"}, "security_status": {"timestamp": 1749326659.2492373, "encryption_enabled": true, "rate_limiting_enabled": true, "audit_logging_enabled": true, "active_sessions": 1, "blocked_ips": 1, "recent_events_1h": 6, "critical_events_1h": 0, "warning_events_1h": 5, "total_security_events": 6, "rate_limit_buckets": 1, "security_config": {"api_key_encryption": true, "rate_limiting_enabled": true, "ip_whitelist_enabled": false, "session_timeout_minutes": 30, "max_login_attempts": 3, "lockout_duration_minutes": 10, "password_min_length": 12, "require_2fa": false, "audit_logging": true, "secure_headers": true}}, "event_summary_24h": {"suspicious_input": 3, "rate_limit_exceeded": 1, "ip_blocked": 1, "session_created": 1}, "recent_events": [{"timestamp": 1749326659.2377274, "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(<script|javascript:|vbscript:|onload=|onerror=)", "input": "<script>alert('xss')</script>"}, "severity": "warning", "resolved": false}, {"timestamp": 1749326659.2387335, "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(union|select|insert|delete|drop|create|alter)", "input": "'; DROP TABLE users; --"}, "severity": "warning", "resolved": false}, {"timestamp": 1749326659.2397327, "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(\\.\\.\\/|\\.\\.\\\\)", "input": "../../../etc/passwd"}, "severity": "warning", "resolved": false}, {"timestamp": 1749326659.2417321, "event_type": "rate_limit_exceeded", "source_ip": "*************", "user_agent": "unknown", "endpoint": "/api/test", "details": {"limit": {"requests": 100, "window": 60}, "current_count": 100}, "severity": "warning", "resolved": false}, {"timestamp": 1749326659.2427323, "event_type": "ip_blocked", "source_ip": "*******", "user_agent": "system", "endpoint": "security_action", "details": {"duration_minutes": 1, "block_until": 1749326719.2427323}, "severity": "warning", "resolved": false}, {"timestamp": 1749326659.243733, "event_type": "session_created", "source_ip": "127.0.0.1", "user_agent": "unknown", "endpoint": "session_management", "details": {"user_id": "test_user", "session_id": "vzW5oR6A..."}, "severity": "info", "resolved": false}], "recommendations": []}