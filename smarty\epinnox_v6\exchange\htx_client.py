#!/usr/bin/env python3
"""
HTX API Client for Live Trading
Handles USDT-M Futures trading operations
"""

import os
import time
import hmac
import hashlib
import base64
import json
import logging
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode
import aiohttp
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)

class HTXClient:
    """HTX USDT-M Futures API Client for live trading."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.exchange_config = config.get('exchange', {})
        
        # API Configuration
        self.api_key = os.getenv('HTX_API_KEY') or self.exchange_config.get('api_key', '')
        self.api_secret = os.getenv('HTX_API_SECRET') or self.exchange_config.get('api_secret', '')
        self.passphrase = os.getenv('HTX_PASSPHRASE') or self.exchange_config.get('passphrase', '')
        
        # Endpoints
        self.rest_url = self.exchange_config.get('rest_url', 'https://api-usdt.linear.contract.huobi.pro')
        self.ws_url = self.exchange_config.get('ws_url', 'wss://api-usdt.linear.contract.huobi.pro/ws')
        
        # Trading Configuration
        self.account_balance = float(os.getenv('ACCOUNT_BALANCE', '5.0'))
        self.max_position_size = float(os.getenv('MAX_POSITION_SIZE', '4.0'))
        self.trading_symbol = os.getenv('TRADING_SYMBOL', 'DOGE-USDT')
        
        # Safety checks
        if not self.api_key or not self.api_secret:
            logger.warning("⚠️ HTX API credentials not configured - live trading disabled")
            self.live_trading_enabled = False
        else:
            self.live_trading_enabled = True
            logger.info(f"🔑 HTX API client initialized for live trading: {self.trading_symbol}")
        
        # Session for HTTP requests
        self.session = None
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def _generate_signature(self, method: str, path: str, params: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate HTX API signature."""
        try:
            timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')
            
            # Create signature string
            if params:
                query_string = urlencode(sorted(params.items()))
                signature_string = f"{method}\n{self.rest_url.replace('https://', '')}\n{path}\n{query_string}"
            else:
                signature_string = f"{method}\n{self.rest_url.replace('https://', '')}\n{path}\n"
            
            # Generate signature
            signature = base64.b64encode(
                hmac.new(
                    self.api_secret.encode('utf-8'),
                    signature_string.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')
            
            return {
                'AccessKeyId': self.api_key,
                'SignatureMethod': 'HmacSHA256',
                'SignatureVersion': '2',
                'Timestamp': timestamp,
                'Signature': signature
            }
            
        except Exception as e:
            logger.error(f"Error generating HTX signature: {e}")
            return {}
    
    async def _make_request(self, method: str, path: str, params: Dict[str, Any] = None, 
                           data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Make authenticated request to HTX API."""
        try:
            # Rate limiting
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                await asyncio.sleep(self.min_request_interval - time_since_last)
            
            # Generate signature
            auth_params = self._generate_signature(method, path, params)
            if not auth_params:
                return None
            
            # Prepare request
            url = f"{self.rest_url}{path}"
            
            if method == 'GET':
                if params:
                    auth_params.update(params)
                response = await self.session.get(url, params=auth_params)
            else:
                headers = {'Content-Type': 'application/json'}
                if data:
                    data = json.dumps(data)
                response = await self.session.request(method, url, params=auth_params, 
                                                    data=data, headers=headers)
            
            self.last_request_time = time.time()
            
            # Parse response
            response_data = await response.json()
            
            if response.status == 200:
                if response_data.get('status') == 'ok':
                    return response_data.get('data')
                else:
                    logger.error(f"HTX API error: {response_data}")
                    return None
            else:
                logger.error(f"HTX HTTP error {response.status}: {response_data}")
                return None
                
        except Exception as e:
            logger.error(f"HTX API request failed: {e}")
            return None
    
    async def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information."""
        try:
            if not self.live_trading_enabled:
                logger.warning("Live trading not enabled - cannot get account info")
                return None
            
            result = await self._make_request('GET', '/linear-swap-api/v1/swap_account_info')
            
            if result:
                logger.info(f"📊 Account info retrieved successfully")
                return result
            else:
                logger.error("Failed to get account info")
                return None
                
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    async def get_position_info(self, symbol: str = None) -> Optional[Dict[str, Any]]:
        """Get position information."""
        try:
            if not self.live_trading_enabled:
                return None
            
            symbol = symbol or self.trading_symbol
            params = {'contract_code': symbol}
            
            result = await self._make_request('GET', '/linear-swap-api/v1/swap_position_info', params)
            
            if result:
                logger.debug(f"📊 Position info for {symbol}: {result}")
                return result
            else:
                logger.warning(f"No position info for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting position info: {e}")
            return None
    
    async def place_order(self, symbol: str, side: str, size: float, 
                         order_type: str = 'limit', price: float = None) -> Optional[Dict[str, Any]]:
        """Place a trading order."""
        try:
            if not self.live_trading_enabled:
                logger.warning("⚠️ Live trading not enabled - order simulation only")
                return {
                    'order_id': f"sim_{int(time.time())}",
                    'status': 'simulated',
                    'symbol': symbol,
                    'side': side,
                    'size': size,
                    'price': price
                }
            
            # Validate order parameters
            if size < 0.5:  # Minimum position size
                logger.warning(f"Order size {size} below minimum 0.5 USD")
                return None
            
            if size > self.max_position_size:
                logger.warning(f"Order size {size} exceeds maximum {self.max_position_size} USD")
                size = self.max_position_size
            
            # Prepare order data
            order_data = {
                'contract_code': symbol,
                'direction': 'buy' if side.upper() in ['LONG', 'BUY'] else 'sell',
                'offset': 'open',
                'lever_rate': 1,  # 1x leverage for safety
                'volume': int(size),  # HTX expects integer volume
                'order_price_type': order_type
            }
            
            if price and order_type == 'limit':
                order_data['price'] = price
            
            logger.info(f"🚀 Placing {side} order: {symbol} {size} USD")
            
            result = await self._make_request('POST', '/linear-swap-api/v1/swap_order', data=order_data)
            
            if result:
                order_id = result.get('order_id')
                logger.info(f"✅ Order placed successfully: {order_id}")
                return {
                    'order_id': order_id,
                    'status': 'placed',
                    'symbol': symbol,
                    'side': side,
                    'size': size,
                    'price': price,
                    'timestamp': time.time()
                }
            else:
                logger.error(f"❌ Failed to place order: {symbol} {side} {size}")
                return None
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order."""
        try:
            if not self.live_trading_enabled:
                logger.info(f"📝 Simulated order cancellation: {order_id}")
                return True
            
            order_data = {
                'contract_code': symbol,
                'order_id': order_id
            }
            
            result = await self._make_request('POST', '/linear-swap-api/v1/swap_cancel', data=order_data)
            
            if result:
                logger.info(f"✅ Order cancelled: {order_id}")
                return True
            else:
                logger.error(f"❌ Failed to cancel order: {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    async def get_order_status(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status."""
        try:
            if not self.live_trading_enabled:
                return {
                    'order_id': order_id,
                    'status': 'simulated',
                    'filled_size': 0,
                    'remaining_size': 0
                }
            
            params = {
                'contract_code': symbol,
                'order_id': order_id
            }
            
            result = await self._make_request('GET', '/linear-swap-api/v1/swap_order_info', params)
            
            if result and len(result) > 0:
                order_info = result[0]
                return {
                    'order_id': order_info.get('order_id'),
                    'status': order_info.get('status'),
                    'filled_size': order_info.get('trade_volume', 0),
                    'remaining_size': order_info.get('volume', 0) - order_info.get('trade_volume', 0),
                    'price': order_info.get('price'),
                    'timestamp': order_info.get('created_at')
                }
            else:
                logger.warning(f"No order info found for {order_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting order status: {e}")
            return None
    
    async def close_position(self, symbol: str, size: float = None) -> Optional[Dict[str, Any]]:
        """Close a position."""
        try:
            # Get current position
            position_info = await self.get_position_info(symbol)
            
            if not position_info or len(position_info) == 0:
                logger.info(f"No position to close for {symbol}")
                return None
            
            position = position_info[0]
            current_size = abs(position.get('volume', 0))
            
            if current_size == 0:
                logger.info(f"No open position for {symbol}")
                return None
            
            # Determine close direction (opposite of current position)
            direction = position.get('direction')
            close_side = 'sell' if direction == 'buy' else 'buy'
            
            # Use specified size or close entire position
            close_size = size or current_size
            
            logger.info(f"🔄 Closing position: {symbol} {close_side} {close_size}")
            
            # Place closing order
            return await self.place_order(symbol, close_side, close_size, 'market')
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return None
    
    def get_trading_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        return {
            'live_trading_enabled': self.live_trading_enabled,
            'api_configured': bool(self.api_key and self.api_secret),
            'trading_symbol': self.trading_symbol,
            'account_balance': self.account_balance,
            'max_position_size': self.max_position_size,
            'rest_url': self.rest_url
        }
