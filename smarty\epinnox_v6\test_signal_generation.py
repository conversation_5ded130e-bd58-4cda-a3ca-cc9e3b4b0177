#!/usr/bin/env python3
"""
Test Signal Generation - Quick test to verify signal generation is working
"""

import asyncio
import logging
import yaml
import time
import random
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from models.smart_strategy import SmartStrategy
from feeds.trade_parser import MarketFeatures

async def test_signal_generation():
    """Test signal generation with mock data."""
    try:
        logger.info("🧪 Testing Signal Generation System")
        logger.info("=" * 50)
        
        # Load configuration
        config_path = Path(__file__).parent / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info("✅ Configuration loaded")
        
        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        smart_strategy = SmartStrategy(config, data_store)
        
        # Inject dependencies
        smart_strategy.set_exchange_client(execution_controller.htx_client)
        smart_strategy.set_account_tracker(execution_controller.account_tracker)
        execution_controller.set_smart_strategy(smart_strategy)
        
        logger.info("✅ Components initialized")
        
        # Start background tasks
        logger.info("🧠 Starting Strategic Intelligence...")
        await smart_strategy.start_background_tasks()
        
        logger.info("✅ Strategic Intelligence started")
        logger.info("🎯 Testing signal generation...")
        
        # Test signal generation with various market conditions
        test_scenarios = [
            {"name": "Bullish Market", "price_change": 0.015, "volume_ratio": 0.7, "rsi": 65},
            {"name": "Bearish Market", "price_change": -0.012, "volume_ratio": 0.3, "rsi": 35},
            {"name": "Neutral Market", "price_change": 0.002, "volume_ratio": 0.5, "rsi": 50},
            {"name": "High Volatility", "price_change": 0.025, "volume_ratio": 0.8, "rsi": 75},
            {"name": "Low Volatility", "price_change": 0.001, "volume_ratio": 0.4, "rsi": 45},
        ]
        
        for i, scenario in enumerate(test_scenarios):
            logger.info(f"\n📊 Test {i+1}: {scenario['name']}")
            logger.info("-" * 30)
            
            # Create market features for this scenario
            current_price = 0.179 + (scenario['price_change'] * 0.179)
            
            features = MarketFeatures(
                symbol="DOGE/USDT:USDT",
                timestamp=int(time.time() * 1000),
                last_price=current_price,
                price_change_1m=scenario['price_change'],
                price_velocity=scenario['price_change'] * 0.5,
                volume_1m=random.uniform(800, 1200),
                buy_volume_ratio=scenario['volume_ratio'],
                volume_delta=100.0,
                order_flow_imbalance=scenario['price_change'] * 2,
                trade_intensity=random.uniform(3.0, 8.0),
                avg_trade_size=100.0,
                rsi=scenario['rsi'],
                vwap=current_price * 0.999,
                volatility=abs(scenario['price_change']) + 0.01
            )
            
            # Store features
            data_store.store_features(features)
            
            # Process through strategy
            signal = await smart_strategy.process_features(features)
            
            if signal:
                logger.info(f"✅ SIGNAL GENERATED!")
                logger.info(f"   🎯 Action: {signal.action}")
                logger.info(f"   💪 Confidence: {signal.confidence:.2%}")
                logger.info(f"   📊 Score: {signal.score:.3f}")
                logger.info(f"   💰 Price: ${signal.price:.4f}")
                logger.info(f"   📝 Reasoning: {signal.reasoning}")
            else:
                logger.info(f"❌ No signal generated")
            
            # Wait between tests
            await asyncio.sleep(2)
        
        logger.info("\n🎉 Signal generation test completed!")
        
        # Shutdown
        smart_strategy.shutdown()
        data_store.shutdown()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_signal_generation())
