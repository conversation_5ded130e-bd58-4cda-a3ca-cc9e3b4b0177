#!/usr/bin/env python3
"""
Test WebSocket and Exchange API Fixes
Comprehensive test for WebSocket connections and exchange API credential issues
"""

import asyncio
import logging
import sys
import json
import aiohttp
import websockets
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8086"
WS_URL = "ws://localhost:8086"

class WebSocketAndAPITester:
    """Test WebSocket and API fixes."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = None
        self.session_cookie = None
        self.test_results = {}
        logger.info("WebSocket and API Tester initialized")
    
    async def run_all_tests(self):
        """Run all WebSocket and API tests."""
        try:
            logger.info("=" * 70)
            logger.info("WEBSOCKET AND EXCHANGE API FIXES TEST SUITE")
            logger.info("=" * 70)
            
            # Create session
            self.session = aiohttp.ClientSession()
            
            # Test 1: Login and get session
            await self._test_login_and_session()
            
            # Test 2: WebSocket connection
            await self._test_websocket_connection()
            
            # Test 3: Exchange credential setup
            await self._test_exchange_credential_setup()
            
            # Test 4: Exchange API integration
            await self._test_exchange_api_integration()
            
            # Test 5: Database connection persistence
            await self._test_database_persistence()
            
            # Test 6: Market data integration
            await self._test_market_data_integration()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        finally:
            if self.session:
                await self.session.close()
        
        return True
    
    async def _test_login_and_session(self):
        """Test login and session management."""
        logger.info("[TEST 1] Testing Login and Session Management...")
        
        try:
            # Login to get session
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            async with self.session.post(f"{BASE_URL}/login", data=login_data) as response:
                if response.status == 302:  # Redirect after successful login
                    # Get session cookie
                    cookies = response.cookies
                    if 'session_id' in cookies:
                        self.session_cookie = cookies['session_id'].value
                        logger.info(f"✅ Login successful, session: {self.session_cookie[:16]}...")
                        self.test_results['login_session'] = 'PASS'
                    else:
                        logger.error("❌ No session cookie received")
                        self.test_results['login_session'] = 'FAIL'
                else:
                    logger.error(f"❌ Login failed: {response.status}")
                    self.test_results['login_session'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Login test failed: {e}")
            self.test_results['login_session'] = 'FAIL'
    
    async def _test_websocket_connection(self):
        """Test WebSocket connection."""
        logger.info("[TEST 2] Testing WebSocket Connection...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping WebSocket test")
                self.test_results['websocket_connection'] = 'SKIP'
                return
            
            # Create WebSocket connection with session cookie
            headers = {
                'Cookie': f'session_id={self.session_cookie}'
            }
            
            try:
                async with websockets.connect(f"{WS_URL}/ws", extra_headers=headers) as websocket:
                    logger.info("✅ WebSocket connection established")
                    
                    # Wait for welcome message
                    try:
                        welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        welcome_data = json.loads(welcome_msg)
                        
                        if welcome_data.get('type') == 'welcome':
                            logger.info(f"✅ Welcome message received: {welcome_data.get('message')}")
                            
                            # Send ping message
                            ping_msg = {'type': 'ping'}
                            await websocket.send(json.dumps(ping_msg))
                            
                            # Wait for pong response
                            pong_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            pong_data = json.loads(pong_msg)
                            
                            if pong_data.get('type') == 'pong':
                                logger.info("✅ Ping/Pong test successful")
                                self.test_results['websocket_connection'] = 'PASS'
                            else:
                                logger.warning("⚠️ Unexpected pong response")
                                self.test_results['websocket_connection'] = 'PARTIAL'
                        else:
                            logger.warning("⚠️ Unexpected welcome message format")
                            self.test_results['websocket_connection'] = 'PARTIAL'
                            
                    except asyncio.TimeoutError:
                        logger.warning("⚠️ WebSocket message timeout")
                        self.test_results['websocket_connection'] = 'PARTIAL'
                        
            except Exception as ws_error:
                logger.error(f"❌ WebSocket connection failed: {ws_error}")
                self.test_results['websocket_connection'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"WebSocket test failed: {e}")
            self.test_results['websocket_connection'] = 'FAIL'
    
    async def _test_exchange_credential_setup(self):
        """Test exchange credential setup."""
        logger.info("[TEST 3] Testing Exchange Credential Setup...")
        
        try:
            # Run the credential setup script
            import subprocess
            result = subprocess.run([
                sys.executable, 'setup_real_credentials.py'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info("✅ Exchange credential setup successful")
                self.test_results['credential_setup'] = 'PASS'
            else:
                logger.error(f"❌ Credential setup failed: {result.stderr}")
                self.test_results['credential_setup'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Credential setup test failed: {e}")
            self.test_results['credential_setup'] = 'FAIL'
    
    async def _test_exchange_api_integration(self):
        """Test exchange API integration."""
        logger.info("[TEST 4] Testing Exchange API Integration...")
        
        try:
            from database.models import DatabaseManager
            from auth.user_manager import UserManager
            from exchanges.account_manager import ExchangeAccountManager
            
            # Initialize managers
            db_manager = DatabaseManager('data/money_circle.db')
            user_manager = UserManager(db_manager)
            exchange_manager = ExchangeAccountManager(db_manager)
            
            # Get epinnox user
            epinnox_user = user_manager.get_user_by_username('epinnox')
            if not epinnox_user:
                logger.error("❌ Epinnox user not found")
                self.test_results['exchange_api'] = 'FAIL'
                return
            
            user_id = epinnox_user.id
            exchanges = exchange_manager.get_user_exchanges(user_id)
            
            if not exchanges:
                logger.warning("⚠️ No exchange accounts found")
                self.test_results['exchange_api'] = 'PARTIAL'
                return
            
            success_count = 0
            total_count = len(exchanges)
            
            for exchange in exchanges:
                logger.info(f"🔍 Testing {exchange.exchange_name}...")
                
                client = exchange_manager.get_exchange_client(user_id, exchange.exchange_name)
                if client:
                    logger.info(f"✅ {exchange.exchange_name} client created successfully")
                    success_count += 1
                else:
                    logger.error(f"❌ {exchange.exchange_name} client creation failed")
            
            if success_count == total_count:
                logger.info("✅ All exchange clients created successfully")
                self.test_results['exchange_api'] = 'PASS'
            elif success_count > 0:
                logger.info(f"⚠️ {success_count}/{total_count} exchange clients successful")
                self.test_results['exchange_api'] = 'PARTIAL'
            else:
                logger.error("❌ No exchange clients created successfully")
                self.test_results['exchange_api'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Exchange API test failed: {e}")
            self.test_results['exchange_api'] = 'FAIL'
    
    async def _test_database_persistence(self):
        """Test database connection persistence."""
        logger.info("[TEST 5] Testing Database Connection Persistence...")
        
        try:
            from database.models import DatabaseManager
            from auth.user_manager import UserManager
            
            # Test multiple database operations
            db_manager = DatabaseManager('data/money_circle.db')
            user_manager = UserManager(db_manager)
            
            # Test 1: Connection ensure
            if db_manager.ensure_connection():
                logger.info("✅ Database connection ensure working")
            else:
                logger.error("❌ Database connection ensure failed")
                self.test_results['database_persistence'] = 'FAIL'
                return
            
            # Test 2: User operations
            user = user_manager.get_user_by_username('epinnox')
            if user:
                logger.info("✅ User retrieval working")
            else:
                logger.error("❌ User retrieval failed")
                self.test_results['database_persistence'] = 'FAIL'
                return
            
            # Test 3: Multiple operations
            for i in range(5):
                test_user = user_manager.get_user_by_id(user.id)
                if not test_user:
                    logger.error(f"❌ Database operation {i+1} failed")
                    self.test_results['database_persistence'] = 'FAIL'
                    return
            
            logger.info("✅ Database persistence tests passed")
            self.test_results['database_persistence'] = 'PASS'
            
        except Exception as e:
            logger.error(f"Database persistence test failed: {e}")
            self.test_results['database_persistence'] = 'FAIL'
    
    async def _test_market_data_integration(self):
        """Test market data integration."""
        logger.info("[TEST 6] Testing Market Data Integration...")
        
        try:
            # Test symbols API
            async with self.session.get(f"{BASE_URL}/api/symbols") as response:
                if response.status == 200:
                    symbols_data = await response.json()
                    if 'symbols' in symbols_data:
                        logger.info(f"✅ Symbols API working: {len(symbols_data['symbols'])} symbols")
                        symbols_result = 'PASS'
                    else:
                        logger.warning("⚠️ Symbols API missing symbols data")
                        symbols_result = 'PARTIAL'
                else:
                    logger.error(f"❌ Symbols API failed: {response.status}")
                    symbols_result = 'FAIL'
            
            # Test market ticker API
            async with self.session.get(f"{BASE_URL}/api/market/ticker/DOGEUSDT") as response:
                if response.status == 200:
                    ticker_data = await response.json()
                    if 'data' in ticker_data:
                        logger.info("✅ Market ticker API working")
                        ticker_result = 'PASS'
                    else:
                        logger.warning("⚠️ Market ticker API missing data")
                        ticker_result = 'PARTIAL'
                else:
                    logger.error(f"❌ Market ticker API failed: {response.status}")
                    ticker_result = 'FAIL'
            
            # Overall result
            if symbols_result == 'PASS' and ticker_result == 'PASS':
                self.test_results['market_data'] = 'PASS'
            elif symbols_result == 'PASS' or ticker_result == 'PASS':
                self.test_results['market_data'] = 'PARTIAL'
            else:
                self.test_results['market_data'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Market data test failed: {e}")
            self.test_results['market_data'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("WEBSOCKET AND API FIXES TEST RESULTS")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] WebSocket and API fixes working!")
            logger.info("\nThe Money Circle platform should now have:")
            logger.info("- Working WebSocket connections without 302 redirects")
            logger.info("- Successful exchange API authentication")
            logger.info("- Reliable database connectivity")
            logger.info("- Consistent market data integration")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical issues still need attention")
        
        logger.info("=" * 70)

async def main():
    """Main test function."""
    tester = WebSocketAndAPITester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 WebSocket and API fixes test suite completed!")
    else:
        print("\n❌ WebSocket and API fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
