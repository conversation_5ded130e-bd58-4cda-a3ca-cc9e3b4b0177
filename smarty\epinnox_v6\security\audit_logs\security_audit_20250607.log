{"timestamp": "2025-06-07T15:04:19.237727", "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(<script|javascript:|vbscript:|onload=|onerror=)", "input": "<script>alert('xss')</script>"}, "severity": "warning"}
{"timestamp": "2025-06-07T15:04:19.238734", "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(union|select|insert|delete|drop|create|alter)", "input": "'; DROP TABLE users; --"}, "severity": "warning"}
{"timestamp": "2025-06-07T15:04:19.239733", "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(\\.\\.\\/|\\.\\.\\\\)", "input": "../../../etc/passwd"}, "severity": "warning"}
{"timestamp": "2025-06-07T15:04:19.241732", "event_type": "rate_limit_exceeded", "source_ip": "*************", "user_agent": "unknown", "endpoint": "/api/test", "details": {"limit": {"requests": 100, "window": 60}, "current_count": 100}, "severity": "warning"}
{"timestamp": "2025-06-07T15:04:19.242732", "event_type": "ip_blocked", "source_ip": "*******", "user_agent": "system", "endpoint": "security_action", "details": {"duration_minutes": 1, "block_until": 1749326719.2427323}, "severity": "warning"}
{"timestamp": "2025-06-07T15:04:19.243733", "event_type": "session_created", "source_ip": "127.0.0.1", "user_agent": "unknown", "endpoint": "session_management", "details": {"user_id": "test_user", "session_id": "vzW5oR6A..."}, "severity": "info"}
{"timestamp": "2025-06-07T15:11:07.666007", "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(<script|javascript:|vbscript:|onload=|onerror=)", "input": "<script>alert('xss')</script>"}, "severity": "warning"}
{"timestamp": "2025-06-07T15:14:15.334047", "event_type": "suspicious_input", "source_ip": "0.0.0.0", "user_agent": "unknown", "endpoint": "input_validation", "details": {"pattern": "(?i)(<script|javascript:|vbscript:|onload=|onerror=)", "input": "<script>alert('xss')</script>"}, "severity": "warning"}
