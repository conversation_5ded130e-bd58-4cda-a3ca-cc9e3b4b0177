# Render Environment Variables Configuration
## Money Circle Investment Club Platform

### **🔧 REQUIRED ENVIRONMENT VARIABLES FOR RENDER**

Add these environment variables in your Render service settings:

#### **Core Configuration**
```
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=10000
PYTHONUNBUFFERED=1
PYTHON_VERSION=3.11.0
```

#### **Database Configuration**
```
DATABASE_URL=[Ren<PERSON> will auto-populate this when you add PostgreSQL]
```

#### **Security Configuration** 
```
JWT_SECRET=[Generate a secure 32+ character secret - see below]
SESSION_TIMEOUT=7200
HTTPS_ONLY=true
SECURE_COOKIES=true
CSRF_PROTECTION=true
```

#### **Trading Configuration**
```
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
MAX_POSITION_SIZE=1000.0
RISK_LIMIT_PERCENT=2.0
```

#### **Exchange Configuration**
```
BINANCE_ENABLED=true
HTX_ENABLED=true
BYBIT_ENABLED=true
EXCHANGE_TIMEOUT=30
EXCHANGE_RETRY_ATTEMPTS=3
```

#### **Monitoring Configuration**
```
MONITORING_ENABLED=true
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true
METRICS_INTERVAL=60
```

#### **Backup Configuration**
```
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
MAX_BACKUPS=30
BACKUP_COMPRESSION=true
```

#### **Rate Limiting**
```
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
```

---

### **🔐 GENERATING JWT SECRET**

**CRITICAL**: Generate a secure JWT secret using one of these methods:

#### **Method 1: Python (Recommended)**
```python
import secrets
print(secrets.token_urlsafe(32))
```

#### **Method 2: Online Generator**
Visit: https://generate-secret.vercel.app/32

#### **Method 3: Command Line**
```bash
openssl rand -base64 32
```

**Copy the generated secret and use it as your JWT_SECRET value.**

---

### **📋 RENDER DEPLOYMENT STEPS**

#### **Step 1: Push Code to GitHub**
```bash
# In your Money Circle directory
git init
git add .
git commit -m "Initial commit: Money Circle for Render deployment"
git remote add origin https://github.com/yourusername/money-circle.git
git push -u origin main
```

#### **Step 2: Create Render Account**
1. Go to [render.com](https://render.com)
2. Sign up with GitHub (recommended)
3. Authorize Render to access your repositories

#### **Step 3: Create Web Service**
1. Click "New +" → "Web Service"
2. Connect your GitHub repository: `money-circle`
3. Configure service settings:
   - **Name**: `money-circle`
   - **Environment**: `Python 3`
   - **Build Command**: `./build.sh`
   - **Start Command**: `python app.py`
   - **Plan**: Choose "Free" or "Starter" ($7/month)

#### **Step 4: Add PostgreSQL Database**
1. Click "New +" → "PostgreSQL"
2. Configure database:
   - **Name**: `money-circle-db`
   - **Plan**: Choose "Free" (90 days) or "Starter" ($7/month)
3. **Copy the DATABASE_URL** from the database info page

#### **Step 5: Configure Environment Variables**
In your web service settings, go to "Environment" tab and add all variables listed above.

---

### **🔧 RENDER-SPECIFIC CONFIGURATION**

#### **Build Command**
```bash
./build.sh
```

#### **Start Command**
```bash
python app.py
```

#### **Health Check Path**
```
/health
```

#### **Auto-Deploy**
- ✅ Enable auto-deploy from main branch
- ✅ Render will redeploy automatically when you push to GitHub

---

### **📊 RENDER PRICING**

#### **Web Service**
- **Free Tier**: Limited hours, spins down after inactivity
- **Starter**: $7/month, always on, custom domains
- **Standard**: $25/month, more resources

#### **PostgreSQL Database**
- **Free Tier**: 90 days free, then $7/month
- **Starter**: $7/month, 1GB storage
- **Standard**: $20/month, 10GB storage

**Recommended for Money Circle**: Starter plan for both ($14/month total)

---

### **🚀 DEPLOYMENT VERIFICATION**

After deployment, test your platform:

#### **Quick Test**
```bash
# Test health endpoint
curl https://your-app.onrender.com/health

# Test homepage
curl https://your-app.onrender.com
```

#### **Comprehensive Test**
```bash
python verify_deployment.py https://your-app.onrender.com
```

---

### **🌐 CUSTOM DOMAIN SETUP (Optional)**

#### **Step 1: Add Custom Domain in Render**
1. Go to your web service settings
2. Click "Custom Domains"
3. Add your domain: `money-circle.yourdomain.com`

#### **Step 2: Update DNS Records**
Add these DNS records in your domain provider:
```
Type: CNAME
Name: money-circle
Value: your-app.onrender.com
```

#### **Step 3: SSL Certificate**
Render automatically provisions SSL certificates for custom domains.

---

### **✅ ENVIRONMENT VARIABLES CHECKLIST**

Before deploying, ensure you have:

- [ ] **ENVIRONMENT** = production
- [ ] **DEBUG** = false
- [ ] **HOST** = 0.0.0.0
- [ ] **PORT** = 10000
- [ ] **PYTHONUNBUFFERED** = 1
- [ ] **PYTHON_VERSION** = 3.11.0
- [ ] **DATABASE_URL** = [from Render PostgreSQL]
- [ ] **JWT_SECRET** = [your generated secret]
- [ ] **SESSION_TIMEOUT** = 7200
- [ ] **HTTPS_ONLY** = true
- [ ] **SECURE_COOKIES** = true
- [ ] **CSRF_PROTECTION** = true
- [ ] **LIVE_TRADING_ENABLED** = true
- [ ] **TESTNET_MODE** = false
- [ ] **MAX_POSITION_SIZE** = 1000.0
- [ ] **RISK_LIMIT_PERCENT** = 2.0
- [ ] **BINANCE_ENABLED** = true
- [ ] **HTX_ENABLED** = true
- [ ] **BYBIT_ENABLED** = true
- [ ] **EXCHANGE_TIMEOUT** = 30
- [ ] **EXCHANGE_RETRY_ATTEMPTS** = 3
- [ ] **MONITORING_ENABLED** = true
- [ ] **PERFORMANCE_MONITORING** = true
- [ ] **ERROR_TRACKING** = true
- [ ] **METRICS_INTERVAL** = 60
- [ ] **BACKUP_ENABLED** = true
- [ ] **BACKUP_INTERVAL** = 3600
- [ ] **MAX_BACKUPS** = 30
- [ ] **BACKUP_COMPRESSION** = true
- [ ] **RATE_LIMIT_REQUESTS** = 1000
- [ ] **RATE_LIMIT_WINDOW** = 3600
- [ ] **MAX_LOGIN_ATTEMPTS** = 5
- [ ] **LOCKOUT_DURATION** = 900

---

### **🆘 TROUBLESHOOTING**

#### **Build Fails**
- Check that `build.sh` has execute permissions
- Verify all dependencies in `requirements.txt`
- Review build logs in Render dashboard

#### **App Won't Start**
- Check start command: `python app.py`
- Verify all environment variables are set
- Review application logs

#### **Database Connection Issues**
- Ensure DATABASE_URL is correctly set
- Verify PostgreSQL service is running
- Check database connection string format

#### **Performance Issues**
- Consider upgrading to Starter plan ($7/month)
- Enable performance monitoring
- Review application logs for bottlenecks

---

### **📞 SUPPORT RESOURCES**

- **Render Documentation**: [render.com/docs](https://render.com/docs)
- **Render Community**: [community.render.com](https://community.render.com)
- **Money Circle Issues**: GitHub repository issues

---

**🎯 Expected Result**: Live Money Circle platform at `https://your-app.onrender.com` ready for Epinnox investment club members!
