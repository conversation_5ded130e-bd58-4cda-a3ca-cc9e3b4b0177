#!/usr/bin/env python3
"""
Strategic Intelligence System Test
Comprehensive test of the new strategic intelligence modules
"""

import asyncio
import logging
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_strategic_intelligence():
    """Test the complete strategic intelligence system."""
    try:
        logger.info("🧠 STRATEGIC INTELLIGENCE SYSTEM TEST")
        logger.info("=" * 60)

        # Import modules
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))

        from autonomy.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        from autonomy.strategy_evaluator import StrategyEvaluator
        from autonomy.strategy_tracker import StrategyTracker

        # Load basic configuration
        logger.info("📋 Loading configuration...")
        config = {
            'multi_timeframe': {'enabled': True},
            'strategy_evaluator': {'enabled': True, 'min_confidence': 0.6},
            'strategy_tracker': {'enabled': True, 'max_trade_history': 1000}
        }

        # Initialize data store
        logger.info("💾 Initializing data store...")
        data_store = LiveDataStore(config)
        await data_store.start_background_tasks()

        # Initialize HTX client
        logger.info("🔗 Initializing HTX client...")
        htx_client = create_htx_client(config)
        connected = await htx_client.connect()

        if not connected:
            logger.error("❌ Failed to connect to HTX - cannot test MTA")
            return False

        # Initialize Smart Strategy with Strategic Intelligence
        logger.info("🧠 Initializing Smart Strategy with Strategic Intelligence...")
        smart_strategy = SmartStrategy(config, data_store)

        # 🔌 CRITICAL: Inject HTX client for MTA
        logger.info("🔗 Injecting HTX client into MTA...")
        smart_strategy.set_exchange_client(htx_client)

        # 🧵 Start background tasks
        logger.info("🚀 Starting Strategic Intelligence background tasks...")
        await smart_strategy.start_background_tasks()

        # Test 1: Multi-Timeframe Analysis
        logger.info("\n🧱 TEST 1: Multi-Timeframe Analysis")
        logger.info("-" * 40)

        symbol = "DOGE/USDT:USDT"
        mta_analysis = await smart_strategy.mta_analyzer.analyze_symbol(symbol, htx_client)

        if mta_analysis:
            logger.info(f"✅ MTA Analysis successful for {symbol}")
            logger.info(f"   Dominant Trend: {mta_analysis.dominant_trend}")
            logger.info(f"   Alignment Score: {mta_analysis.alignment_score:.2f}")
            logger.info(f"   Risk Level: {mta_analysis.risk_level}")
            logger.info(f"   Execution Rec: {mta_analysis.execution_recommendation}")
            logger.info(f"   Confidence: {mta_analysis.confidence:.2f}")
            logger.info(f"   Timeframes Analyzed: {len(mta_analysis.timeframes)}")

            for tf, context in mta_analysis.timeframes.items():
                logger.info(f"   {tf}: {context.regime.value} | VWAP: {context.vwap_bias.value} | Momentum: {context.momentum_score:.2f}")
        else:
            logger.error("❌ MTA Analysis failed")
            return False

        # Test 2: Strategy Evaluation
        logger.info("\n🧠 TEST 2: Strategy Evaluation")
        logger.info("-" * 40)

        # Create mock signal for evaluation
        mock_signal = {
            'symbol': symbol,
            'action': 'LONG',
            'confidence': 0.75,
            'model_contributions': {
                'rsi': 0.3,
                'vwap': 0.4,
                'orderflow': 0.2,
                'volatility': 0.1
            }
        }

        mock_account_data = {
            'margin_used_pct': 45.0,
            'available_balance': 100.0
        }

        mock_model_outputs = {
            'rsi': type('MockOutput', (), {'model_name': 'rsi', 'confidence': 0.7})(),
            'vwap': type('MockOutput', (), {'model_name': 'vwap', 'confidence': 0.8})(),
        }

        evaluation = await smart_strategy.strategy_evaluator.evaluate_signal(
            mock_signal, mta_analysis, mock_account_data, mock_model_outputs
        )

        if evaluation:
            logger.info(f"✅ Strategy Evaluation successful")
            logger.info(f"   Decision: {evaluation.decision.value}")
            logger.info(f"   Confidence Adjustment: {evaluation.confidence_adjustment:.2f}")
            logger.info(f"   Size Adjustment: {evaluation.size_adjustment:.2f}")
            logger.info(f"   Risk Assessment: {evaluation.risk_assessment}")
            logger.info(f"   Execution Priority: {evaluation.execution_priority}")
            logger.info(f"   Reasoning: {evaluation.reasoning}")
        else:
            logger.error("❌ Strategy Evaluation failed")
            return False

        # Test 3: Strategy Tracker
        logger.info("\n📊 TEST 3: Strategy Tracker")
        logger.info("-" * 40)

        # Create mock trade result
        from autonomy.strategy_tracker import TradeResult

        mock_trade = TradeResult(
            trade_id="test_001",
            symbol=symbol,
            action="LONG",
            entry_price=0.1850,
            exit_price=0.1875,
            size=100.0,
            pnl=2.50,
            duration_minutes=45.0,
            model_contributions={'rsi': 0.3, 'vwap': 0.4, 'orderflow': 0.3},
            mta_alignment_score=0.75,
            signal_confidence=0.80,
            execution_quality=0.85,
            timestamp=time.time()
        )

        smart_strategy.strategy_tracker.record_trade_result(mock_trade)
        logger.info("✅ Trade result recorded in Strategy Tracker")

        # Get tracker stats
        tracker_stats = smart_strategy.strategy_tracker.get_tracker_stats()
        logger.info(f"   Total Trades Tracked: {tracker_stats['basic_stats']['total_trades_tracked']}")

        # Test 4: Strategic Intelligence Stats
        logger.info("\n🧠 TEST 4: Strategic Intelligence Statistics")
        logger.info("-" * 40)

        si_stats = smart_strategy.get_strategic_intelligence_stats()

        if si_stats:
            logger.info("✅ Strategic Intelligence stats retrieved")
            logger.info(f"   MTA Analyses: {si_stats['mta_analyzer']['total_analyses']}")
            logger.info(f"   Cache Hit Rate: {si_stats['mta_analyzer']['cache_hit_rate']:.1%}")
            logger.info(f"   Evaluations: {si_stats['strategy_evaluator']['total_evaluations']}")
            logger.info(f"   Execution Rate: {si_stats['strategy_evaluator']['execution_rate']:.1%}")

            if si_stats['last_mta_analysis']:
                for sym, analysis in si_stats['last_mta_analysis'].items():
                    logger.info(f"   Last MTA ({sym}): {analysis['dominant_trend']} | Confidence: {analysis['confidence']:.2f}")

        # Test 5: Model Weight Updates
        logger.info("\n🔧 TEST 5: Model Weight Updates")
        logger.info("-" * 40)

        new_weights = {
            'rsi': 0.9,    # Reduce RSI weight
            'vwap': 1.3,   # Increase VWAP weight
            'orderflow': 0.8,
            'volatility': 0.6
        }

        smart_strategy.update_model_weights(new_weights)
        logger.info("✅ Model weights updated successfully")

        # Test 6: Background Task Status
        logger.info("\n🧵 TEST 6: Background Task Status")
        logger.info("-" * 40)

        # Wait a moment for background tasks to run
        await asyncio.sleep(2)

        logger.info("✅ Background tasks running successfully")
        logger.info("   Strategy Tracker: Periodic saves active")
        logger.info("   MTA Analyzer: Cache management active")

        # Final Summary
        logger.info("\n🎉 STRATEGIC INTELLIGENCE TEST SUMMARY")
        logger.info("=" * 60)
        logger.info("✅ Multi-Timeframe Analysis: WORKING")
        logger.info("✅ Strategy Evaluation: WORKING")
        logger.info("✅ Strategy Tracker: WORKING")
        logger.info("✅ Performance Monitoring: WORKING")
        logger.info("✅ Model Weight Updates: WORKING")
        logger.info("✅ Background Tasks: WORKING")
        logger.info("✅ HTX Integration: WORKING")

        logger.info("\n🧠 STRATEGIC INTELLIGENCE SYSTEM: FULLY OPERATIONAL")

        # Cleanup
        smart_strategy.shutdown()
        await htx_client.disconnect()

        return True

    except Exception as e:
        logger.error(f"❌ Strategic Intelligence test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Strategic Intelligence System Test...")

    success = await test_strategic_intelligence()

    if success:
        logger.info("\n🎉 ALL TESTS PASSED - STRATEGIC INTELLIGENCE READY!")
        logger.info("🧠 Your trading system now has institutional-grade intelligence!")
    else:
        logger.error("\n❌ TESTS FAILED - CHECK CONFIGURATION")

    return success

if __name__ == "__main__":
    asyncio.run(main())
