#!/usr/bin/env python3
"""
Gradual Multi-Symbol Integration
Conservative integration of multi-symbol system with existing infrastructure.
"""

import asyncio
import logging
import time
import yaml
import signal
import sys
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import existing system components
from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from storage.live_store import LiveDataStore
from feeds.trade_parser import MarketFeatures
from execution.execution_controller import ExecutionController
from models.smart_strategy import SmartStrategy
from feeds.htx_ws_client import HTXWebSocketClient
from feeds.binance_ws_client import BinanceWebSocketClient

# Import multi-symbol components
from autonomy.market_scanner import MultiSymbolMarketScanner
from autonomy.symbol_selector import DynamicSymbolSelector
from autonomy.multi_symbol_trader import MultiSymbolAutonomousTrader
from autonomy.enhanced_decision_engine import EnhancedDecisionEngine

class GradualMultiSymbolSystem:
    """
    Gradual integration of multi-symbol system with existing infrastructure.
    Runs multi-symbol components in monitoring mode initially.
    """

    def __init__(self):
        # Existing system components
        self.ui = None
        self.data_store = None
        self.execution_controller = None
        self.strategy = None
        self.htx_client = None
        self.binance_client = None
        self.active_ws_client = None
        
        # Multi-symbol components
        self.market_scanner = None
        self.symbol_selector = None
        self.multi_symbol_trader = None
        self.decision_engine = None
        
        # System state
        self.shutdown_event = asyncio.Event()
        self.tasks = []
        self.multi_symbol_enabled = False
        self.monitoring_mode = True  # Start in monitoring mode
        
        # Performance tracking
        self.startup_time = 0
        self.system_stats = {
            'original_system_trades': 0,
            'multi_symbol_signals': 0,
            'multi_symbol_opportunities': 0,
            'system_uptime': 0
        }

    async def start(self):
        """Start the gradual multi-symbol integration system."""
        try:
            logger.info("🚀 Starting Gradual Multi-Symbol Integration System")
            logger.info("📊 Mode: Conservative Integration with Live Monitoring")
            
            self.startup_time = time.time()

            # Load configurations
            await self._load_configurations()

            # Initialize existing system components
            await self._initialize_existing_system()

            # Initialize multi-symbol components (monitoring mode)
            await self._initialize_multi_symbol_components()

            # Start all components
            await self._start_all_components()

            # Display system status
            await self._display_system_status()

            # Start monitoring loop
            monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.tasks.append(monitoring_task)

            # Wait for shutdown signal
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"❌ Error starting gradual integration system: {e}")
            raise

    async def _load_configurations(self):
        """Load both original and multi-symbol configurations."""
        
        # Load original configuration
        script_dir = Path(__file__).parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            self.original_config = yaml.safe_load(f)
        
        # Load multi-symbol configuration
        multi_config_path = script_dir / "config" / "multi_symbol_config.yaml"
        with open(multi_config_path, 'r') as f:
            self.multi_config = yaml.safe_load(f)
        
        # Merge configurations
        self.config = self.original_config.copy()
        self.config.update(self.multi_config)
        
        # Set conservative mode
        self.multi_symbol_enabled = self.config.get('multi_symbol_enabled', True)
        self.monitoring_mode = True  # Always start in monitoring mode
        
        logger.info(f"✅ Configurations loaded: Multi-symbol enabled: {self.multi_symbol_enabled}")

    async def _initialize_existing_system(self):
        """Initialize the existing trading system components."""
        
        logger.info("📊 Initializing existing system components...")
        
        # Initialize data store
        self.data_store = LiveDataStore(self.config)
        
        # Initialize execution controller
        self.execution_controller = ExecutionController(self.config)
        
        # Initialize WebSocket clients
        self.htx_client = HTXWebSocketClient(self.config)
        self.binance_client = BinanceWebSocketClient(self.config)
        
        # Set up data handlers
        await self._setup_data_handlers()
        
        # Initialize AI trading strategy
        smart_strategy = SmartStrategy(self.config, self.data_store)
        
        # Inject dependencies
        smart_strategy.set_exchange_client(self.execution_controller.htx_client)
        smart_strategy.set_account_tracker(self.execution_controller.account_tracker)
        self.execution_controller.set_smart_strategy(smart_strategy)
        
        # Set initial strategy mode
        initial_strategy_mode = self.config.get('strategy_mode', 'scalping')
        smart_strategy.set_strategy_mode(initial_strategy_mode)
        
        self.strategy = smart_strategy
        
        # Initialize UI
        self.ui = AIStrategyTunerDashboard(self.config, self.data_store, self.execution_controller)
        
        logger.info("✅ Existing system components initialized")

    async def _initialize_multi_symbol_components(self):
        """Initialize multi-symbol components in monitoring mode."""
        
        if not self.multi_symbol_enabled:
            logger.info("⚠️ Multi-symbol system disabled in configuration")
            return
        
        logger.info("🎯 Initializing Multi-Symbol Components (Monitoring Mode)...")
        
        try:
            # Initialize market scanner
            self.market_scanner = MultiSymbolMarketScanner(
                self.config, 
                self.execution_controller.htx_client
            )
            
            # Initialize symbol selector
            self.symbol_selector = DynamicSymbolSelector(
                self.config, 
                self.market_scanner
            )
            
            # Initialize multi-symbol trader (monitoring mode only)
            trader_config = self.config.copy()
            trader_config['multi_symbol_trader']['enabled'] = False  # Monitoring mode
            
            self.multi_symbol_trader = MultiSymbolAutonomousTrader(
                trader_config, 
                self.market_scanner, 
                self.symbol_selector,
                None  # No execution controller in monitoring mode
            )
            
            # Initialize decision engine (monitoring mode)
            self.decision_engine = EnhancedDecisionEngine(
                self.config, 
                self.market_scanner, 
                self.symbol_selector, 
                self.multi_symbol_trader
            )
            
            # Inject multi-symbol components into UI for API access
            self.ui.market_scanner = self.market_scanner
            self.ui.symbol_selector = self.symbol_selector
            self.ui.multi_symbol_trader = self.multi_symbol_trader
            self.ui.decision_engine = self.decision_engine
            
            logger.info("✅ Multi-Symbol components initialized (monitoring mode)")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize multi-symbol components: {e}")
            self.multi_symbol_enabled = False

    async def _setup_data_handlers(self):
        """Set up data handlers for WebSocket clients."""
        
        async def trade_handler(trade_data):
            """Process trade data into market features."""
            try:
                symbol = trade_data.get('symbol', 'DOGE/USDT:USDT')
                price = float(trade_data.get('price', 0))
                volume = float(trade_data.get('volume', 0))
                timestamp = trade_data.get('timestamp', time.time())

                if price > 0:
                    features = MarketFeatures(
                        symbol=symbol,
                        timestamp=int(timestamp * 1000),
                        last_price=price,
                        price_change_1m=0.001,
                        price_velocity=0.0001,
                        volume_1m=volume,
                        buy_volume_ratio=0.55,
                        volume_delta=volume * 0.1,
                        order_flow_imbalance=0.1,
                        trade_intensity=5.0,
                        avg_trade_size=volume / 10,
                        rsi=50.0 + (price % 10 - 5) * 4,
                        vwap=price * 0.999,
                        volatility=0.02
                    )

                    self.data_store.store_features(features)
                    
                    # Update system stats
                    self.system_stats['original_system_trades'] += 1

            except Exception as e:
                logger.error(f"❌ Error processing trade data: {e}")

        async def depth_handler(depth_data):
            """Handle order book depth data."""
            try:
                symbol = depth_data.get('symbol', 'DOGE/USDT:USDT')
                logger.debug(f"📈 Depth data received: {symbol}")
            except Exception as e:
                logger.error(f"❌ Error processing depth data: {e}")

        # Set handlers for both clients
        self.htx_client.set_trade_handler(trade_handler)
        self.htx_client.set_depth_handler(depth_handler)
        self.binance_client.set_trade_handler(trade_handler)
        self.binance_client.set_depth_handler(depth_handler)

    async def _start_all_components(self):
        """Start all system components."""
        
        logger.info("🔄 Starting all system components...")
        
        # Start existing system components
        await self.data_store.start_background_tasks()
        await self.strategy.start_background_tasks()
        
        # Connect to market data sources
        await self._connect_market_data()
        
        # Start multi-symbol components (monitoring mode)
        if self.multi_symbol_enabled:
            logger.info("🎯 Starting Multi-Symbol Components (Monitoring Mode)...")
            
            # Start market scanner
            await self.market_scanner.start_scanning()
            scanner_task = asyncio.create_task(self._monitor_scanner())
            self.tasks.append(scanner_task)
            
            # Start symbol selector
            await self.symbol_selector.start_selection()
            selector_task = asyncio.create_task(self._monitor_selector())
            self.tasks.append(selector_task)
            
            # Start decision engine
            await self.decision_engine.start_engine()
            engine_task = asyncio.create_task(self._monitor_decision_engine())
            self.tasks.append(engine_task)
            
            logger.info("✅ Multi-Symbol components started (monitoring mode)")
        
        # Start the dashboard
        self.runner = await self.ui.start_server(host='localhost', port=8086)
        
        # Mark strategy as running in UI
        self.ui.set_strategy_running(True)

    async def _connect_market_data(self):
        """Connect to market data sources with fallback."""
        
        logger.info("📡 Connecting to market data sources...")
        
        # Try HTX first (primary)
        if await self.htx_client.connect():
            htx_task = asyncio.create_task(self.htx_client.listen())
            self.tasks.append(htx_task)
            self.active_ws_client = self.htx_client
            logger.info("✅ HTX WebSocket connected (primary source)")
        else:
            # HTX failed, try Binance fallback
            logger.warning("⚠️ HTX failed, trying Binance fallback...")
            if await self.binance_client.connect():
                binance_task = asyncio.create_task(self.binance_client.listen())
                self.tasks.append(binance_task)
                self.active_ws_client = self.binance_client
                logger.info("✅ Binance WebSocket connected (fallback)")
            else:
                logger.error("❌ Both data sources failed - offline mode")
                self.active_ws_client = None

    async def _monitoring_loop(self):
        """Main monitoring loop for system performance."""
        
        while not self.shutdown_event.is_set():
            try:
                # Update system stats
                self.system_stats['system_uptime'] = time.time() - self.startup_time
                
                # Log system status every 5 minutes
                if int(self.system_stats['system_uptime']) % 300 == 0:
                    await self._log_system_performance()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(10)

    async def _monitor_scanner(self):
        """Monitor market scanner performance."""
        
        while not self.shutdown_event.is_set():
            try:
                if self.market_scanner:
                    stats = self.market_scanner.get_scan_stats()
                    opportunities = self.market_scanner.get_market_opportunities(5)
                    
                    self.system_stats['multi_symbol_opportunities'] = len(opportunities)
                    
                    # Log scanner status every 10 minutes
                    if int(time.time()) % 600 == 0:
                        logger.info(f"📊 Scanner: {stats.get('symbols_scanned', 0)} symbols, "
                                  f"{len(opportunities)} opportunities")
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error monitoring scanner: {e}")
                await asyncio.sleep(30)

    async def _monitor_selector(self):
        """Monitor symbol selector performance."""
        
        while not self.shutdown_event.is_set():
            try:
                if self.symbol_selector:
                    current_symbol = self.symbol_selector.get_current_symbol()
                    stats = self.symbol_selector.get_selection_stats()
                    
                    # Log selector status every 15 minutes
                    if int(time.time()) % 900 == 0:
                        logger.info(f"🎯 Selector: Current symbol: {current_symbol}, "
                                  f"Selections: {stats.get('total_selections', 0)}")
                
                await asyncio.sleep(120)  # Check every 2 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error monitoring selector: {e}")
                await asyncio.sleep(60)

    async def _monitor_decision_engine(self):
        """Monitor decision engine performance."""
        
        while not self.shutdown_event.is_set():
            try:
                if self.decision_engine:
                    status = self.decision_engine.get_engine_status()
                    signals = self.decision_engine.get_recent_signals(5)
                    
                    self.system_stats['multi_symbol_signals'] = len(signals)
                    
                    # Log engine status every 20 minutes
                    if int(time.time()) % 1200 == 0:
                        logger.info(f"🧠 Decision Engine: {len(signals)} recent signals, "
                                  f"Running: {status.get('running', False)}")
                
                await asyncio.sleep(180)  # Check every 3 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error monitoring decision engine: {e}")
                await asyncio.sleep(90)

    async def _log_system_performance(self):
        """Log comprehensive system performance."""
        
        try:
            # Get account summary
            account_summary = self.execution_controller.account_tracker.get_account_summary()
            balance = account_summary.get('available_balance', 0.0)
            total_balance = account_summary.get('total_balance', 0.0)
            
            logger.info("📊 SYSTEM PERFORMANCE REPORT")
            logger.info("=" * 50)
            logger.info(f"⏱️ Uptime: {self.system_stats['system_uptime']:.0f}s")
            logger.info(f"💰 Account Balance: ${balance:.2f} / ${total_balance:.2f}")
            logger.info(f"📈 Original System Trades: {self.system_stats['original_system_trades']}")
            logger.info(f"🎯 Multi-Symbol Opportunities: {self.system_stats['multi_symbol_opportunities']}")
            logger.info(f"🧠 Multi-Symbol Signals: {self.system_stats['multi_symbol_signals']}")
            logger.info(f"🌐 Dashboard: http://localhost:8086")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"❌ Error logging system performance: {e}")

    async def _display_system_status(self):
        """Display comprehensive system status."""
        
        logger.info("\n🎉 Gradual Multi-Symbol Integration System Online!")
        logger.info("🌐 Dashboard URL: http://localhost:8086")
        
        logger.info("\n🎯 Active Components:")
        logger.info("   ✅ Original Onnyx System (Live Trading)")
        logger.info("   ✅ Multi-Symbol Market Scanner (Monitoring)")
        logger.info("   ✅ Dynamic Symbol Selector (Monitoring)")
        logger.info("   ✅ Enhanced Decision Engine (Monitoring)")
        logger.info("   ✅ Multi-Symbol Trader (Monitoring Mode)")
        
        # Market data source
        if self.active_ws_client == self.htx_client:
            logger.info("   ✅ HTX WebSocket market data feed (primary)")
        elif self.active_ws_client == self.binance_client:
            logger.info("   ✅ Binance WebSocket market data feed (fallback)")
        else:
            logger.info("   ⚠️ No live market data feed (offline mode)")
        
        logger.info("\n🛡️ Conservative Configuration:")
        logger.info(f"   💰 Total Capital: ${self.config['multi_symbol_trader']['total_capital']}")
        logger.info(f"   📊 Max Symbols: {self.config['multi_symbol_trader']['max_symbols']}")
        logger.info(f"   ⚖️ Max Risk per Symbol: {self.config['multi_symbol_trader']['max_risk_per_symbol']:.1%}")
        logger.info(f"   🛡️ Max Portfolio Risk: {self.config['multi_symbol_trader']['max_portfolio_risk']:.1%}")
        logger.info(f"   🎯 Min Confidence: {self.config['enhanced_decision_engine']['min_confidence']:.1%}")
        
        logger.info("\n📊 Monitoring Mode:")
        logger.info("   🔍 Market scanning and analysis active")
        logger.info("   📈 Signal generation and tracking active")
        logger.info("   🚫 No live trading from multi-symbol system")
        logger.info("   ✅ Original system continues live trading")
        
        logger.info("\n⌨️ Press Ctrl+C to stop the system")

    async def stop(self):
        """Stop all components with proper cleanup."""
        try:
            logger.info("🛑 Shutting down Gradual Multi-Symbol Integration System...")

            # Stop multi-symbol components
            if self.multi_symbol_enabled:
                if self.decision_engine:
                    await self.decision_engine.stop_engine()
                if self.symbol_selector:
                    await self.symbol_selector.stop_selection()
                if self.market_scanner:
                    await self.market_scanner.stop_scanning()
                logger.info("✅ Multi-Symbol components stopped")

            # Stop all background tasks
            for task in self.tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Stop existing system components
            if self.strategy:
                if hasattr(self.strategy, 'shutdown'):
                    self.strategy.shutdown()
                logger.info("✅ AI Strategy stopped")

            # Stop WebSocket clients
            if self.htx_client:
                await self.htx_client.disconnect()
                logger.info("✅ HTX WebSocket client stopped")
            if self.binance_client:
                await self.binance_client.disconnect()
                logger.info("✅ Binance WebSocket client stopped")

            # Stop account monitoring
            if self.execution_controller and hasattr(self.execution_controller, 'account_tracker'):
                await self.execution_controller.account_tracker.stop_monitoring()
                logger.info("✅ Account monitoring stopped")

            # Close CCXT connections
            if self.execution_controller and hasattr(self.execution_controller, 'htx_client'):
                await self.execution_controller.htx_client.disconnect()
                logger.info("✅ HTX client disconnected")

            # Stop the web server
            if hasattr(self, 'runner') and self.runner:
                await self.runner.cleanup()
                logger.info("✅ Web server stopped")

            # Shutdown data store
            if self.data_store:
                self.data_store.shutdown()
                logger.info("✅ Data store shutdown")

            logger.info("✅ Gradual integration system shutdown successful")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, initiating system shutdown...")
        self.shutdown_event.set()

async def main():
    """Main function with proper signal handling."""
    system = GradualMultiSymbolSystem()

    # Setup signal handlers
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)

    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        await system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Gradual integration system shutdown complete!")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)
