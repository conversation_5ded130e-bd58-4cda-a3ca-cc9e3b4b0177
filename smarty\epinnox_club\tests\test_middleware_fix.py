#!/usr/bin/env python3
"""
Simple test to verify middleware fix
Tests that the performance middleware receives proper Request objects
"""

import asyncio
import aiohttp
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_middleware_fix():
    """Test that the middleware fix works."""
    logger.info("🧪 Testing middleware fix...")
    
    base_url = "http://localhost:8086"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test health endpoint
            logger.info("Testing /health endpoint...")
            async with session.get(f"{base_url}/health") as resp:
                logger.info(f"Health endpoint status: {resp.status}")
                
                if resp.status == 200:
                    logger.info("✅ Health endpoint working - no middleware error!")
                    
                    # Check for performance headers
                    headers = dict(resp.headers)
                    if 'X-Response-Time' in headers:
                        logger.info(f"✅ Performance middleware working: {headers['X-Response-Time']}")
                    else:
                        logger.warning("⚠️ Performance headers missing")
                    
                    return True
                else:
                    logger.error(f"❌ Health endpoint failed: {resp.status}")
                    return False
            
            # Test homepage
            logger.info("Testing homepage...")
            async with session.get(base_url) as resp:
                logger.info(f"Homepage status: {resp.status}")
                
                if resp.status in [200, 302]:  # 302 = redirect to login
                    logger.info("✅ Homepage working - no middleware error!")
                    return True
                else:
                    logger.error(f"❌ Homepage failed: {resp.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return False

async def main():
    """Main test function."""
    print("🔧 Testing Money Circle Middleware Fix")
    print("=" * 40)
    
    success = await test_middleware_fix()
    
    if success:
        print("\n🎉 MIDDLEWARE FIX SUCCESSFUL!")
        print("✅ Performance middleware is working correctly")
        print("✅ No more 'Application' object errors")
        print("✅ Money Circle is ready for testing")
    else:
        print("\n❌ MIDDLEWARE STILL HAS ISSUES")
        print("🔧 Check the server logs for more details")
    
    return success

if __name__ == '__main__':
    success = asyncio.run(main())
    exit(0 if success else 1)
