#!/usr/bin/env python3
"""
Background Task Scheduler and Timer Utilities
Epinnox V6 - Standalone AI Strategy Tuner
"""

import asyncio
import logging
import time
from typing import Callable, Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TaskScheduler:
    """
    Background task scheduler for periodic operations.
    Manages recurring tasks with error handling and monitoring.
    """
    
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.running = False
        
    def add_task(self, 
                 name: str, 
                 func: Callable, 
                 interval: float, 
                 *args, 
                 **kwargs):
        """
        Add a periodic task to the scheduler.
        
        Args:
            name: Unique task name
            func: Function to execute
            interval: Interval in seconds
            *args, **kwargs: Arguments for the function
        """
        self.tasks[name] = {
            'func': func,
            'interval': interval,
            'args': args,
            'kwargs': kwargs,
            'last_run': 0,
            'run_count': 0,
            'error_count': 0,
            'enabled': True
        }
        
        logger.info(f"Task '{name}' added with {interval}s interval")
    
    def remove_task(self, name: str):
        """Remove a task from the scheduler."""
        if name in self.tasks:
            del self.tasks[name]
            logger.info(f"Task '{name}' removed")
    
    def enable_task(self, name: str):
        """Enable a task."""
        if name in self.tasks:
            self.tasks[name]['enabled'] = True
            logger.info(f"Task '{name}' enabled")
    
    def disable_task(self, name: str):
        """Disable a task."""
        if name in self.tasks:
            self.tasks[name]['enabled'] = False
            logger.info(f"Task '{name}' disabled")
    
    async def start(self):
        """Start the task scheduler."""
        self.running = True
        logger.info("Task scheduler started")
        
        while self.running:
            current_time = time.time()
            
            for name, task in self.tasks.items():
                if not task['enabled']:
                    continue
                
                # Check if it's time to run the task
                if current_time - task['last_run'] >= task['interval']:
                    try:
                        # Execute the task
                        if asyncio.iscoroutinefunction(task['func']):
                            await task['func'](*task['args'], **task['kwargs'])
                        else:
                            task['func'](*task['args'], **task['kwargs'])
                        
                        # Update task statistics
                        task['last_run'] = current_time
                        task['run_count'] += 1
                        
                    except Exception as e:
                        task['error_count'] += 1
                        logger.error(f"Error in task '{name}': {e}")
            
            # Sleep for a short interval
            await asyncio.sleep(1)
    
    def stop(self):
        """Stop the task scheduler."""
        self.running = False
        logger.info("Task scheduler stopped")
    
    def get_task_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all tasks."""
        stats = {}
        for name, task in self.tasks.items():
            stats[name] = {
                'enabled': task['enabled'],
                'interval': task['interval'],
                'run_count': task['run_count'],
                'error_count': task['error_count'],
                'last_run': datetime.fromtimestamp(task['last_run']) if task['last_run'] > 0 else None,
                'next_run': datetime.fromtimestamp(task['last_run'] + task['interval']) if task['last_run'] > 0 else None
            }
        return stats

class PerformanceTimer:
    """
    Performance timing utility for measuring execution times.
    """
    
    def __init__(self, name: str = "Timer"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.measurements = []
    
    def start(self):
        """Start timing."""
        self.start_time = time.perf_counter()
    
    def stop(self) -> float:
        """Stop timing and return elapsed time."""
        if self.start_time is None:
            raise ValueError("Timer not started")
        
        self.end_time = time.perf_counter()
        elapsed = self.end_time - self.start_time
        self.measurements.append(elapsed)
        
        return elapsed
    
    def elapsed(self) -> float:
        """Get elapsed time without stopping."""
        if self.start_time is None:
            return 0.0
        
        return time.perf_counter() - self.start_time
    
    def reset(self):
        """Reset the timer."""
        self.start_time = None
        self.end_time = None
    
    def get_stats(self) -> Dict[str, float]:
        """Get timing statistics."""
        if not self.measurements:
            return {'count': 0, 'avg': 0.0, 'min': 0.0, 'max': 0.0}
        
        return {
            'count': len(self.measurements),
            'avg': sum(self.measurements) / len(self.measurements),
            'min': min(self.measurements),
            'max': max(self.measurements),
            'total': sum(self.measurements)
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        elapsed = self.stop()
        logger.debug(f"{self.name} took {elapsed:.4f} seconds")

class RateLimiter:
    """
    Rate limiter for controlling function call frequency.
    """
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def can_proceed(self) -> bool:
        """Check if a call can proceed without exceeding rate limit."""
        current_time = time.time()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls 
                     if current_time - call_time < self.time_window]
        
        # Check if we can make another call
        return len(self.calls) < self.max_calls
    
    def record_call(self):
        """Record a function call."""
        self.calls.append(time.time())
    
    def wait_time(self) -> float:
        """Get time to wait before next call is allowed."""
        if self.can_proceed():
            return 0.0
        
        # Find the oldest call that needs to expire
        current_time = time.time()
        oldest_call = min(self.calls)
        
        return self.time_window - (current_time - oldest_call)
    
    async def wait_if_needed(self):
        """Wait if rate limit is exceeded."""
        wait_time = self.wait_time()
        if wait_time > 0:
            logger.debug(f"Rate limit exceeded, waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)

def rate_limited(max_calls: int, time_window: float):
    """
    Decorator for rate limiting function calls.
    
    Args:
        max_calls: Maximum number of calls allowed
        time_window: Time window in seconds
    """
    limiter = RateLimiter(max_calls, time_window)
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            await limiter.wait_if_needed()
            limiter.record_call()
            
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator

# Global task scheduler instance
scheduler = TaskScheduler()

async def start_scheduler():
    """Start the global task scheduler."""
    await scheduler.start()

def stop_scheduler():
    """Stop the global task scheduler."""
    scheduler.stop()
