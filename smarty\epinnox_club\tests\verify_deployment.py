#!/usr/bin/env python3
"""
Money Circle Deployment Verification Script
Comprehensive testing of all platform features in production environment
"""

import asyncio
import aiohttp
import json
import sys
import time
from datetime import datetime
from typing import Dict, List, Any

class DeploymentVerifier:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = None
        self.test_results = []
        self.auth_cookies = None
        
    async def run_verification(self):
        """Run complete deployment verification."""
        print("🔍 Money Circle Deployment Verification")
        print("=" * 50)
        print(f"🌐 Testing URL: {self.base_url}")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            # Core Infrastructure Tests
            await self.test_health_check()
            await self.test_ssl_certificate()
            await self.test_security_headers()
            
            # Authentication Tests
            await self.test_authentication()
            
            # Dashboard Tests (requires authentication)
            if self.auth_cookies:
                await self.test_admin_dashboard()
                await self.test_auto_trader_dashboard()
                await self.test_signals_dashboard()
                await self.test_portfolio_analytics()
                await self.test_social_trading()
                await self.test_club_features()
            
            # API Tests
            await self.test_api_endpoints()
            
            # Performance Tests
            await self.test_performance()
            
            # Market Data Tests
            await self.test_market_data()
            
        # Generate report
        self.generate_verification_report()
        
    async def test_health_check(self):
        """Test health check endpoint."""
        print("🏥 Testing Health Check...")
        
        try:
            async with self.session.get(f"{self.base_url}/health") as resp:
                if resp.status == 200:
                    health_data = await resp.json()
                    
                    required_fields = ['status', 'timestamp', 'version', 'environment']
                    has_all_fields = all(field in health_data for field in required_fields)
                    
                    if has_all_fields and health_data.get('status') in ['healthy', 'degraded']:
                        self.test_results.append({
                            'test': 'Health Check',
                            'status': 'PASS',
                            'details': f"Status: {health_data.get('status')}, Environment: {health_data.get('environment')}"
                        })
                        print("  ✅ Health check passed")
                    else:
                        self.test_results.append({
                            'test': 'Health Check',
                            'status': 'FAIL',
                            'details': f'Invalid health response: {health_data}'
                        })
                        print("  ❌ Health check failed - invalid response")
                else:
                    self.test_results.append({
                        'test': 'Health Check',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Health check failed - HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Health Check',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Health check error: {e}")
    
    async def test_ssl_certificate(self):
        """Test SSL certificate validity."""
        print("🔒 Testing SSL Certificate...")
        
        try:
            if self.base_url.startswith('https://'):
                async with self.session.get(self.base_url) as resp:
                    if resp.status in [200, 302]:  # 302 = redirect to login
                        self.test_results.append({
                            'test': 'SSL Certificate',
                            'status': 'PASS',
                            'details': 'HTTPS connection successful'
                        })
                        print("  ✅ SSL certificate valid")
                    else:
                        self.test_results.append({
                            'test': 'SSL Certificate',
                            'status': 'FAIL',
                            'details': f'HTTPS connection failed: {resp.status}'
                        })
                        print(f"  ❌ SSL certificate issues: {resp.status}")
            else:
                self.test_results.append({
                    'test': 'SSL Certificate',
                    'status': 'WARNING',
                    'details': 'Not using HTTPS'
                })
                print("  ⚠️ Not using HTTPS")
                
        except Exception as e:
            self.test_results.append({
                'test': 'SSL Certificate',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ SSL test error: {e}")
    
    async def test_security_headers(self):
        """Test security headers."""
        print("🛡️ Testing Security Headers...")
        
        try:
            async with self.session.get(f"{self.base_url}/dashboard") as resp:
                security_headers = [
                    'X-Content-Type-Options',
                    'X-Frame-Options',
                    'X-XSS-Protection'
                ]
                
                present_headers = [h for h in security_headers if h in resp.headers]
                
                if len(present_headers) >= 2:
                    self.test_results.append({
                        'test': 'Security Headers',
                        'status': 'PASS',
                        'details': f'Headers present: {", ".join(present_headers)}'
                    })
                    print("  ✅ Security headers configured")
                else:
                    self.test_results.append({
                        'test': 'Security Headers',
                        'status': 'PARTIAL',
                        'details': f'Only {len(present_headers)} security headers found'
                    })
                    print("  ⚠️ Some security headers missing")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Security Headers',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Security headers test error: {e}")
    
    async def test_authentication(self):
        """Test authentication system."""
        print("🔐 Testing Authentication...")
        
        try:
            # Test login
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            async with self.session.post(f"{self.base_url}/login", data=login_data) as resp:
                if resp.status == 302:  # Redirect after successful login
                    # Store cookies for authenticated requests
                    self.auth_cookies = resp.cookies
                    
                    self.test_results.append({
                        'test': 'Authentication',
                        'status': 'PASS',
                        'details': 'Login successful with default credentials'
                    })
                    print("  ✅ Authentication working")
                else:
                    self.test_results.append({
                        'test': 'Authentication',
                        'status': 'FAIL',
                        'details': f'Login failed: HTTP {resp.status}'
                    })
                    print(f"  ❌ Authentication failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Authentication',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Authentication test error: {e}")
    
    async def test_admin_dashboard(self):
        """Test admin dashboard functionality."""
        print("👑 Testing Admin Dashboard...")
        
        try:
            async with self.session.get(f"{self.base_url}/admin", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    content = await resp.text()
                    if 'Admin Dashboard' in content:
                        self.test_results.append({
                            'test': 'Admin Dashboard',
                            'status': 'PASS',
                            'details': 'Admin dashboard accessible and rendering'
                        })
                        print("  ✅ Admin dashboard working")
                    else:
                        self.test_results.append({
                            'test': 'Admin Dashboard',
                            'status': 'FAIL',
                            'details': 'Admin dashboard not rendering correctly'
                        })
                        print("  ❌ Admin dashboard content issues")
                else:
                    self.test_results.append({
                        'test': 'Admin Dashboard',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Admin dashboard failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Admin Dashboard',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Admin dashboard test error: {e}")
    
    async def test_auto_trader_dashboard(self):
        """Test auto trader dashboard."""
        print("🤖 Testing Auto Trader Dashboard...")
        
        try:
            async with self.session.get(f"{self.base_url}/auto-trader", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    self.test_results.append({
                        'test': 'Auto Trader Dashboard',
                        'status': 'PASS',
                        'details': 'Auto trader dashboard accessible'
                    })
                    print("  ✅ Auto trader dashboard working")
                else:
                    self.test_results.append({
                        'test': 'Auto Trader Dashboard',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Auto trader dashboard failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Auto Trader Dashboard',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Auto trader dashboard test error: {e}")
    
    async def test_signals_dashboard(self):
        """Test signals dashboard."""
        print("📡 Testing Signals Dashboard...")
        
        try:
            async with self.session.get(f"{self.base_url}/signals", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    self.test_results.append({
                        'test': 'Signals Dashboard',
                        'status': 'PASS',
                        'details': 'Signals dashboard accessible'
                    })
                    print("  ✅ Signals dashboard working")
                else:
                    self.test_results.append({
                        'test': 'Signals Dashboard',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Signals dashboard failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Signals Dashboard',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Signals dashboard test error: {e}")
    
    async def test_portfolio_analytics(self):
        """Test portfolio analytics dashboard."""
        print("📈 Testing Portfolio Analytics...")
        
        try:
            async with self.session.get(f"{self.base_url}/portfolio-analytics", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    self.test_results.append({
                        'test': 'Portfolio Analytics',
                        'status': 'PASS',
                        'details': 'Portfolio analytics dashboard accessible'
                    })
                    print("  ✅ Portfolio analytics working")
                else:
                    self.test_results.append({
                        'test': 'Portfolio Analytics',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Portfolio analytics failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Portfolio Analytics',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Portfolio analytics test error: {e}")
    
    async def test_social_trading(self):
        """Test social trading dashboard."""
        print("👥 Testing Social Trading...")
        
        try:
            async with self.session.get(f"{self.base_url}/social-trading", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    self.test_results.append({
                        'test': 'Social Trading',
                        'status': 'PASS',
                        'details': 'Social trading dashboard accessible'
                    })
                    print("  ✅ Social trading working")
                else:
                    self.test_results.append({
                        'test': 'Social Trading',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Social trading failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Social Trading',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Social trading test error: {e}")
    
    async def test_club_features(self):
        """Test club features."""
        print("🏛️ Testing Club Features...")
        
        try:
            async with self.session.get(f"{self.base_url}/club", cookies=self.auth_cookies) as resp:
                if resp.status == 200:
                    self.test_results.append({
                        'test': 'Club Features',
                        'status': 'PASS',
                        'details': 'Club dashboard accessible'
                    })
                    print("  ✅ Club features working")
                else:
                    self.test_results.append({
                        'test': 'Club Features',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Club features failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Club Features',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Club features test error: {e}")
    
    async def test_api_endpoints(self):
        """Test API endpoints."""
        print("🔌 Testing API Endpoints...")
        
        try:
            # Test system status (should require auth)
            async with self.session.get(f"{self.base_url}/api/system/status") as resp:
                if resp.status in [200, 302, 401, 403]:  # Valid responses
                    self.test_results.append({
                        'test': 'API Endpoints',
                        'status': 'PASS',
                        'details': f'API endpoints responding (status: {resp.status})'
                    })
                    print("  ✅ API endpoints working")
                else:
                    self.test_results.append({
                        'test': 'API Endpoints',
                        'status': 'FAIL',
                        'details': f'Unexpected API response: {resp.status}'
                    })
                    print(f"  ❌ API endpoints failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'API Endpoints',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ API endpoints test error: {e}")
    
    async def test_performance(self):
        """Test performance metrics."""
        print("⚡ Testing Performance...")
        
        try:
            start_time = time.time()
            async with self.session.get(f"{self.base_url}/health") as resp:
                response_time = time.time() - start_time
                
                if resp.status == 200 and response_time < 2.0:  # Under 2 seconds
                    self.test_results.append({
                        'test': 'Performance',
                        'status': 'PASS',
                        'details': f'Response time: {response_time:.3f}s'
                    })
                    print(f"  ✅ Performance good ({response_time:.3f}s)")
                elif resp.status == 200:
                    self.test_results.append({
                        'test': 'Performance',
                        'status': 'WARNING',
                        'details': f'Slow response time: {response_time:.3f}s'
                    })
                    print(f"  ⚠️ Performance slow ({response_time:.3f}s)")
                else:
                    self.test_results.append({
                        'test': 'Performance',
                        'status': 'FAIL',
                        'details': f'HTTP {resp.status}'
                    })
                    print(f"  ❌ Performance test failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Performance',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Performance test error: {e}")
    
    async def test_market_data(self):
        """Test market data connectivity."""
        print("📊 Testing Market Data...")
        
        try:
            # Test market overview endpoint
            async with self.session.get(f"{self.base_url}/api/market/overview", cookies=self.auth_cookies) as resp:
                if resp.status in [200, 302, 401]:  # Valid responses
                    self.test_results.append({
                        'test': 'Market Data',
                        'status': 'PASS',
                        'details': 'Market data endpoints accessible'
                    })
                    print("  ✅ Market data connectivity working")
                else:
                    self.test_results.append({
                        'test': 'Market Data',
                        'status': 'FAIL',
                        'details': f'Market data endpoint failed: {resp.status}'
                    })
                    print(f"  ❌ Market data failed: HTTP {resp.status}")
                    
        except Exception as e:
            self.test_results.append({
                'test': 'Market Data',
                'status': 'ERROR',
                'details': str(e)
            })
            print(f"  ❌ Market data test error: {e}")
    
    def generate_verification_report(self):
        """Generate comprehensive verification report."""
        print("\n" + "=" * 50)
        print("📋 DEPLOYMENT VERIFICATION REPORT")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARNING'])
        
        print(f"🌐 Platform URL: {self.base_url}")
        print(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\n📊 Test Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ✅")
        print(f"   Failed: {failed_tests} ❌")
        print(f"   Errors: {error_tests} ⚠️")
        print(f"   Warnings: {warning_tests} 🔶")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 Detailed Results:")
        print("-" * 30)
        for result in self.test_results:
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '⚠️',
                'WARNING': '🔶'
            }.get(result['status'], '❓')
            
            print(f"{status_icon} {result['test']}: {result['status']}")
            print(f"   {result['details']}")
        
        # Overall assessment
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED - Deployment successful!")
            print("✅ Money Circle is ready for Epinnox investment club members")
        elif passed_tests >= total_tests * 0.8:
            print("\n✅ MOSTLY SUCCESSFUL - Deployment nearly complete")
            print("⚠️ Address any failed tests before going live")
        else:
            print("\n❌ DEPLOYMENT ISSUES - Requires attention")
            print("🔧 Fix critical issues before member access")
        
        print(f"\n🎯 Platform Status: {'READY' if passed_tests >= total_tests * 0.8 else 'NEEDS WORK'}")
        print(f"🔗 Access URL: {self.base_url}")
        print(f"🔐 Admin Login: epinnox / securepass123")

async def main():
    """Main verification function."""
    if len(sys.argv) != 2:
        print("Usage: python verify_deployment.py <URL>")
        print("Example: python verify_deployment.py https://your-app.railway.app")
        sys.exit(1)
    
    url = sys.argv[1]
    verifier = DeploymentVerifier(url)
    await verifier.run_verification()

if __name__ == "__main__":
    asyncio.run(main())
