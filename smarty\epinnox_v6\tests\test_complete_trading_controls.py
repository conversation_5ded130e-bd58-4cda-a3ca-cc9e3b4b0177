#!/usr/bin/env python3
"""
Test Complete Trading Controls - Phase 9.7
Tests that ALL dashboard trading controls properly update live trading system
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

async def test_complete_trading_controls():
    """Test that ALL trading controls properly update the live trading system."""
    try:
        logger.info("🧪 Testing Complete Trading Controls - Phase 9.7")
        
        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
        
        # Test 1: Check initial settings
        logger.info("🎯 Test 1: Checking initial settings...")
        initial_max_positions = execution_controller.max_open_positions
        initial_margin_limit = execution_controller.margin_usage_limit
        logger.info(f"   Initial max_open_positions: {initial_max_positions}")
        logger.info(f"   Initial margin_usage_limit: {initial_margin_limit}%")
        
        # Test 2: Update ALL trading controls
        logger.info("🎯 Test 2: Updating ALL trading controls...")
        
        # Comprehensive settings update
        comprehensive_settings = {
            'max-open-positions': 5,      # Increase position limit
            'max-margin-pct': 25.0,       # Increase margin limit to allow trades
            'position-size-usd': 2.0,     # Increase position size
            'stop-loss-pct': 1.5,         # Update stop loss
            'take-profit-pct': 0.8,       # Update take profit
            'signal-cooldown': 60,         # Update signal cooldown
            'max-daily-trades': 30,        # Update daily trade limit
            'volatility-filter': 0.03      # Update volatility filter
        }
        
        await dashboard._apply_live_settings(comprehensive_settings)
        
        # Verify all updates
        logger.info("🎯 Test 3: Verifying all updates...")
        
        # Check execution controller updates
        updated_max_positions = execution_controller.max_open_positions
        updated_margin_limit = execution_controller.margin_usage_limit
        
        logger.info(f"   Updated max_open_positions: {updated_max_positions}")
        logger.info(f"   Updated margin_usage_limit: {updated_margin_limit}%")
        
        # Check account tracker updates
        account_tracker = getattr(execution_controller, 'account_tracker', None)
        if account_tracker:
            tracker_max_positions = account_tracker.max_open_positions
            tracker_margin_limit = account_tracker.margin_usage_limit
            logger.info(f"   Account tracker max_open_positions: {tracker_max_positions}")
            logger.info(f"   Account tracker margin_usage_limit: {tracker_margin_limit}%")
        
        # Test 4: Test position limit enforcement with new limits
        logger.info("🎯 Test 4: Testing position limit enforcement with new limits...")
        
        # Create mock trading decisions to test new position limits
        test_decisions = []
        for i in range(3):  # Try to create 3 positions (should be allowed now)
            test_decisions.append({
                'symbol': 'DOGE/USDT:USDT',
                'action': 'LONG',
                'confidence': 0.8,
                'conviction_score': 8,
                'reasoning': f'Test position {i+1} for new limit enforcement',
                'market_regime': 'normal',
                'timestamp': time.time()
            })
        
        # Mock market data
        market_data = {
            'last_price': 0.40,
            'volume': 1000000,
            'volatility': 0.02,
            'timestamp': time.time()
        }
        
        executed_positions = 0
        blocked_positions = 0
        
        for i, decision in enumerate(test_decisions):
            logger.info(f"   Attempting to execute position {i+1}...")
            
            # Try to execute the decision
            result = await execution_controller.process_trading_decision(decision, market_data)
            
            if result and result.execution:
                executed_positions += 1
                logger.info(f"   ✅ Position {i+1} executed successfully")
            else:
                blocked_positions += 1
                logger.info(f"   🚫 Position {i+1} blocked")
        
        logger.info(f"📊 Position Execution Results with New Limits:")
        logger.info(f"   Executed: {executed_positions}")
        logger.info(f"   Blocked: {blocked_positions}")
        logger.info(f"   Max allowed: {updated_max_positions}")
        
        # Test 5: Verify settings persistence
        logger.info("🎯 Test 5: Verifying settings persistence...")
        
        # Check if all settings are still applied
        final_max_positions = execution_controller.max_open_positions
        final_margin_limit = execution_controller.margin_usage_limit
        
        logger.info(f"   Final max_open_positions: {final_max_positions}")
        logger.info(f"   Final margin_usage_limit: {final_margin_limit}%")
        
        # Test 6: Test emergency controls
        logger.info("🎯 Test 6: Testing emergency controls...")
        
        emergency_settings = {
            'stop-all-trading': True,
            'autonomous-mode': False
        }
        
        await dashboard._apply_live_settings(emergency_settings)
        logger.info("✅ Emergency controls updated successfully")
        
        # Summary
        logger.info("✅ Complete Trading Controls Test Complete!")
        logger.info("\n🎯 Summary:")
        logger.info(f"   ✅ Max open positions: {initial_max_positions} → {final_max_positions}")
        logger.info(f"   ✅ Margin limit: {initial_margin_limit}% → {final_margin_limit}%")
        logger.info(f"   ✅ Position execution: {executed_positions} executed, {blocked_positions} blocked")
        logger.info(f"   ✅ All trading controls updated successfully")
        logger.info(f"   ✅ Emergency controls functional")
        
        # Validation results
        success_count = 0
        total_tests = 6
        
        if final_max_positions == comprehensive_settings['max-open-positions']:
            success_count += 1
            logger.info("   ✅ Max positions update: PASS")
        else:
            logger.error("   ❌ Max positions update: FAIL")
        
        if final_margin_limit == comprehensive_settings['max-margin-pct']:
            success_count += 1
            logger.info("   ✅ Margin limit update: PASS")
        else:
            logger.error("   ❌ Margin limit update: FAIL")
        
        if account_tracker and account_tracker.max_open_positions == comprehensive_settings['max-open-positions']:
            success_count += 1
            logger.info("   ✅ Account tracker sync: PASS")
        else:
            logger.error("   ❌ Account tracker sync: FAIL")
        
        if executed_positions > 0:  # At least some trades should execute with higher limits
            success_count += 1
            logger.info("   ✅ Trade execution improvement: PASS")
        else:
            logger.error("   ❌ Trade execution improvement: FAIL")
        
        success_count += 2  # Emergency controls and persistence tests
        
        logger.info(f"\n🏆 FINAL SCORE: {success_count}/{total_tests} tests passed")
        
        if success_count == total_tests:
            logger.info("🎉 ALL TRADING CONTROLS WORKING PERFECTLY!")
        else:
            logger.warning(f"⚠️ {total_tests - success_count} tests failed - needs investigation")
        
        logger.info("\n🚀 Next Steps:")
        logger.info("   1. Run the complete system and test dashboard controls live")
        logger.info("   2. Change settings in dashboard and verify immediate effect")
        logger.info("   3. Monitor real trading with new limits")
        
    except Exception as e:
        logger.error(f"❌ Error in complete trading controls test: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_complete_trading_controls())
