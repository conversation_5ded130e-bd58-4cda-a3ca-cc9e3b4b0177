#!/usr/bin/env python3
"""
Simple Signal Test - Generate and execute test trading signals
"""

import asyncio
import logging
import time
import random
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_simple_signals():
    """Test simple signal generation and execution."""
    try:
        logger.info("🧪 Testing Simple Signal Generation & Execution")
        logger.info("=" * 60)

        # Import components
        from storage.live_store import LiveDataStore
        from execution.execution_controller import ExecutionController
        import yaml

        # Load configuration
        config_path = Path(__file__).parent / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        logger.info("✅ Configuration loaded")

        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)

        logger.info("✅ Components initialized")
        logger.info(f"💰 Account Balance: ${execution_controller.account_tracker.get_account_summary()['balance']:.2f}")

        # Test signal scenarios
        test_signals = [
            {
                "name": "Strong Bullish Signal",
                "action": "LONG",
                "confidence": 0.85,
                "score": 0.7,
                "reasoning": "Strong upward momentum detected"
            },
            {
                "name": "Moderate Bearish Signal",
                "action": "SHORT",
                "confidence": 0.65,
                "score": -0.4,
                "reasoning": "Bearish trend confirmation"
            },
            {
                "name": "Weak Bullish Signal",
                "action": "LONG",
                "confidence": 0.45,
                "score": 0.2,
                "reasoning": "Minor bullish divergence"
            }
        ]

        symbol = "DOGE/USDT:USDT"
        current_price = 0.179

        for i, test_signal in enumerate(test_signals):
            logger.info(f"\n🎯 Test Signal {i+1}: {test_signal['name']}")
            logger.info("-" * 40)

            # Create signal data
            signal_data = {
                'symbol': symbol,
                'action': test_signal['action'],
                'confidence': test_signal['confidence'],
                'score': test_signal['score'],
                'reasoning': test_signal['reasoning'],
                'price': current_price + random.uniform(-0.002, 0.002),
                'timestamp': time.time(),
                'model_contributions': {'test_model': test_signal['score']}
            }

            logger.info(f"📊 Signal Details:")
            logger.info(f"   🎯 Action: {signal_data['action']}")
            logger.info(f"   💪 Confidence: {signal_data['confidence']:.2%}")
            logger.info(f"   💰 Score: {signal_data['score']:.3f}")
            logger.info(f"   💲 Price: ${signal_data['price']:.4f}")
            logger.info(f"   📝 Reasoning: {signal_data['reasoning']}")

            # Store signal in data store
            data_store.store_signal(signal_data)
            logger.info("✅ Signal stored in data store")

            # Test execution controller signal processing
            try:
                # Check if execution controller can process the signal
                if hasattr(execution_controller, 'process_signal'):
                    result = await execution_controller.process_signal(signal_data)
                    logger.info(f"📤 Execution result: {result}")
                else:
                    logger.info("📤 Execution controller ready (no process_signal method)")

                # Check account state after signal
                account_summary = execution_controller.account_tracker.get_account_summary()
                logger.info(f"💰 Account after signal: ${account_summary['balance']:.2f}")

            except Exception as e:
                logger.warning(f"⚠️ Signal processing error: {e}")

            # Wait between signals
            await asyncio.sleep(3)

        logger.info("\n🎉 Simple signal test completed!")
        logger.info("📊 Check dashboard at http://localhost:8086 for signal display")

        # Cleanup
        data_store.shutdown()

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_signals())
