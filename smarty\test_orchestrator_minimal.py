#!/usr/bin/env python3
"""
Minimal test to check orchestrator startup issues.
"""

import asyncio
import logging
import sys
import time
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_basic_imports():
    """Test basic imports."""
    print("🔍 Testing basic imports...")
    
    try:
        from core.events import Kline, Trade, Signal
        print("✅ Core events imported")
        
        from core.feature_store import feature_store
        print("✅ Feature store imported")
        
        from pipeline.databus import create_bus
        print("✅ Databus imported")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

async def test_bus_creation():
    """Test message bus creation."""
    print("🔍 Testing message bus creation...")
    
    try:
        from pipeline.databus import create_bus
        
        config = {
            "bus": {
                "type": "sqlite",
                "path": "data/bus.db"
            }
        }
        
        bus = create_bus(config)
        print(f"✅ Bus created: {type(bus).__name__}")
        
        # Test publishing a simple message
        test_message = {"test": "message", "timestamp": time.time()}
        bus.publish("test.stream", test_message)
        print("✅ Test message published")
        
        return True
    except Exception as e:
        print(f"❌ Bus creation failed: {e}")
        return False

async def test_htx_client():
    """Test HTX client creation."""
    print("🔍 Testing HTX client creation...")
    
    try:
        from feeds.htx_futures import HTXFuturesClient
        
        client = HTXFuturesClient(
            api_key="",
            api_secret="",
            testnet=True
        )
        print("✅ HTX client created")
        
        return True
    except Exception as e:
        print(f"❌ HTX client creation failed: {e}")
        return False

async def test_model_creation():
    """Test model creation."""
    print("🔍 Testing model creation...")
    
    try:
        from models.rsi import RSIModel
        
        rsi_model = RSIModel(period=14, overbought_threshold=70, oversold_threshold=30)
        print("✅ RSI model created")
        
        return True
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return False

async def test_orchestrator_config():
    """Test orchestrator configuration."""
    print("🔍 Testing orchestrator configuration...")
    
    try:
        config = {
            "symbols": ["BTC-USDT"],
            "trading": {
                "enabled": False,
                "simulation_mode": True,
                "max_positions": 1
            },
            "bus": {
                "type": "sqlite",
                "path": "data/bus.db"
            },
            "enable_rsi_model": True,
            "enable_orderflow_model": True,
            "dummy_orderflow": True,
            "dummy_llm": True,
            "testnet": True,
            "debug": {
                "enabled": True,
                "generate_test_signals": False
            }
        }
        
        print("✅ Configuration created")
        return config
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return None

async def test_orchestrator_creation():
    """Test orchestrator creation."""
    print("🔍 Testing orchestrator creation...")
    
    try:
        # Import here to avoid issues if previous tests failed
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from orchestrator import Orchestrator
        
        config = await test_orchestrator_config()
        if not config:
            return False
        
        orchestrator = Orchestrator(config)
        print("✅ Orchestrator created")
        
        return True
    except Exception as e:
        print(f"❌ Orchestrator creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_orchestrator_start():
    """Test orchestrator startup."""
    print("🔍 Testing orchestrator startup...")
    
    try:
        from orchestrator import Orchestrator
        
        config = await test_orchestrator_config()
        if not config:
            return False
        
        orchestrator = Orchestrator(config)
        print("✅ Orchestrator created, attempting start...")
        
        # Try to start with timeout
        start_task = asyncio.create_task(orchestrator.start())
        
        try:
            await asyncio.wait_for(start_task, timeout=10.0)
            print("✅ Orchestrator started successfully")
            
            # Stop it
            await orchestrator.stop()
            print("✅ Orchestrator stopped successfully")
            
            return True
        except asyncio.TimeoutError:
            print("⏳ Orchestrator startup timed out (this might be normal)")
            start_task.cancel()
            return True
        
    except Exception as e:
        print(f"❌ Orchestrator startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("🧠 MINIMAL ORCHESTRATOR TEST")
    print("=" * 50)
    print(f"⏰ Time: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Bus Creation", test_bus_creation),
        ("HTX Client", test_htx_client),
        ("Model Creation", test_model_creation),
        ("Orchestrator Creation", test_orchestrator_creation),
        ("Orchestrator Startup", test_orchestrator_start),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*50}")
    print("📊 TEST RESULTS:")
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n🎯 SUMMARY: {passed}/{total} tests passed")

if __name__ == "__main__":
    asyncio.run(main())
