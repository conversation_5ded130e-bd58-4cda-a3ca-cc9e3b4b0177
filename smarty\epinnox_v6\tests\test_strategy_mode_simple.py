#!/usr/bin/env python3
"""
🎯 Simple Strategy Mode Test
Verify that strategy mode properly controls timeframe selection
"""

import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_strategy_mode_simple():
    """Simple test that strategy mode properly controls timeframes."""
    
    logger.info("🎯 SIMPLE STRATEGY MODE TEST")
    logger.info("=" * 50)
    
    try:
        # Import required modules
        from autonomy.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        
        # Basic config
        config = {'strategy_mode': 'scalping'}
        
        # Test 1: Scalping Mode
        logger.info("\n⚡ TEST: SCALPING MODE")
        logger.info("-" * 30)
        
        scalping_config = {**config, 'strategy_mode': 'scalping'}
        scalping_mta = MultiTimeframeAnalyzer(scalping_config)
        
        logger.info(f"📊 Scalping timeframes: {scalping_mta.timeframes}")
        
        # Verify scalping excludes 4h
        if '4h' not in scalping_mta.timeframes:
            logger.info("✅ SUCCESS: 4h timeframe correctly excluded from scalping")
        else:
            logger.error("❌ FAIL: 4h timeframe incorrectly included in scalping")
            return False
        
        # Verify scalping includes 1m
        if '1m' in scalping_mta.timeframes:
            logger.info("✅ SUCCESS: 1m timeframe correctly included in scalping")
        else:
            logger.error("❌ FAIL: 1m timeframe missing from scalping")
            return False
        
        # Test 2: Swing Mode
        logger.info("\n🌊 TEST: SWING MODE")
        logger.info("-" * 30)
        
        swing_config = {**config, 'strategy_mode': 'swing'}
        swing_mta = MultiTimeframeAnalyzer(swing_config)
        
        logger.info(f"📊 Swing timeframes: {swing_mta.timeframes}")
        
        # Verify swing includes 4h
        if '4h' in swing_mta.timeframes:
            logger.info("✅ SUCCESS: 4h timeframe correctly included in swing")
        else:
            logger.error("❌ FAIL: 4h timeframe missing from swing")
            return False
        
        # Verify swing excludes 1m
        if '1m' not in swing_mta.timeframes:
            logger.info("✅ SUCCESS: 1m timeframe correctly excluded from swing")
        else:
            logger.error("❌ FAIL: 1m timeframe incorrectly included in swing")
            return False
        
        # Test 3: Dynamic Mode Switching
        logger.info("\n🔄 TEST: DYNAMIC MODE SWITCHING")
        logger.info("-" * 30)
        
        mta = MultiTimeframeAnalyzer(config)
        initial_timeframes = mta.timeframes.copy()
        logger.info(f"📊 Initial timeframes: {initial_timeframes}")
        
        # Switch to swing mode
        mta.set_strategy_mode('swing')
        new_timeframes = mta.timeframes
        logger.info(f"📊 After switch to swing: {new_timeframes}")
        
        if initial_timeframes != new_timeframes:
            logger.info("✅ SUCCESS: Timeframes changed when strategy mode switched")
        else:
            logger.error("❌ FAIL: Timeframes didn't change when strategy mode switched")
            return False
        
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("=" * 50)
        logger.info("✅ Strategy mode fix is working correctly!")
        logger.info("🎯 Scalping will no longer use 4h data")
        logger.info("🌊 Swing will use 4h data appropriately")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_strategy_mode_simple()
    if success:
        print("\n🎯 STRATEGY MODE FIX: VERIFIED ✅")
    else:
        print("\n❌ STRATEGY MODE FIX: FAILED")
        exit(1)
