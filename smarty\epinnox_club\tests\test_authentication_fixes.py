#!/usr/bin/env python3
"""
Test Authentication and Security Fixes
Tests the fixes for dropdown positioning, database connection issues, and CSRF token handling.
"""

import requests
import time
import json
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:8087"

def test_server_health():
    """Test if server is running and healthy."""
    print("🔍 Testing Server Health...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            print("  ✅ Server is running and healthy")
            return True
        else:
            print(f"  ❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Server health check error: {e}")
        return False

def test_authentication_flow():
    """Test the complete authentication flow."""
    print("\n🔐 Testing Authentication Flow...")
    
    session = requests.Session()
    
    # Test 1: Access dashboard without login (should redirect to login)
    try:
        response = session.get(f"{BASE_URL}/dashboard", allow_redirects=False)
        if response.status_code == 302 and '/login' in response.headers.get('Location', ''):
            print("  ✅ Unauthenticated access correctly redirects to login")
        else:
            print(f"  ❌ Unexpected response for unauthenticated access: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Authentication test error: {e}")
        return False
    
    # Test 2: Login with correct credentials
    try:
        # Get login page first to get CSRF token
        login_page = session.get(f"{BASE_URL}/login")
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token.get('value')
        
        response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302 and '/dashboard' in response.headers.get('Location', ''):
            print("  ✅ Login successful, redirects to dashboard")
        else:
            print(f"  ❌ Login failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Login test error: {e}")
        return False
    
    # Test 3: Access dashboard after login (should work without agreement redirect)
    try:
        response = session.get(f"{BASE_URL}/dashboard")
        if response.status_code == 200:
            print("  ✅ Dashboard accessible after login (no agreement redirect loop)")
        else:
            print(f"  ❌ Dashboard access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Dashboard access test error: {e}")
        return False
    
    # Test 4: Access admin page (should work for admin user)
    try:
        response = session.get(f"{BASE_URL}/admin")
        if response.status_code == 200:
            print("  ✅ Admin page accessible for admin user")
        else:
            print(f"  ❌ Admin page access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Admin page test error: {e}")
        return False
    
    return True

def test_api_endpoints():
    """Test API endpoints work without agreement redirect."""
    print("\n🔌 Testing API Endpoints...")
    
    session = requests.Session()
    
    # Login first
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }
    session.post(f"{BASE_URL}/login", data=login_data)
    
    # Test API endpoints
    api_endpoints = [
        '/api/notifications/count',
        '/api/portfolio',
        '/api/market-data/symbols'
    ]
    
    success_count = 0
    for endpoint in api_endpoints:
        try:
            response = session.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"  ✅ {endpoint} - Working")
                success_count += 1
            else:
                print(f"  ❌ {endpoint} - Failed ({response.status_code})")
        except Exception as e:
            print(f"  ❌ {endpoint} - Error: {e}")
    
    print(f"  📊 API Endpoints: {success_count}/{len(api_endpoints)} working")
    return success_count == len(api_endpoints)

def test_dropdown_positioning():
    """Test that dropdown positioning CSS and JS are loaded."""
    print("\n🎯 Testing Dropdown Positioning...")
    
    session = requests.Session()
    
    # Login first
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }
    session.post(f"{BASE_URL}/login", data=login_data)
    
    # Get dashboard page
    try:
        response = session.get(f"{BASE_URL}/dashboard")
        content = response.text
        
        # Check for CSS positioning rules
        css_response = session.get(f"{BASE_URL}/static/css/unified_header.css")
        css_content = css_response.text
        
        positioning_rules = [
            '.user-profile-dropdown {',
            'position: relative;',
            '.user-dropdown-menu {',
            'position: absolute;',
            'top: calc(100% + 8px);'
        ]
        
        css_found = 0
        for rule in positioning_rules:
            if rule in css_content:
                css_found += 1
        
        print(f"  ✅ CSS positioning rules: {css_found}/{len(positioning_rules)} found")
        
        # Check for JavaScript positioning functions
        js_response = session.get(f"{BASE_URL}/static/js/header_navigation.js")
        js_content = js_response.text
        
        js_functions = [
            'positionNotificationsPanel',
            'toggleUserDropdown',
            'toggleNotifications'
        ]
        
        js_found = 0
        for func in js_functions:
            if func in js_content:
                js_found += 1
        
        print(f"  ✅ JavaScript functions: {js_found}/{len(js_functions)} found")
        
        return css_found >= 4 and js_found >= 2
        
    except Exception as e:
        print(f"  ❌ Dropdown positioning test error: {e}")
        return False

def test_csrf_protection():
    """Test CSRF token handling in agreement form."""
    print("\n🛡️ Testing CSRF Protection...")
    
    session = requests.Session()
    
    # Login first
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }
    session.post(f"{BASE_URL}/login", data=login_data)
    
    try:
        # Get agreement page
        response = session.get(f"{BASE_URL}/agreement")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_token = soup.find('input', {'name': 'csrf_token'})
            
            if csrf_token:
                print("  ✅ CSRF token found in agreement form")
                return True
            else:
                print("  ❌ CSRF token not found in agreement form")
                return False
        else:
            print(f"  ❌ Agreement page not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ CSRF protection test error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Money Circle Authentication & Security Fixes Test")
    print("=" * 60)
    
    tests = [
        ("Server Health", test_server_health),
        ("Authentication Flow", test_authentication_flow),
        ("API Endpoints", test_api_endpoints),
        ("Dropdown Positioning", test_dropdown_positioning),
        ("CSRF Protection", test_csrf_protection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
        
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Authentication and security fixes are working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    main()
