"""
System Health Monitor
Comprehensive system health monitoring and automatic recovery for Money Circle platform
"""

import asyncio
import logging
import time
import traceback
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """System health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    FAILED = "failed"


@dataclass
class HealthCheck:
    """Individual health check configuration."""
    name: str
    check_function: Callable[[], bool]
    recovery_function: Optional[Callable[[], bool]] = None
    interval: int = 60  # seconds
    timeout: int = 30   # seconds
    max_failures: int = 3
    critical: bool = False
    
    # Runtime state
    last_check: Optional[datetime] = field(default=None, init=False)
    last_success: Optional[datetime] = field(default=None, init=False)
    failure_count: int = field(default=0, init=False)
    status: HealthStatus = field(default=HealthStatus.HEALTHY, init=False)
    last_error: Optional[str] = field(default=None, init=False)


class SystemHealthMonitor:
    """Comprehensive system health monitoring with automatic recovery."""
    
    def __init__(self):
        self.health_checks: Dict[str, HealthCheck] = {}
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.overall_status = HealthStatus.HEALTHY
        
        # Callbacks
        self.on_status_change: Optional[Callable[[str, HealthStatus], None]] = None
        self.on_recovery_attempt: Optional[Callable[[str], None]] = None
        self.on_critical_failure: Optional[Callable[[str], None]] = None
        
        # Statistics
        self.start_time: Optional[datetime] = None
        self.total_checks = 0
        self.total_failures = 0
        self.total_recoveries = 0
    
    def add_health_check(self, health_check: HealthCheck):
        """Add a health check to the monitor."""
        self.health_checks[health_check.name] = health_check
        logger.info(f"[HEALTH] Added health check: {health_check.name}")
    
    def remove_health_check(self, name: str):
        """Remove a health check from the monitor."""
        if name in self.health_checks:
            del self.health_checks[name]
            logger.info(f"[HEALTH] Removed health check: {name}")
    
    async def start(self):
        """Start the health monitoring system."""
        if self.running:
            return
        
        self.running = True
        self.start_time = datetime.now()
        
        logger.info("[HEALTH] Starting system health monitor...")
        
        self.monitor_task = asyncio.create_task(self._monitor_loop())
    
    async def stop(self):
        """Stop the health monitoring system."""
        self.running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("[HEALTH] System health monitor stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop."""
        while self.running:
            try:
                await self._run_health_checks()
                await self._update_overall_status()
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"[HEALTH] Monitor loop error: {e}")
                await asyncio.sleep(30)
    
    async def _run_health_checks(self):
        """Run all health checks that are due."""
        current_time = datetime.now()
        
        for name, check in self.health_checks.items():
            # Skip if not due for check
            if check.last_check:
                time_since_check = (current_time - check.last_check).total_seconds()
                if time_since_check < check.interval:
                    continue
            
            await self._run_single_check(check)
    
    async def _run_single_check(self, check: HealthCheck):
        """Run a single health check."""
        check.last_check = datetime.now()
        self.total_checks += 1
        
        try:
            # Run the check with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(check.check_function),
                timeout=check.timeout
            )
            
            if result:
                # Check passed
                if check.failure_count > 0:
                    logger.info(f"[HEALTH] ✅ {check.name} recovered")
                
                check.failure_count = 0
                check.status = HealthStatus.HEALTHY
                check.last_success = datetime.now()
                check.last_error = None
                
            else:
                # Check failed
                await self._handle_check_failure(check, "Check returned False")
                
        except asyncio.TimeoutError:
            await self._handle_check_failure(check, f"Check timed out after {check.timeout}s")
            
        except Exception as e:
            await self._handle_check_failure(check, str(e))
    
    async def _handle_check_failure(self, check: HealthCheck, error: str):
        """Handle a health check failure."""
        check.failure_count += 1
        check.last_error = error
        self.total_failures += 1
        
        logger.warning(f"[HEALTH] ❌ {check.name} failed ({check.failure_count}/{check.max_failures}): {error}")
        
        # Determine status based on failure count
        if check.failure_count >= check.max_failures:
            if check.critical:
                check.status = HealthStatus.CRITICAL
                if self.on_critical_failure:
                    await self.on_critical_failure(check.name)
            else:
                check.status = HealthStatus.FAILED
        else:
            check.status = HealthStatus.WARNING
        
        # Notify status change
        if self.on_status_change:
            await self.on_status_change(check.name, check.status)
        
        # Attempt recovery if available
        if check.recovery_function and check.failure_count >= 2:
            await self._attempt_recovery(check)
    
    async def _attempt_recovery(self, check: HealthCheck):
        """Attempt to recover from a failed health check."""
        if not check.recovery_function:
            return
        
        logger.info(f"[HEALTH] 🔄 Attempting recovery for {check.name}")
        
        if self.on_recovery_attempt:
            await self.on_recovery_attempt(check.name)
        
        try:
            recovery_result = await asyncio.wait_for(
                asyncio.to_thread(check.recovery_function),
                timeout=check.timeout * 2
            )
            
            if recovery_result:
                logger.info(f"[HEALTH] ✅ Recovery successful for {check.name}")
                self.total_recoveries += 1
                check.failure_count = 0  # Reset failure count on successful recovery
            else:
                logger.warning(f"[HEALTH] ❌ Recovery failed for {check.name}")
                
        except Exception as e:
            logger.error(f"[HEALTH] Recovery error for {check.name}: {e}")
    
    async def _update_overall_status(self):
        """Update overall system status based on individual checks."""
        if not self.health_checks:
            self.overall_status = HealthStatus.HEALTHY
            return
        
        critical_failures = sum(1 for check in self.health_checks.values() 
                               if check.status == HealthStatus.CRITICAL)
        
        failed_checks = sum(1 for check in self.health_checks.values() 
                           if check.status == HealthStatus.FAILED)
        
        warning_checks = sum(1 for check in self.health_checks.values() 
                            if check.status == HealthStatus.WARNING)
        
        if critical_failures > 0:
            new_status = HealthStatus.CRITICAL
        elif failed_checks > 0:
            new_status = HealthStatus.FAILED
        elif warning_checks > 0:
            new_status = HealthStatus.WARNING
        else:
            new_status = HealthStatus.HEALTHY
        
        if new_status != self.overall_status:
            old_status = self.overall_status
            self.overall_status = new_status
            logger.info(f"[HEALTH] Overall status changed: {old_status.value} → {new_status.value}")
    
    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report."""
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        checks_report = {}
        for name, check in self.health_checks.items():
            checks_report[name] = {
                'status': check.status.value,
                'failure_count': check.failure_count,
                'max_failures': check.max_failures,
                'last_check': check.last_check.isoformat() if check.last_check else None,
                'last_success': check.last_success.isoformat() if check.last_success else None,
                'last_error': check.last_error,
                'critical': check.critical,
                'interval': check.interval
            }
        
        return {
            'overall_status': self.overall_status.value,
            'uptime_seconds': uptime,
            'total_checks': self.total_checks,
            'total_failures': self.total_failures,
            'total_recoveries': self.total_recoveries,
            'checks': checks_report,
            'summary': {
                'healthy': sum(1 for c in self.health_checks.values() if c.status == HealthStatus.HEALTHY),
                'warning': sum(1 for c in self.health_checks.values() if c.status == HealthStatus.WARNING),
                'failed': sum(1 for c in self.health_checks.values() if c.status == HealthStatus.FAILED),
                'critical': sum(1 for c in self.health_checks.values() if c.status == HealthStatus.CRITICAL)
            }
        }
    
    async def force_check(self, check_name: str) -> bool:
        """Force run a specific health check."""
        if check_name not in self.health_checks:
            return False
        
        check = self.health_checks[check_name]
        await self._run_single_check(check)
        return True
    
    async def force_recovery(self, check_name: str) -> bool:
        """Force recovery attempt for a specific check."""
        if check_name not in self.health_checks:
            return False
        
        check = self.health_checks[check_name]
        if not check.recovery_function:
            return False
        
        await self._attempt_recovery(check)
        return True


# Global health monitor instance
_health_monitor: Optional[SystemHealthMonitor] = None


def get_health_monitor() -> SystemHealthMonitor:
    """Get or create the global health monitor."""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = SystemHealthMonitor()
    return _health_monitor


async def test_health_monitor():
    """Test the health monitoring system."""
    logger.info("[TEST] Testing health monitor...")
    
    monitor = SystemHealthMonitor()
    
    # Add test health checks
    def always_pass():
        return True
    
    def sometimes_fail():
        import random
        return random.random() > 0.3
    
    def recovery_function():
        logger.info("[TEST] Running recovery function")
        return True
    
    monitor.add_health_check(HealthCheck(
        name="test_pass",
        check_function=always_pass,
        interval=5
    ))
    
    monitor.add_health_check(HealthCheck(
        name="test_fail",
        check_function=sometimes_fail,
        recovery_function=recovery_function,
        interval=5,
        max_failures=2
    ))
    
    await monitor.start()
    
    # Let it run for 30 seconds
    await asyncio.sleep(30)
    
    report = monitor.get_health_report()
    logger.info(f"[TEST] Health report: {report}")
    
    await monitor.stop()
    
    return True


if __name__ == "__main__":
    async def main():
        success = await test_health_monitor()
        if success:
            print("✅ Health monitor test passed")
        else:
            print("❌ Health monitor test failed")
    
    asyncio.run(main())
