#!/usr/bin/env python3
"""
Test CoinGecko Windows fix
"""

import asyncio
import sys
from utils.windows_event_loop_fix import CoinGeckoClientFixed

async def test_coingecko():
    try:
        async with CoinGeckoClientFixed() as client:
            data = await client.get_simple_price('bitcoin,ethereum', 'usd')
            if data:
                print('SUCCESS: CoinGecko fix working!')
                for coin, info in data.items():
                    price = info.get('usd', 0)
                    print(f'{coin}: ${price:,.2f}')
                return True
            else:
                print('FAILED: No data returned')
                return False
    except Exception as e:
        print(f'ERROR: {e}')
        return False

if __name__ == "__main__":
    result = asyncio.run(test_coingecko())
    sys.exit(0 if result else 1)
