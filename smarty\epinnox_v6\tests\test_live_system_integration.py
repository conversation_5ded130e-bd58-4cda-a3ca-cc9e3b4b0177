#!/usr/bin/env python3
"""
Comprehensive Live System Integration Test
End-to-end testing of complete Onnyx V6 trading system with all Phase 10 components
"""

import asyncio
import logging
import time
import yaml
import json
import aiohttp
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import all system components
from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from monitoring.production_monitor import ProductionMonitor
from monitoring.alert_system import AdvancedAlertSystem
from optimization.performance_optimizer import PerformanceOptimizer
from security.security_manager import SecurityManager

class LiveSystemIntegrationTest:
    """Comprehensive live system integration test suite."""

    def __init__(self):
        self.test_results = {}
        self.critical_issues = []
        self.warnings = []
        self.start_time = time.time()

        # Component references
        self.data_store = None
        self.execution_controller = None
        self.dashboard = None
        self.production_monitor = None
        self.alert_system = None
        self.performance_optimizer = None
        self.security_manager = None

        # Test configuration
        self.test_symbol = "DOGE/USDT:USDT"
        self.test_duration = 300  # 5 minutes of live testing
        self.dashboard_url = "http://localhost:8086"

    async def run_comprehensive_test(self):
        """Execute comprehensive live system integration test."""
        try:
            logger.info("🧪 COMPREHENSIVE LIVE SYSTEM INTEGRATION TEST")
            logger.info("="*80)
            logger.info("🚀 Testing complete Onnyx V6 system with all Phase 10 components")
            logger.info("="*80)

            # Load configuration
            config = await self._load_configuration()

            # Test 1: System Initialization
            await self._test_system_initialization(config)

            # Test 2: Component Integration
            await self._test_component_integration()

            # Test 3: Live Data Feed Testing
            await self._test_live_data_feeds()

            # Test 4: Security System Verification
            await self._test_security_systems()

            # Test 5: Performance Optimization Validation
            await self._test_performance_optimization()

            # Test 6: Production Monitoring Validation
            await self._test_production_monitoring()

            # Test 7: Alert System Testing
            await self._test_alert_systems()

            # Test 8: Dashboard Interface Testing
            await self._test_dashboard_interface()

            # Test 9: Manual Trading Controls
            await self._test_manual_trading_controls()

            # Test 10: Account Safety Verification
            await self._test_account_safety_systems()

            # Test 11: End-to-End Workflow Testing
            await self._test_end_to_end_workflow()

            # Test 12: Live System Stress Testing
            await self._test_system_under_load()

            # Generate final report
            await self._generate_final_report()

        except Exception as e:
            logger.error(f"❌ Critical error in live system integration test: {e}")
            self.critical_issues.append(f"Test execution failure: {e}")
            raise

    async def _load_configuration(self):
        """Load and validate system configuration."""
        logger.info("\n🎯 Loading and validating system configuration...")

        try:
            script_dir = Path(__file__).parent.parent
            config_path = script_dir / "config" / "strategy.yaml"

            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Add live testing configurations
            config.update({
                'production_monitor': {
                    'system_interval': 10,
                    'trading_interval': 5,
                    'alert_check_interval': 3
                },
                'alert_system': {
                    'channels': {
                        'discord': {
                            'enabled': False,  # Disable for testing
                            'webhook_url': 'test_webhook'
                        },
                        'webhook': {
                            'enabled': True,
                            'url': 'http://httpbin.org/post'
                        }
                    }
                },
                'performance_optimization': {
                    'enable_caching': True,
                    'enable_batching': True,
                    'enable_threading': True,
                    'cache_ttl': 30
                },
                'security': {
                    'api_key_encryption': True,
                    'rate_limiting_enabled': True,
                    'audit_logging': True,
                    'secure_headers': True
                }
            })

            logger.info("✅ Configuration loaded and enhanced for live testing")
            return config

        except Exception as e:
            logger.error(f"❌ Configuration loading failed: {e}")
            self.critical_issues.append(f"Configuration error: {e}")
            raise

    async def _test_system_initialization(self, config):
        """Test 1: System Initialization."""
        logger.info("\n🎯 Test 1: System Initialization")
        logger.info("="*50)

        try:
            # Initialize core components
            logger.info("   Initializing core components...")
            self.data_store = LiveDataStore(config)
            self.execution_controller = ExecutionController(config)
            self.dashboard = AIStrategyTunerDashboard(config, self.data_store, self.execution_controller)

            # Initialize Phase 10 components
            logger.info("   Initializing Phase 10 components...")
            self.production_monitor = ProductionMonitor(config)
            self.alert_system = AdvancedAlertSystem(config)
            self.performance_optimizer = PerformanceOptimizer(config)
            self.security_manager = SecurityManager(config)

            logger.info("✅ All system components initialized successfully")
            self.test_results['system_initialization'] = True

        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            self.critical_issues.append(f"Initialization failure: {e}")
            self.test_results['system_initialization'] = False

    async def _test_component_integration(self):
        """Test 2: Component Integration."""
        logger.info("\n🎯 Test 2: Component Integration")
        logger.info("="*50)

        try:
            # Set up component integrations
            logger.info("   Setting up component integrations...")

            # Production monitor integrations
            self.production_monitor.set_integrations(
                self.data_store,
                self.execution_controller,
                self.dashboard
            )

            # Alert system integrations
            self.alert_system.set_production_monitor(self.production_monitor)

            # Performance optimizer integrations
            self.performance_optimizer.set_integrations(
                self.data_store,
                self.execution_controller,
                self.dashboard
            )

            # Test cross-component communication
            logger.info("   Testing cross-component communication...")

            # Test data flow
            test_signal = {
                'symbol': self.test_symbol,
                'action': 'LONG',
                'confidence': 0.8,
                'timestamp': time.time()
            }

            # Store signal and verify retrieval (FIX: store_signal takes only signal_data)
            test_signal['symbol'] = self.test_symbol  # Add symbol to signal data
            self.data_store.store_signal(test_signal)
            retrieved_signals = self.data_store.get_signals(self.test_symbol, limit=1)

            if retrieved_signals and len(retrieved_signals) > 0:
                logger.info("✅ Data store integration working")
            else:
                self.warnings.append("Data store integration may have issues")

            logger.info("✅ Component integration completed successfully")
            self.test_results['component_integration'] = True

        except Exception as e:
            logger.error(f"❌ Component integration failed: {e}")
            self.critical_issues.append(f"Integration failure: {e}")
            self.test_results['component_integration'] = False

    async def _test_live_data_feeds(self):
        """Test 3: Live Data Feed Testing."""
        logger.info("\n🎯 Test 3: Live Data Feed Testing")
        logger.info("="*50)

        try:
            logger.info("   Testing HTX WebSocket connection...")

            # Test data store connection
            if hasattr(self.data_store, 'test_connection'):
                connection_ok = await self.data_store.test_connection()
                if connection_ok:
                    logger.info("✅ HTX WebSocket connection successful")
                else:
                    self.warnings.append("HTX WebSocket connection issues detected")

            # Test real-time data flow
            logger.info("   Testing real-time data flow...")

            # Get recent market data
            market_data = self.data_store.get_market_data(self.test_symbol)
            if market_data and 'price' in market_data:
                current_price = market_data['price']
                logger.info(f"✅ Live market data: {self.test_symbol} = ${current_price}")
            else:
                self.warnings.append("Market data may not be updating")

            # Test data freshness
            signals = self.data_store.get_signals(self.test_symbol, limit=5)
            if signals:
                latest_signal_time = max(s.get('timestamp', 0) for s in signals)
                data_age = time.time() - latest_signal_time

                if data_age < 300:  # Less than 5 minutes old
                    logger.info(f"✅ Data freshness good: {data_age:.1f} seconds old")
                else:
                    self.warnings.append(f"Data may be stale: {data_age:.1f} seconds old")

            logger.info("✅ Live data feed testing completed")
            self.test_results['live_data_feeds'] = True

        except Exception as e:
            logger.error(f"❌ Live data feed testing failed: {e}")
            self.critical_issues.append(f"Data feed failure: {e}")
            self.test_results['live_data_feeds'] = False

    async def _test_security_systems(self):
        """Test 4: Security System Verification."""
        logger.info("\n🎯 Test 4: Security System Verification")
        logger.info("="*50)

        try:
            logger.info("   Testing API key encryption...")

            # Test encryption/decryption
            test_api_key = "test_key_12345"
            encrypted = self.security_manager.encrypt_sensitive_data(test_api_key)
            decrypted = self.security_manager.decrypt_sensitive_data(encrypted)

            if decrypted == test_api_key:
                logger.info("✅ API key encryption working correctly")
            else:
                self.critical_issues.append("API key encryption failure")

            # Test input validation
            logger.info("   Testing input validation...")

            valid_input = "DOGE/USDT:USDT"
            invalid_input = "<script>alert('xss')</script>"

            valid_result, _ = self.security_manager.validate_input(valid_input, 'symbol')
            invalid_result, _ = self.security_manager.validate_input(invalid_input, 'general')

            if valid_result and not invalid_result:
                logger.info("✅ Input validation working correctly")
            else:
                self.critical_issues.append("Input validation failure")

            # Test rate limiting
            logger.info("   Testing rate limiting...")

            test_ip = "127.0.0.1"
            allowed_count = 0

            for i in range(10):
                allowed, _ = self.security_manager.check_rate_limit(test_ip, "/api/test")
                if allowed:
                    allowed_count += 1

            if allowed_count > 0:
                logger.info(f"✅ Rate limiting functional: {allowed_count}/10 requests allowed")
            else:
                self.warnings.append("Rate limiting may be too restrictive")

            # Run security scan
            logger.info("   Running security vulnerability scan...")
            scan_results = self.security_manager.run_security_scan()

            if 'error' not in scan_results:
                security_score = scan_results.get('security_score', 0)
                logger.info(f"✅ Security scan completed: Score {security_score}/100")

                if security_score < 70:
                    self.warnings.append(f"Security score below recommended: {security_score}/100")
            else:
                self.warnings.append("Security scan failed")

            logger.info("✅ Security system verification completed")
            self.test_results['security_systems'] = True

        except Exception as e:
            logger.error(f"❌ Security system testing failed: {e}")
            self.critical_issues.append(f"Security failure: {e}")
            self.test_results['security_systems'] = False

    async def _test_performance_optimization(self):
        """Test 5: Performance Optimization Validation."""
        logger.info("\n🎯 Test 5: Performance Optimization Validation")
        logger.info("="*50)

        try:
            logger.info("   Testing signal processing optimization...")

            # Create test signals
            test_signals = [
                {'id': i, 'symbol': self.test_symbol, 'action': 'LONG', 'confidence': 0.8}
                for i in range(20)
            ]

            # Test optimization
            start_time = time.time()
            optimized_signals = self.performance_optimizer.optimize_signal_processing(test_signals)
            processing_time = (time.time() - start_time) * 1000

            if len(optimized_signals) == len(test_signals):
                logger.info(f"✅ Signal optimization working: {processing_time:.1f}ms for {len(test_signals)} signals")
            else:
                self.warnings.append("Signal optimization may have issues")

            # Test caching
            logger.info("   Testing caching system...")

            # Run same optimization again (should be cached)
            start_time = time.time()
            cached_signals = self.performance_optimizer.optimize_signal_processing(test_signals)
            cached_time = (time.time() - start_time) * 1000

            if cached_time < processing_time:
                logger.info(f"✅ Caching working: {cached_time:.1f}ms (cached) vs {processing_time:.1f}ms (original)")
            else:
                self.warnings.append("Caching may not be effective")

            # Test performance benchmark
            logger.info("   Running performance benchmark...")

            benchmark_results = await self.performance_optimizer.run_performance_benchmark()

            if 'error' not in benchmark_results:
                overall_score = benchmark_results.get('overall', {}).get('performance_score', 0)
                logger.info(f"✅ Performance benchmark: Score {overall_score}/100")

                if overall_score < 50:
                    self.warnings.append(f"Performance score below recommended: {overall_score}/100")
            else:
                self.warnings.append("Performance benchmark failed")

            logger.info("✅ Performance optimization validation completed")
            self.test_results['performance_optimization'] = True

        except Exception as e:
            logger.error(f"❌ Performance optimization testing failed: {e}")
            self.warnings.append(f"Performance optimization issue: {e}")
            self.test_results['performance_optimization'] = False

    async def _test_production_monitoring(self):
        """Test 6: Production Monitoring Validation."""
        logger.info("\n🎯 Test 6: Production Monitoring Validation")
        logger.info("="*50)

        try:
            logger.info("   Starting production monitoring...")

            # Start monitoring for a short period
            monitoring_task = asyncio.create_task(self.production_monitor.start_monitoring())

            # Let it run for 30 seconds
            await asyncio.sleep(30)

            # Stop monitoring
            self.production_monitor.stop_monitoring()
            monitoring_task.cancel()

            # Check collected metrics
            current_metrics = self.production_monitor.get_current_metrics()

            if current_metrics and 'system' in current_metrics:
                system_metrics = current_metrics['system']
                logger.info(f"✅ System monitoring working:")
                logger.info(f"   CPU: {system_metrics.get('cpu_percent', 0):.1f}%")
                logger.info(f"   Memory: {system_metrics.get('memory_percent', 0):.1f}%")
                logger.info(f"   Uptime: {system_metrics.get('uptime_hours', 0):.2f}h")
            else:
                self.warnings.append("System monitoring may not be collecting data")

            if current_metrics and 'trading' in current_metrics:
                trading_metrics = current_metrics['trading']
                logger.info(f"✅ Trading monitoring working:")
                logger.info(f"   Signals/min: {trading_metrics.get('signals_per_minute', 0):.1f}")
                logger.info(f"   Latency: {trading_metrics.get('execution_latency_ms', 0):.0f}ms")
                logger.info(f"   Active positions: {trading_metrics.get('active_positions', 0)}")
            else:
                self.warnings.append("Trading monitoring may not be collecting data")

            # Test health score
            health = self.production_monitor.get_health_score()
            if health and 'score' in health:
                health_score = health['score']
                logger.info(f"✅ Health monitoring working: Score {health_score}/100")

                if health_score < 50:
                    self.warnings.append(f"System health score low: {health_score}/100")
            else:
                self.warnings.append("Health score calculation may have issues")

            logger.info("✅ Production monitoring validation completed")
            self.test_results['production_monitoring'] = True

        except Exception as e:
            logger.error(f"❌ Production monitoring testing failed: {e}")
            self.warnings.append(f"Production monitoring issue: {e}")
            self.test_results['production_monitoring'] = False

    async def _test_alert_systems(self):
        """Test 7: Alert System Testing."""
        logger.info("\n🎯 Test 7: Alert System Testing")
        logger.info("="*50)

        try:
            logger.info("   Testing alert channel configuration...")

            # Test channel setup
            alert_status = self.alert_system.get_alert_status()

            if 'error' not in alert_status:
                enabled_channels = alert_status.get('enabled_channels', [])
                total_rules = alert_status.get('total_rules', 0)

                logger.info(f"✅ Alert system configured:")
                logger.info(f"   Enabled channels: {', '.join(enabled_channels) if enabled_channels else 'None'}")
                logger.info(f"   Total rules: {total_rules}")
            else:
                self.warnings.append("Alert system status retrieval failed")

            # Test webhook channel
            logger.info("   Testing webhook alert channel...")

            test_results = await self.alert_system.test_channels()

            webhook_success = test_results.get('webhook', False)
            if webhook_success:
                logger.info("✅ Webhook alert channel working")
            else:
                self.warnings.append("Webhook alert channel may have issues")

            # Test alert rule evaluation
            logger.info("   Testing alert rule evaluation...")

            # Manually trigger alert check
            try:
                await self.alert_system._check_alert_rules()
                logger.info("✅ Alert rule evaluation completed")
            except Exception as e:
                self.warnings.append(f"Alert rule evaluation issue: {e}")

            # Check for generated alerts
            recent_alerts = [a for a in self.alert_system.alerts if time.time() - a.timestamp < 300]
            logger.info(f"✅ Recent alerts generated: {len(recent_alerts)}")

            logger.info("✅ Alert system testing completed")
            self.test_results['alert_systems'] = True

        except Exception as e:
            logger.error(f"❌ Alert system testing failed: {e}")
            self.warnings.append(f"Alert system issue: {e}")
            self.test_results['alert_systems'] = False

    async def _test_dashboard_interface(self):
        """Test 8: Dashboard Interface Testing."""
        logger.info("\n🎯 Test 8: Dashboard Interface Testing")
        logger.info("="*50)

        try:
            logger.info("   Testing dashboard server startup...")

            # Start dashboard server
            dashboard_task = asyncio.create_task(self.dashboard.start_server())

            # Wait for server to start
            await asyncio.sleep(5)

            # Test API endpoints
            logger.info("   Testing dashboard API endpoints...")

            async with aiohttp.ClientSession() as session:
                # Test status endpoint
                try:
                    async with session.get(f"{self.dashboard_url}/api/status", timeout=10) as response:
                        if response.status == 200:
                            status_data = await response.json()
                            logger.info(f"✅ Status API working: {status_data.get('status', 'unknown')}")
                        else:
                            self.warnings.append(f"Status API returned {response.status}")
                except Exception as e:
                    self.warnings.append(f"Status API connection failed: {e}")

                # Test config endpoint
                try:
                    async with session.get(f"{self.dashboard_url}/api/config", timeout=10) as response:
                        if response.status == 200:
                            config_data = await response.json()
                            logger.info(f"✅ Config API working: {len(config_data.get('symbols', []))} symbols")
                        else:
                            self.warnings.append(f"Config API returned {response.status}")
                except Exception as e:
                    self.warnings.append(f"Config API connection failed: {e}")

                # Test dashboard main page
                try:
                    async with session.get(f"{self.dashboard_url}/", timeout=10) as response:
                        if response.status == 200:
                            logger.info("✅ Dashboard main page accessible")
                        else:
                            self.warnings.append(f"Dashboard main page returned {response.status}")
                except Exception as e:
                    self.warnings.append(f"Dashboard main page connection failed: {e}")

            # Stop dashboard server
            dashboard_task.cancel()

            logger.info("✅ Dashboard interface testing completed")
            self.test_results['dashboard_interface'] = True

        except Exception as e:
            logger.error(f"❌ Dashboard interface testing failed: {e}")
            self.warnings.append(f"Dashboard interface issue: {e}")
            self.test_results['dashboard_interface'] = False

    async def _test_manual_trading_controls(self):
        """Test 9: Manual Trading Controls."""
        logger.info("\n🎯 Test 9: Manual Trading Controls")
        logger.info("="*50)

        try:
            logger.info("   Testing manual trade execution...")

            # Get account tracker
            account_tracker = self.execution_controller.account_tracker

            # Test manual trade function (without actually executing)
            if hasattr(self.dashboard, '_execute_manual_trade_live'):
                try:
                    # Test with very small size to minimize risk
                    result = await self.dashboard._execute_manual_trade_live(
                        action='LONG',
                        symbol=self.test_symbol,
                        size=0.01,  # Very small test size
                        account_tracker=account_tracker
                    )

                    if isinstance(result, dict):
                        if 'error' in result:
                            logger.info(f"✅ Manual trade validation working: {result['error']}")
                        else:
                            logger.info(f"✅ Manual trade execution available: {result.get('message', 'Success')}")
                    else:
                        self.warnings.append("Manual trade function returned unexpected format")

                except Exception as e:
                    logger.info(f"✅ Manual trade error handling working: {type(e).__name__}")
            else:
                self.warnings.append("Manual trade function not available")

            # Test account safety checks
            logger.info("   Testing account safety checks...")

            account_summary = account_tracker.get_account_summary()
            if account_summary:
                balance = account_summary.get('balance', 0)
                margin_usage = account_summary.get('margin_usage_percent', 0)

                logger.info(f"✅ Account monitoring working:")
                logger.info(f"   Balance: ${balance:.2f}")
                logger.info(f"   Margin usage: {margin_usage:.1f}%")

                if balance < 10:  # Less than $10
                    self.warnings.append(f"Low account balance: ${balance:.2f}")

                if margin_usage > 80:  # High margin usage
                    self.warnings.append(f"High margin usage: {margin_usage:.1f}%")
            else:
                self.warnings.append("Account summary not available")

            logger.info("✅ Manual trading controls testing completed")
            self.test_results['manual_trading_controls'] = True

        except Exception as e:
            logger.error(f"❌ Manual trading controls testing failed: {e}")
            self.warnings.append(f"Manual trading controls issue: {e}")
            self.test_results['manual_trading_controls'] = False

    async def _test_account_safety_systems(self):
        """Test 10: Account Safety Verification."""
        logger.info("\n🎯 Test 10: Account Safety Verification")
        logger.info("="*50)

        try:
            logger.info("   Testing risk management systems...")

            # Test position limits
            account_tracker = self.execution_controller.account_tracker
            position_info = account_tracker.get_position_info()

            if position_info:
                has_position = position_info.get('has_position', False)
                position_size = position_info.get('position_size', 0)

                logger.info(f"✅ Position monitoring working:")
                logger.info(f"   Has position: {has_position}")
                logger.info(f"   Position size: {position_size}")

                # Check position limits
                max_position_size = 4.0  # From config
                if abs(position_size) > max_position_size:
                    self.critical_issues.append(f"Position size exceeds limit: {position_size} > {max_position_size}")
            else:
                self.warnings.append("Position information not available")

            # Test margin checks
            logger.info("   Testing margin safety checks...")

            account_summary = account_tracker.get_account_summary()
            if account_summary:
                margin_usage = account_summary.get('margin_usage_percent', 0)
                daily_pnl = account_summary.get('daily_pnl', 0)

                logger.info(f"✅ Margin monitoring working:")
                logger.info(f"   Margin usage: {margin_usage:.1f}%")
                logger.info(f"   Daily P&L: ${daily_pnl:.2f}")

                # Check safety thresholds
                if margin_usage > 90:
                    self.critical_issues.append(f"Margin usage critical: {margin_usage:.1f}%")
                elif margin_usage > 70:
                    self.warnings.append(f"Margin usage high: {margin_usage:.1f}%")

                if daily_pnl < -2.5:  # Daily loss limit
                    self.critical_issues.append(f"Daily loss limit exceeded: ${daily_pnl:.2f}")
            else:
                self.warnings.append("Account summary not available")

            # Test circuit breaker
            logger.info("   Testing circuit breaker system...")

            if hasattr(self.execution_controller, 'circuit_breaker'):
                circuit_breaker = self.execution_controller.circuit_breaker
                if circuit_breaker:
                    is_active = getattr(circuit_breaker, 'is_active', True)
                    logger.info(f"✅ Circuit breaker status: {'ACTIVE' if is_active else 'TRIGGERED'}")

                    if not is_active:
                        self.critical_issues.append("Circuit breaker has been triggered")
                else:
                    self.warnings.append("Circuit breaker not configured")
            else:
                self.warnings.append("Circuit breaker system not available")

            logger.info("✅ Account safety verification completed")
            self.test_results['account_safety_systems'] = True

        except Exception as e:
            logger.error(f"❌ Account safety testing failed: {e}")
            self.critical_issues.append(f"Account safety issue: {e}")
            self.test_results['account_safety_systems'] = False

    async def _test_end_to_end_workflow(self):
        """Test 11: End-to-End Workflow Testing."""
        logger.info("\n🎯 Test 11: End-to-End Workflow Testing")
        logger.info("="*50)

        try:
            logger.info("   Testing complete data flow pipeline...")

            # Step 1: Data ingestion
            logger.info("   Step 1: Testing data ingestion...")
            market_data = self.data_store.get_market_data(self.test_symbol)
            if market_data and 'price' in market_data:
                logger.info(f"✅ Data ingestion: Current price ${market_data['price']}")
            else:
                self.warnings.append("Data ingestion may have issues")

            # Step 2: Signal generation
            logger.info("   Step 2: Testing signal generation...")
            signals = self.data_store.get_signals(self.test_symbol, limit=5)
            if signals and len(signals) > 0:
                latest_signal = signals[0]
                signal_age = time.time() - latest_signal.get('timestamp', 0)
                logger.info(f"✅ Signal generation: Latest signal {signal_age:.1f}s old")

                if signal_age > 600:  # Older than 10 minutes
                    self.warnings.append(f"Signals may be stale: {signal_age:.1f}s old")
            else:
                self.warnings.append("No signals found - signal generation may have issues")

            # Step 3: Performance optimization
            logger.info("   Step 3: Testing performance optimization...")
            if signals:
                optimized_signals = self.performance_optimizer.optimize_signal_processing(signals)
                if len(optimized_signals) == len(signals):
                    logger.info("✅ Performance optimization: Signal processing working")
                else:
                    self.warnings.append("Performance optimization may have issues")

            # Step 4: Security validation
            logger.info("   Step 4: Testing security validation...")
            test_input = self.test_symbol
            is_valid, _ = self.security_manager.validate_input(test_input, 'symbol')
            if is_valid:
                logger.info("✅ Security validation: Input validation working")
            else:
                self.warnings.append("Security validation may have issues")

            # Step 5: Monitoring and alerting
            logger.info("   Step 5: Testing monitoring and alerting...")
            health = self.production_monitor.get_health_score()
            if health and 'score' in health:
                logger.info(f"✅ Monitoring: Health score {health['score']}/100")
            else:
                self.warnings.append("Monitoring may have issues")

            # Step 6: Dashboard integration
            logger.info("   Step 6: Testing dashboard integration...")
            dashboard_data = {
                'symbol': self.test_symbol,
                'price': market_data.get('price', 0) if market_data else 0,
                'signals': signals[:3] if signals else [],
                'timestamp': time.time()
            }

            optimized_dashboard_data = self.performance_optimizer.optimize_dashboard_data(dashboard_data)
            if optimized_dashboard_data and '_performance' in optimized_dashboard_data:
                logger.info("✅ Dashboard integration: Data optimization working")
            else:
                self.warnings.append("Dashboard integration may have issues")

            logger.info("✅ End-to-end workflow testing completed")
            self.test_results['end_to_end_workflow'] = True

        except Exception as e:
            logger.error(f"❌ End-to-end workflow testing failed: {e}")
            self.critical_issues.append(f"End-to-end workflow issue: {e}")
            self.test_results['end_to_end_workflow'] = False

    async def _test_system_under_load(self):
        """Test 12: Live System Stress Testing."""
        logger.info("\n🎯 Test 12: Live System Stress Testing")
        logger.info("="*50)

        try:
            logger.info("   Running system stress test...")

            # Start all monitoring systems
            logger.info("   Starting all monitoring systems...")

            monitoring_tasks = []

            # Start production monitoring
            monitoring_tasks.append(asyncio.create_task(self.production_monitor.start_monitoring()))

            # Start performance optimization
            monitoring_tasks.append(asyncio.create_task(self.performance_optimizer.start_optimization()))

            # Start security monitoring
            monitoring_tasks.append(asyncio.create_task(self.security_manager.start_security_monitoring()))

            # Start alert monitoring
            monitoring_tasks.append(asyncio.create_task(self.alert_system.start_monitoring()))

            # Let all systems run under load for 60 seconds
            logger.info("   Running stress test for 60 seconds...")
            await asyncio.sleep(60)

            # Generate some load
            logger.info("   Generating system load...")

            for i in range(50):
                # Generate test signals
                test_signal = {
                    'id': f'stress_test_{i}',
                    'symbol': self.test_symbol,
                    'action': 'LONG' if i % 2 == 0 else 'SHORT',
                    'confidence': 0.7 + (i % 3) * 0.1,
                    'timestamp': time.time()
                }

                # Process through optimization
                self.performance_optimizer.optimize_signal_processing([test_signal])

                # Add to batch queues
                self.performance_optimizer.add_to_signal_batch(test_signal)

                # Test security validation
                self.security_manager.validate_input(f"test_input_{i}", 'general')

                # Small delay to simulate real load
                await asyncio.sleep(0.1)

            # Stop all monitoring systems
            logger.info("   Stopping monitoring systems...")

            self.production_monitor.stop_monitoring()
            self.performance_optimizer.stop_optimization()

            for task in monitoring_tasks:
                task.cancel()

            # Check system state after stress test
            logger.info("   Checking system state after stress test...")

            # Check performance metrics
            performance_summary = self.performance_optimizer.get_performance_summary()
            if 'error' not in performance_summary:
                perf_score = performance_summary.get('system_health', {}).get('performance_score', 0)
                logger.info(f"✅ Performance after stress test: {perf_score}/100")

                if perf_score < 30:
                    self.warnings.append(f"Performance degraded under load: {perf_score}/100")
            else:
                self.warnings.append("Performance summary failed after stress test")

            # Check security status
            security_status = self.security_manager.get_security_status()
            if 'error' not in security_status:
                recent_events = security_status.get('recent_events_1h', 0)
                logger.info(f"✅ Security events during stress test: {recent_events}")

                if recent_events > 100:
                    self.warnings.append(f"High security event count: {recent_events}")
            else:
                self.warnings.append("Security status check failed after stress test")

            logger.info("✅ System stress testing completed")
            self.test_results['system_under_load'] = True

        except Exception as e:
            logger.error(f"❌ System stress testing failed: {e}")
            self.critical_issues.append(f"System stress test issue: {e}")
            self.test_results['system_under_load'] = False

    async def _generate_final_report(self):
        """Generate comprehensive final test report."""
        logger.info("\n" + "="*80)
        logger.info("🏆 COMPREHENSIVE LIVE SYSTEM INTEGRATION TEST RESULTS")
        logger.info("="*80)

        # Calculate overall results
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        test_duration = time.time() - self.start_time

        # Test results summary
        logger.info(f"\n📊 OVERALL TEST RESULTS:")
        logger.info(f"   Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        logger.info(f"   Test duration: {test_duration:.1f} seconds")
        logger.info(f"   Critical issues: {len(self.critical_issues)}")
        logger.info(f"   Warnings: {len(self.warnings)}")

        # Detailed test results
        logger.info(f"\n📋 DETAILED TEST RESULTS:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {status} {test_name.replace('_', ' ').title()}")

        # Critical issues
        if self.critical_issues:
            logger.info(f"\n🚨 CRITICAL ISSUES ({len(self.critical_issues)}):")
            for i, issue in enumerate(self.critical_issues, 1):
                logger.error(f"   {i}. {issue}")
        else:
            logger.info(f"\n✅ NO CRITICAL ISSUES DETECTED")

        # Warnings
        if self.warnings:
            logger.info(f"\n⚠️ WARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                logger.warning(f"   {i}. {warning}")
        else:
            logger.info(f"\n✅ NO WARNINGS")

        # Component status summary
        logger.info(f"\n🎯 COMPONENT STATUS SUMMARY:")

        component_status = {
            'Core System': self.test_results.get('system_initialization', False) and
                          self.test_results.get('component_integration', False),
            'Live Data Feeds': self.test_results.get('live_data_feeds', False),
            'Security Systems': self.test_results.get('security_systems', False),
            'Performance Optimization': self.test_results.get('performance_optimization', False),
            'Production Monitoring': self.test_results.get('production_monitoring', False),
            'Alert Systems': self.test_results.get('alert_systems', False),
            'Dashboard Interface': self.test_results.get('dashboard_interface', False),
            'Manual Trading Controls': self.test_results.get('manual_trading_controls', False),
            'Account Safety': self.test_results.get('account_safety_systems', False),
            'End-to-End Workflow': self.test_results.get('end_to_end_workflow', False),
            'System Under Load': self.test_results.get('system_under_load', False)
        }

        for component, status in component_status.items():
            status_icon = "✅ OPERATIONAL" if status else "❌ ISSUES"
            logger.info(f"   {status_icon} {component}")

        # Production readiness assessment
        logger.info(f"\n🚀 PRODUCTION READINESS ASSESSMENT:")

        operational_components = sum(component_status.values())
        total_components = len(component_status)
        readiness_percent = (operational_components / total_components) * 100

        logger.info(f"   📊 Component readiness: {operational_components}/{total_components} ({readiness_percent:.1f}%)")

        # Determine production readiness
        if len(self.critical_issues) == 0 and success_rate >= 90:
            production_status = "🟢 READY FOR PRODUCTION"
            recommendation = "System is ready for live trading with real funds"
        elif len(self.critical_issues) == 0 and success_rate >= 80:
            production_status = "🟡 MOSTLY READY"
            recommendation = "Address warnings before production deployment"
        elif len(self.critical_issues) <= 2 and success_rate >= 70:
            production_status = "🟡 NEEDS ATTENTION"
            recommendation = "Fix critical issues and address warnings"
        else:
            production_status = "🔴 NOT READY"
            recommendation = "Significant issues must be resolved before production"

        logger.info(f"   🎯 Production status: {production_status}")
        logger.info(f"   💡 Recommendation: {recommendation}")

        # Security assessment
        if self.security_manager:
            try:
                security_status = self.security_manager.get_security_status()
                if 'error' not in security_status:
                    logger.info(f"\n🔒 SECURITY ASSESSMENT:")
                    logger.info(f"   Encryption enabled: {security_status.get('encryption_enabled', False)}")
                    logger.info(f"   Rate limiting enabled: {security_status.get('rate_limiting_enabled', False)}")
                    logger.info(f"   Audit logging enabled: {security_status.get('audit_logging_enabled', False)}")
                    logger.info(f"   Active sessions: {security_status.get('active_sessions', 0)}")
                    logger.info(f"   Blocked IPs: {security_status.get('blocked_ips', 0)}")
            except Exception as e:
                logger.warning(f"Security assessment failed: {e}")

        # Performance assessment
        if self.performance_optimizer:
            try:
                performance_summary = self.performance_optimizer.get_performance_summary()
                if 'error' not in performance_summary:
                    logger.info(f"\n⚡ PERFORMANCE ASSESSMENT:")

                    perf_metrics = performance_summary.get('performance_metrics', {})
                    logger.info(f"   Signal processing: {perf_metrics.get('avg_signal_processing_ms', 0):.1f}ms")
                    logger.info(f"   Dashboard response: {perf_metrics.get('avg_dashboard_response_ms', 0):.1f}ms")
                    logger.info(f"   Cache hit rate: {perf_metrics.get('avg_cache_hit_rate_percent', 0):.1f}%")

                    health = performance_summary.get('system_health', {})
                    perf_score = health.get('performance_score', 0)
                    logger.info(f"   Performance score: {perf_score}/100")
            except Exception as e:
                logger.warning(f"Performance assessment failed: {e}")

        # Final recommendations
        logger.info(f"\n💡 FINAL RECOMMENDATIONS:")

        if len(self.critical_issues) > 0:
            logger.info(f"   🔴 HIGH PRIORITY: Fix {len(self.critical_issues)} critical issues")

        if len(self.warnings) > 5:
            logger.info(f"   🟡 MEDIUM PRIORITY: Address {len(self.warnings)} warnings")

        if success_rate < 90:
            logger.info(f"   🟡 MEDIUM PRIORITY: Improve test success rate from {success_rate:.1f}%")

        if len(self.critical_issues) == 0 and len(self.warnings) <= 3 and success_rate >= 90:
            logger.info(f"   🟢 EXCELLENT: System ready for production deployment!")

        # Save detailed report
        report_data = {
            'timestamp': time.time(),
            'test_duration_seconds': test_duration,
            'success_rate_percent': success_rate,
            'tests_passed': passed_tests,
            'total_tests': total_tests,
            'critical_issues': self.critical_issues,
            'warnings': self.warnings,
            'test_results': self.test_results,
            'component_status': component_status,
            'production_readiness_percent': readiness_percent,
            'production_status': production_status,
            'recommendation': recommendation
        }

        report_file = Path(__file__).parent / "live_system_integration_report.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        logger.info(f"\n💾 Detailed report saved to: {report_file}")

        # Final status
        logger.info(f"\n" + "="*80)
        if success_rate >= 90 and len(self.critical_issues) == 0:
            logger.info("🎉 LIVE SYSTEM INTEGRATION TEST: OUTSTANDING SUCCESS!")
            logger.info("✅ Onnyx V6 trading system is ready for production deployment!")
        elif success_rate >= 80 and len(self.critical_issues) <= 1:
            logger.info("✅ LIVE SYSTEM INTEGRATION TEST: SUCCESS!")
            logger.info("⚠️ Minor issues to address but system is mostly ready!")
        else:
            logger.info("⚠️ LIVE SYSTEM INTEGRATION TEST: NEEDS ATTENTION!")
            logger.info("🔧 Address critical issues before production deployment!")

        logger.info("="*80)

        return report_data

# Main execution function
async def main():
    """Main execution function for live system integration test."""
    test_suite = LiveSystemIntegrationTest()

    try:
        report = await test_suite.run_comprehensive_test()
        return report
    except Exception as e:
        logger.error(f"❌ Live system integration test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
