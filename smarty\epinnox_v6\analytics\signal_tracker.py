"""
Comprehensive Signal Performance Tracking System
Tracks signal performance, validates against price action, calculates P&L
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class SignalStatus(Enum):
    PENDING = "pending"
    PROFITABLE = "profitable"
    STOPPED_OUT = "stopped_out"
    EXPIRED = "expired"

@dataclass
class SignalEntry:
    """Individual signal entry with tracking data."""
    signal_id: str
    symbol: str
    direction: str  # LONG/SHORT
    entry_price: float
    entry_time: float
    confidence: float

    # Performance tracking
    take_profit: float
    stop_loss: float
    status: SignalStatus = SignalStatus.PENDING
    exit_price: Optional[float] = None
    exit_time: Optional[float] = None
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    holding_time: float = 0.0

    # Real-time tracking
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    unrealized_pnl_percentage: float = 0.0
    max_favorable: float = 0.0
    max_adverse: float = 0.0

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    total_signals: int = 0
    pending_signals: int = 0
    profitable_signals: int = 0
    stopped_out_signals: int = 0
    expired_signals: int = 0

    win_rate: float = 0.0
    total_pnl: float = 0.0
    total_pnl_percentage: float = 0.0
    average_pnl: float = 0.0
    average_holding_time: float = 0.0

    best_signal_pnl: float = 0.0
    worst_signal_pnl: float = 0.0
    longest_holding_time: float = 0.0
    shortest_holding_time: float = 0.0

    current_unrealized_pnl: float = 0.0
    active_positions: int = 0

class SignalTracker:
    """
    Comprehensive signal performance tracking and analysis system.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.signals: Dict[str, SignalEntry] = {}
        self.performance_history: List[PerformanceMetrics] = []

        # Configuration
        self.default_tp_percentage = config.get('signal_tracking', {}).get('default_tp_percentage', 2.0)
        self.default_sl_percentage = config.get('signal_tracking', {}).get('default_sl_percentage', 1.0)
        self.signal_expiry_hours = config.get('signal_tracking', {}).get('signal_expiry_hours', 24)

        # Custom TP/SL settings per symbol
        self.tp_sl_settings: Dict[str, Dict[str, float]] = {}

        logger.info(f"Signal Tracker initialized (TP: {self.default_tp_percentage}%, SL: {self.default_sl_percentage}%)")

    def set_tp_sl_settings(self, symbol: str, tp_percentage: float, sl_percentage: float):
        """Set custom TP/SL settings for a symbol."""
        self.tp_sl_settings[symbol] = {
            'tp_percentage': tp_percentage,
            'sl_percentage': sl_percentage
        }
        logger.info(f"Updated TP/SL for {symbol}: TP={tp_percentage}%, SL={sl_percentage}%")

    def get_tp_sl_settings(self, symbol: str) -> Dict[str, float]:
        """Get TP/SL settings for a symbol."""
        return self.tp_sl_settings.get(symbol, {
            'tp_percentage': self.default_tp_percentage,
            'sl_percentage': self.default_sl_percentage
        })

    def add_signal(self, signal_data: Dict[str, Any]) -> str:
        """Add a new signal for tracking."""
        try:
            signal_id = f"{signal_data['symbol']}_{int(signal_data['timestamp'])}"
            symbol = signal_data['symbol']
            direction = signal_data['action']
            entry_price = signal_data.get('price', 0.0)
            entry_time = signal_data['timestamp']
            confidence = signal_data.get('confidence', 0.0)

            # Get TP/SL settings
            tp_sl = self.get_tp_sl_settings(symbol)

            # Calculate TP/SL levels
            if direction == 'LONG':
                take_profit = entry_price * (1 + tp_sl['tp_percentage'] / 100)
                stop_loss = entry_price * (1 - tp_sl['sl_percentage'] / 100)
            else:  # SHORT
                take_profit = entry_price * (1 - tp_sl['tp_percentage'] / 100)
                stop_loss = entry_price * (1 + tp_sl['sl_percentage'] / 100)

            # Create signal entry
            signal_entry = SignalEntry(
                signal_id=signal_id,
                symbol=symbol,
                direction=direction,
                entry_price=entry_price,
                entry_time=entry_time,
                confidence=confidence,
                take_profit=take_profit,
                stop_loss=stop_loss,
                current_price=entry_price
            )

            self.signals[signal_id] = signal_entry

            logger.info(f"📊 Signal tracked: {signal_id} - {direction} @ {entry_price:.2f} (TP: {take_profit:.2f}, SL: {stop_loss:.2f})")

            return signal_id

        except Exception as e:
            logger.error(f"Error adding signal for tracking: {e}")
            return ""

    def update_price(self, symbol: str, current_price: float):
        """Update current price and recalculate unrealized P&L for all active signals."""
        try:
            current_time = time.time()

            for signal_id, signal in self.signals.items():
                if signal.symbol == symbol and signal.status == SignalStatus.PENDING:
                    signal.current_price = current_price

                    # Calculate unrealized P&L
                    if signal.direction == 'LONG':
                        signal.unrealized_pnl = current_price - signal.entry_price
                        signal.unrealized_pnl_percentage = ((current_price - signal.entry_price) / signal.entry_price) * 100
                    else:  # SHORT
                        signal.unrealized_pnl = signal.entry_price - current_price
                        signal.unrealized_pnl_percentage = ((signal.entry_price - current_price) / signal.entry_price) * 100

                    # Track max favorable/adverse
                    if signal.unrealized_pnl > signal.max_favorable:
                        signal.max_favorable = signal.unrealized_pnl
                    if signal.unrealized_pnl < signal.max_adverse:
                        signal.max_adverse = signal.unrealized_pnl

                    # Check TP/SL conditions
                    self._check_tp_sl(signal, current_time)

                    # Check expiry
                    self._check_expiry(signal, current_time)

        except Exception as e:
            logger.error(f"Error updating price for signal tracking: {e}")

    def _check_tp_sl(self, signal: SignalEntry, current_time: float):
        """Check if signal hit TP or SL levels."""
        try:
            if signal.direction == 'LONG':
                # LONG: TP hit if price >= TP, SL hit if price <= SL
                if signal.current_price >= signal.take_profit:
                    self._close_signal(signal, signal.take_profit, current_time, SignalStatus.PROFITABLE)
                elif signal.current_price <= signal.stop_loss:
                    self._close_signal(signal, signal.stop_loss, current_time, SignalStatus.STOPPED_OUT)
            else:  # SHORT
                # SHORT: TP hit if price <= TP, SL hit if price >= SL
                if signal.current_price <= signal.take_profit:
                    self._close_signal(signal, signal.take_profit, current_time, SignalStatus.PROFITABLE)
                elif signal.current_price >= signal.stop_loss:
                    self._close_signal(signal, signal.stop_loss, current_time, SignalStatus.STOPPED_OUT)

        except Exception as e:
            logger.error(f"Error checking TP/SL for signal {signal.signal_id}: {e}")

    def _check_expiry(self, signal: SignalEntry, current_time: float):
        """Check if signal has expired."""
        try:
            expiry_time = signal.entry_time + (self.signal_expiry_hours * 3600)
            if current_time >= expiry_time:
                self._close_signal(signal, signal.current_price, current_time, SignalStatus.EXPIRED)

        except Exception as e:
            logger.error(f"Error checking expiry for signal {signal.signal_id}: {e}")

    def _close_signal(self, signal: SignalEntry, exit_price: float, exit_time: float, status: SignalStatus):
        """Close a signal and calculate final P&L."""
        try:
            signal.exit_price = exit_price
            signal.exit_time = exit_time
            signal.status = status
            signal.holding_time = exit_time - signal.entry_time

            # Calculate final P&L
            if signal.direction == 'LONG':
                signal.pnl = exit_price - signal.entry_price
                signal.pnl_percentage = ((exit_price - signal.entry_price) / signal.entry_price) * 100
            else:  # SHORT
                signal.pnl = signal.entry_price - exit_price
                signal.pnl_percentage = ((signal.entry_price - exit_price) / signal.entry_price) * 100

            # Reset unrealized P&L
            signal.unrealized_pnl = 0.0
            signal.unrealized_pnl_percentage = 0.0

            status_emoji = "✅" if status == SignalStatus.PROFITABLE else "❌" if status == SignalStatus.STOPPED_OUT else "⏰"
            logger.info(f"{status_emoji} Signal closed: {signal.signal_id} - {status.value.upper()} - P&L: {signal.pnl:.2f} ({signal.pnl_percentage:.2f}%)")

        except Exception as e:
            logger.error(f"Error closing signal {signal.signal_id}: {e}")

    def get_performance_metrics(self) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics."""
        try:
            metrics = PerformanceMetrics()

            if not self.signals:
                return metrics

            # Count signals by status
            for signal in self.signals.values():
                metrics.total_signals += 1

                if signal.status == SignalStatus.PENDING:
                    metrics.pending_signals += 1
                    metrics.current_unrealized_pnl += signal.unrealized_pnl
                elif signal.status == SignalStatus.PROFITABLE:
                    metrics.profitable_signals += 1
                    metrics.total_pnl += signal.pnl
                elif signal.status == SignalStatus.STOPPED_OUT:
                    metrics.stopped_out_signals += 1
                    metrics.total_pnl += signal.pnl
                elif signal.status == SignalStatus.EXPIRED:
                    metrics.expired_signals += 1
                    metrics.total_pnl += signal.pnl

            metrics.active_positions = metrics.pending_signals

            # Calculate rates and averages
            closed_signals = metrics.profitable_signals + metrics.stopped_out_signals + metrics.expired_signals
            if closed_signals > 0:
                metrics.win_rate = (metrics.profitable_signals / closed_signals) * 100
                metrics.average_pnl = metrics.total_pnl / closed_signals

                # Calculate percentage P&L
                total_pnl_percentage = sum(s.pnl_percentage for s in self.signals.values() if s.status != SignalStatus.PENDING)
                metrics.total_pnl_percentage = total_pnl_percentage

                # Calculate average holding time
                holding_times = [s.holding_time for s in self.signals.values() if s.status != SignalStatus.PENDING]
                if holding_times:
                    metrics.average_holding_time = sum(holding_times) / len(holding_times)
                    metrics.longest_holding_time = max(holding_times)
                    metrics.shortest_holding_time = min(holding_times)

                # Best/worst signals
                pnls = [s.pnl for s in self.signals.values() if s.status != SignalStatus.PENDING]
                if pnls:
                    metrics.best_signal_pnl = max(pnls)
                    metrics.worst_signal_pnl = min(pnls)

            return metrics

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return PerformanceMetrics()

    def get_signals_for_display(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get signals formatted for dashboard display."""
        try:
            signals_list = []

            # Sort by entry time (newest first)
            sorted_signals = sorted(self.signals.values(), key=lambda x: x.entry_time, reverse=True)

            for signal in sorted_signals[:limit]:
                signal_dict = asdict(signal)

                # Add formatted fields
                signal_dict['formatted_time'] = time.strftime('%H:%M:%S', time.localtime(signal.entry_time))
                signal_dict['formatted_date'] = time.strftime('%Y-%m-%d', time.localtime(signal.entry_time))

                # Status styling
                if signal.status == SignalStatus.PENDING:
                    signal_dict['status_class'] = 'signal-pending'
                    signal_dict['status_text'] = 'PENDING'
                elif signal.status == SignalStatus.PROFITABLE:
                    signal_dict['status_class'] = 'signal-profitable'
                    signal_dict['status_text'] = 'PROFIT'
                elif signal.status == SignalStatus.STOPPED_OUT:
                    signal_dict['status_class'] = 'signal-stopped'
                    signal_dict['status_text'] = 'STOPPED'
                else:
                    signal_dict['status_class'] = 'signal-expired'
                    signal_dict['status_text'] = 'EXPIRED'

                signals_list.append(signal_dict)

            return signals_list

        except Exception as e:
            logger.error(f"Error getting signals for display: {e}")
            return []

    def get_chart_data(self, symbol: str) -> Dict[str, Any]:
        """Get signal data formatted for chart display."""
        try:
            chart_signals = []

            for signal in self.signals.values():
                if signal.symbol == symbol:
                    chart_signals.append({
                        'x': signal.entry_time * 1000,  # Chart.js expects milliseconds
                        'y': signal.entry_price,
                        'direction': signal.direction,
                        'confidence': signal.confidence,
                        'status': signal.status.value,
                        'pnl': signal.pnl if signal.status != SignalStatus.PENDING else signal.unrealized_pnl,
                        'signal_id': signal.signal_id
                    })

            return {
                'signals': chart_signals,
                'symbol': symbol,
                'count': len(chart_signals)
            }

        except Exception as e:
            logger.error(f"Error getting chart data: {e}")
            return {'signals': [], 'symbol': symbol, 'count': 0}

    def get_formatted_signal_history(self, symbol: str = None, limit: int = 10) -> str:
        """Get formatted signal history for LLM context injection."""
        try:
            # Get recent signals
            if symbol:
                recent_signals = [s for s in self.signals.values() if s.symbol == symbol]
            else:
                recent_signals = list(self.signals.values())

            # Sort by timestamp (most recent first)
            recent_signals.sort(key=lambda x: x.timestamp, reverse=True)
            recent_signals = recent_signals[:limit]

            if not recent_signals:
                return "No recent signals available"

            # Format each signal
            formatted_lines = []
            for signal in recent_signals:
                timestamp_str = time.strftime('%H:%M:%S', time.localtime(signal.timestamp))
                status_str = signal.status.value.upper()

                line = f"{timestamp_str} | {signal.direction} @ ${signal.entry_price:.2f} | {status_str} | P&L: ${signal.pnl:.2f} | Confidence: {signal.confidence:.0f}%"
                formatted_lines.append(line)

            return "\n".join(formatted_lines)

        except Exception as e:
            logger.error(f"Error formatting signal history: {e}")
            return "Error retrieving signal history"

    def get_model_performance_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """Get performance breakdown by model for LLM context."""
        try:
            model_stats = {}

            for signal in self.signals.values():
                if hasattr(signal, 'model_contributions') and signal.model_contributions:
                    for model, contribution in signal.model_contributions.items():
                        if model not in model_stats:
                            model_stats[model] = {
                                'total_signals': 0,
                                'profitable_signals': 0,
                                'total_pnl': 0.0,
                                'win_rate': 0.0,
                                'avg_contribution': 0.0,
                                'contributions': []
                            }

                        stats = model_stats[model]
                        stats['total_signals'] += 1
                        stats['contributions'].append(contribution)

                        if signal.pnl > 0:
                            stats['profitable_signals'] += 1
                        stats['total_pnl'] += signal.pnl

            # Calculate derived metrics
            for model, stats in model_stats.items():
                if stats['total_signals'] > 0:
                    stats['win_rate'] = (stats['profitable_signals'] / stats['total_signals']) * 100
                    stats['avg_contribution'] = sum(stats['contributions']) / len(stats['contributions'])

                # Remove raw contributions to keep response clean
                del stats['contributions']

            return model_stats

        except Exception as e:
            logger.error(f"Error getting model performance breakdown: {e}")
            return {}

    def detect_recent_pattern(self, limit: int = 5) -> Dict[str, Any]:
        """Enhanced pattern detection with streak analysis and symbol-specific tracking."""
        try:
            recent_signals = list(self.signals.values())[-limit:]

            if len(recent_signals) < 2:
                return {'pattern': 'insufficient_data', 'description': 'Not enough signals for pattern analysis'}

            # Analyze recent outcomes
            recent_outcomes = [s.status.value for s in recent_signals]
            profitable_count = sum(1 for s in recent_signals if s.pnl > 0)

            # Enhanced pattern detection with streak analysis
            streak_info = self._analyze_streak_pattern(recent_signals)

            # Detect patterns
            if profitable_count == len(recent_signals):
                pattern = 'winning_streak'
                description = f"Perfect winning streak: {len(recent_signals)} consecutive profitable signals"
            elif profitable_count == 0:
                pattern = 'losing_streak'
                description = f"Losing streak: {len(recent_signals)} consecutive unprofitable signals"
            elif profitable_count >= len(recent_signals) * 0.8:
                pattern = 'mostly_winning'
                description = f"Strong performance: {profitable_count}/{len(recent_signals)} profitable signals"
            elif profitable_count <= len(recent_signals) * 0.2:
                pattern = 'mostly_losing'
                description = f"Poor performance: {profitable_count}/{len(recent_signals)} profitable signals"
            else:
                pattern = 'mixed'
                description = f"Mixed results: {profitable_count}/{len(recent_signals)} profitable signals"

            # Direction bias analysis
            directions = [s.direction for s in recent_signals]
            long_count = directions.count('LONG')
            short_count = directions.count('SHORT')

            if long_count > short_count * 2:
                direction_bias = 'bullish_bias'
            elif short_count > long_count * 2:
                direction_bias = 'bearish_bias'
            else:
                direction_bias = 'balanced'

            # Generate visual pattern representation
            pattern_visual = self._generate_pattern_visual(recent_signals)

            return {
                'pattern': pattern,
                'description': description,
                'direction_bias': direction_bias,
                'recent_win_rate': (profitable_count / len(recent_signals)) * 100,
                'signal_count': len(recent_signals),
                'current_streak': streak_info['current_streak'],
                'streak_type': streak_info['streak_type'],
                'streak_length': streak_info['streak_length'],
                'pattern_visual': pattern_visual,
                'confidence_impact': self._calculate_confidence_impact(pattern, profitable_count, len(recent_signals))
            }

        except Exception as e:
            logger.error(f"Error detecting recent pattern: {e}")
            return {'pattern': 'error', 'description': 'Error analyzing recent patterns'}

    def _analyze_streak_pattern(self, signals: list) -> Dict[str, Any]:
        """Analyze current streak pattern in signals."""
        try:
            if not signals:
                return {'current_streak': 'none', 'streak_type': 'none', 'streak_length': 0}

            # Analyze from most recent backwards
            current_outcome = 'win' if signals[-1].pnl > 0 else 'loss'
            streak_length = 1

            # Count consecutive outcomes of same type
            for i in range(len(signals) - 2, -1, -1):
                signal_outcome = 'win' if signals[i].pnl > 0 else 'loss'
                if signal_outcome == current_outcome:
                    streak_length += 1
                else:
                    break

            # Determine streak description
            if streak_length >= 3:
                if current_outcome == 'win':
                    current_streak = f"{streak_length} consecutive wins"
                    streak_type = 'winning_streak'
                else:
                    current_streak = f"{streak_length} consecutive losses"
                    streak_type = 'losing_streak'
            else:
                current_streak = f"{streak_length} {current_outcome}{'s' if streak_length > 1 else ''}"
                streak_type = 'short_streak'

            return {
                'current_streak': current_streak,
                'streak_type': streak_type,
                'streak_length': streak_length
            }

        except Exception as e:
            logger.warning(f"Error analyzing streak pattern: {e}")
            return {'current_streak': 'unknown', 'streak_type': 'unknown', 'streak_length': 0}

    def _generate_pattern_visual(self, signals: list) -> str:
        """Generate visual representation of recent signal outcomes."""
        try:
            visual_symbols = []
            for signal in signals:
                if signal.pnl > 0:
                    visual_symbols.append(f"✓{signal.direction}")
                else:
                    visual_symbols.append(f"✗{signal.direction}")

            return "[" + ", ".join(visual_symbols) + "]"

        except Exception as e:
            logger.warning(f"Error generating pattern visual: {e}")
            return "[Pattern visualization error]"

    def _calculate_confidence_impact(self, pattern: str, profitable_count: int, total_count: int) -> str:
        """Calculate how pattern should impact confidence."""
        try:
            win_rate = profitable_count / total_count if total_count > 0 else 0

            if pattern == 'winning_streak':
                return "Increase confidence by 10-15% due to winning streak"
            elif pattern == 'losing_streak':
                return "Decrease confidence by 15-20% due to losing streak"
            elif win_rate >= 0.8:
                return "Increase confidence by 5-10% due to strong recent performance"
            elif win_rate <= 0.2:
                return "Decrease confidence by 10-15% due to poor recent performance"
            else:
                return "Neutral confidence impact from mixed performance"

        except Exception as e:
            logger.warning(f"Error calculating confidence impact: {e}")
            return "Unable to assess confidence impact"

    def get_symbol_specific_performance(self, symbol: str, limit: int = 10) -> Dict[str, Any]:
        """Get symbol-specific performance analysis."""
        try:
            symbol_signals = [s for s in self.signals.values() if s.symbol == symbol][-limit:]

            if not symbol_signals:
                return {
                    'symbol': symbol,
                    'signal_count': 0,
                    'win_rate': 0,
                    'total_pnl': 0,
                    'recent_pattern': 'No signals',
                    'recommendation': 'No historical data available'
                }

            profitable_count = sum(1 for s in symbol_signals if s.pnl > 0)
            total_pnl = sum(s.pnl for s in symbol_signals)
            win_rate = (profitable_count / len(symbol_signals)) * 100

            # Generate recommendation based on performance
            if len(symbol_signals) >= 3:
                if win_rate >= 70:
                    recommendation = "Strong performance - maintain standard position sizing"
                elif win_rate <= 30:
                    recommendation = "Poor performance - consider reduced position sizing"
                else:
                    recommendation = "Mixed performance - standard risk management"
            else:
                recommendation = "Limited data - use standard risk parameters"

            return {
                'symbol': symbol,
                'signal_count': len(symbol_signals),
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'recent_pattern': self._generate_pattern_visual(symbol_signals[-5:]),
                'recommendation': recommendation
            }

        except Exception as e:
            logger.error(f"Error getting symbol-specific performance: {e}")
            return {
                'symbol': symbol,
                'signal_count': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'recent_pattern': 'Error',
                'recommendation': 'Unable to assess performance'
            }
