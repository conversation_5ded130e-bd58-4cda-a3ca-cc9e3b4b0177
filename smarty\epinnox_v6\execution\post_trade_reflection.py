#!/usr/bin/env python3
"""
Post-Trade LLM Reflection
LLM receives: "Your last decision was LONG, and it hit stop-loss." → Adapts rationale or triggers reasoning penalty
"""

import logging
import time
import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TradeOutcome(Enum):
    WIN = "win"
    LOSS = "loss"
    BREAKEVEN = "breakeven"
    STOPPED_OUT = "stopped_out"
    PARTIAL_FILL = "partial_fill"
    TIMEOUT = "timeout"

@dataclass
class TradeReflection:
    """Reflection on a completed trade."""
    trade_id: str
    original_decision: Dict[str, Any]
    actual_outcome: TradeOutcome
    pnl: float
    pnl_percentage: float
    hold_duration: float
    exit_reason: str
    market_conditions_change: str
    llm_reflection: str
    lessons_learned: List[str]
    confidence_adjustment: float
    reasoning_quality_score: float
    timestamp: float

class PostTradeReflection:
    """
    Analyzes completed trades and provides LLM-based reflection
    to improve future decision-making quality.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.reflection_config = config.get('post_trade_reflection', {})

        # LLM configuration
        self.llm_config = config.get('llm', {})
        self.api_url = self.llm_config.get('api_url', 'http://localhost:1234/v1')
        self.model_name = self.llm_config.get('model_name', 'phi-3.1-mini')

        # Reflection settings
        self.reflection_threshold = self.reflection_config.get('reflection_threshold', 0.1)  # Reflect on trades with >10% impact
        self.batch_reflection_size = self.reflection_config.get('batch_reflection_size', 5)
        self.reflection_frequency = self.reflection_config.get('reflection_frequency', 3600)  # 1 hour

        # Storage
        self.reflections: List[TradeReflection] = []
        self.pending_trades: Dict[str, Dict[str, Any]] = {}

        # Performance tracking
        self.reflection_insights = {
            'common_mistakes': [],
            'successful_patterns': [],
            'confidence_calibration': {},
            'market_regime_lessons': {}
        }

        logger.info("Post-Trade Reflection system initialized")

    async def analyze_completed_trade(self, trade_data: Dict[str, Any]) -> Optional[TradeReflection]:
        """
        Analyze a completed trade and generate LLM reflection.

        Args:
            trade_data: Complete trade information including entry, exit, and outcome

        Returns:
            TradeReflection object with insights and lessons
        """
        try:
            trade_id = trade_data.get('trade_id', '')
            original_decision = trade_data.get('original_decision', {})
            pnl = trade_data.get('pnl', 0.0)
            entry_price = trade_data.get('entry_price', 0.0)
            exit_price = trade_data.get('exit_price', 0.0)

            # Calculate trade metrics
            pnl_percentage = (pnl / (entry_price * trade_data.get('size', 1))) if entry_price > 0 else 0
            hold_duration = trade_data.get('hold_duration', 0.0)

            # Determine outcome
            outcome = self._determine_trade_outcome(trade_data)

            # Check if reflection is warranted
            if not self._should_reflect(pnl_percentage, outcome, original_decision):
                logger.debug(f"Skipping reflection for trade {trade_id} - below threshold")
                return None

            # Generate LLM reflection
            llm_reflection = await self._generate_llm_reflection(trade_data, outcome)

            # Extract lessons and adjustments
            lessons_learned = self._extract_lessons(llm_reflection, outcome, trade_data)
            confidence_adjustment = self._calculate_confidence_adjustment(outcome, original_decision)
            reasoning_quality_score = self._assess_reasoning_quality(original_decision, outcome)

            # Create reflection object
            reflection = TradeReflection(
                trade_id=trade_id,
                original_decision=original_decision,
                actual_outcome=outcome,
                pnl=pnl,
                pnl_percentage=pnl_percentage,
                hold_duration=hold_duration,
                exit_reason=trade_data.get('exit_reason', 'unknown'),
                market_conditions_change=trade_data.get('market_change', 'stable'),
                llm_reflection=llm_reflection,
                lessons_learned=lessons_learned,
                confidence_adjustment=confidence_adjustment,
                reasoning_quality_score=reasoning_quality_score,
                timestamp=time.time()
            )

            # Store reflection
            self.reflections.append(reflection)

            # Update insights
            self._update_reflection_insights(reflection)

            logger.info(f"Generated reflection for trade {trade_id}: {outcome.value} "
                       f"({pnl_percentage*100:.2f}% PnL)")

            return reflection

        except Exception as e:
            logger.error(f"Error analyzing completed trade: {e}")
            return None

    async def _generate_llm_reflection(self, trade_data: Dict[str, Any],
                                     outcome: TradeOutcome) -> str:
        """Generate LLM-based reflection on the trade."""
        try:
            # Prepare reflection prompt
            prompt = self._build_reflection_prompt(trade_data, outcome)

            # Call LLM
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert trading analyst providing post-trade reflection and learning insights."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 500,
                    "temperature": 0.7
                }

                async with session.post(f"{self.api_url}/chat/completions",
                                      json=payload, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        reflection = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                        return reflection.strip()
                    else:
                        logger.warning(f"LLM reflection failed: {response.status}")
                        return self._generate_fallback_reflection(trade_data, outcome)

        except Exception as e:
            logger.error(f"Error generating LLM reflection: {e}")
            return self._generate_fallback_reflection(trade_data, outcome)

    def _build_reflection_prompt(self, trade_data: Dict[str, Any], outcome: TradeOutcome) -> str:
        """Build prompt for LLM reflection."""
        try:
            original_decision = trade_data.get('original_decision', {})
            action = original_decision.get('action', 'UNKNOWN')
            confidence = original_decision.get('confidence', 0.0)
            reasoning = original_decision.get('reasoning', 'No reasoning provided')

            pnl = trade_data.get('pnl', 0.0)
            pnl_percentage = (pnl / (trade_data.get('entry_price', 1) * trade_data.get('size', 1))) * 100

            prompt = f"""
TRADE REFLECTION ANALYSIS

Original Decision:
- Action: {action}
- Confidence: {confidence*100:.0f}%
- Reasoning: {reasoning}

Trade Outcome:
- Result: {outcome.value.upper()}
- PnL: ${pnl:.2f} ({pnl_percentage:+.2f}%)
- Hold Duration: {trade_data.get('hold_duration', 0):.1f} hours
- Exit Reason: {trade_data.get('exit_reason', 'unknown')}

Market Context:
- Symbol: {trade_data.get('symbol', 'UNKNOWN')}
- Entry Price: ${trade_data.get('entry_price', 0):.2f}
- Exit Price: ${trade_data.get('exit_price', 0):.2f}
- Market Regime: {original_decision.get('market_regime', 'unknown')}

Please provide a concise reflection addressing:
1. Was the original reasoning sound given the outcome?
2. What market factors were overlooked or misassessed?
3. How should confidence be adjusted for similar future scenarios?
4. What specific lesson can be learned from this trade?

Keep response under 300 words and focus on actionable insights.
"""

            return prompt.strip()

        except Exception as e:
            logger.error(f"Error building reflection prompt: {e}")
            return "Analyze this trade outcome and provide insights for improvement."

    def _determine_trade_outcome(self, trade_data: Dict[str, Any]) -> TradeOutcome:
        """Determine the outcome category of the trade."""
        try:
            pnl = trade_data.get('pnl', 0.0)
            exit_reason = trade_data.get('exit_reason', '').lower()

            if 'stop' in exit_reason or 'stopped' in exit_reason:
                return TradeOutcome.STOPPED_OUT
            elif 'partial' in exit_reason:
                return TradeOutcome.PARTIAL_FILL
            elif 'timeout' in exit_reason:
                return TradeOutcome.TIMEOUT
            elif pnl > 0.01:  # Meaningful profit
                return TradeOutcome.WIN
            elif pnl < -0.01:  # Meaningful loss
                return TradeOutcome.LOSS
            else:
                return TradeOutcome.BREAKEVEN

        except Exception as e:
            logger.error(f"Error determining trade outcome: {e}")
            return TradeOutcome.BREAKEVEN

    def _should_reflect(self, pnl_percentage: float, outcome: TradeOutcome,
                       original_decision: Dict[str, Any]) -> bool:
        """Determine if trade warrants reflection."""
        try:
            # Always reflect on significant losses
            if pnl_percentage < -self.reflection_threshold:
                return True

            # Reflect on stopped out trades
            if outcome == TradeOutcome.STOPPED_OUT:
                return True

            # Reflect on high confidence trades that failed
            confidence = original_decision.get('confidence', 0.0)
            if confidence > 0.8 and outcome == TradeOutcome.LOSS:
                return True

            # Reflect on unexpected wins with low confidence
            if confidence < 0.5 and outcome == TradeOutcome.WIN and pnl_percentage > 0.05:
                return True

            return False

        except Exception as e:
            logger.error(f"Error determining reflection need: {e}")
            return False

    def _extract_lessons(self, llm_reflection: str, outcome: TradeOutcome,
                        trade_data: Dict[str, Any]) -> List[str]:
        """Extract actionable lessons from LLM reflection."""
        try:
            lessons = []

            # Parse LLM reflection for lessons
            if "lesson" in llm_reflection.lower():
                # Extract sentences containing "lesson"
                sentences = llm_reflection.split('.')
                for sentence in sentences:
                    if "lesson" in sentence.lower():
                        lessons.append(sentence.strip())

            # Add outcome-specific lessons
            if outcome == TradeOutcome.STOPPED_OUT:
                lessons.append("Consider wider stop-loss or better entry timing")
            elif outcome == TradeOutcome.LOSS:
                confidence = trade_data.get('original_decision', {}).get('confidence', 0.0)
                if confidence > 0.8:
                    lessons.append("High confidence trades require extra validation")

            return lessons[:3]  # Limit to 3 key lessons

        except Exception as e:
            logger.error(f"Error extracting lessons: {e}")
            return ["Review trade setup and market conditions"]

    def _calculate_confidence_adjustment(self, outcome: TradeOutcome,
                                       original_decision: Dict[str, Any]) -> float:
        """Calculate confidence adjustment for future similar trades."""
        try:
            original_confidence = original_decision.get('confidence', 0.5)

            # Adjustment based on outcome
            if outcome == TradeOutcome.WIN:
                # Slight confidence boost for wins
                return min(0.05, (1.0 - original_confidence) * 0.1)
            elif outcome == TradeOutcome.LOSS:
                # Confidence penalty for losses
                return -min(0.1, original_confidence * 0.2)
            elif outcome == TradeOutcome.STOPPED_OUT:
                # Larger penalty for stop-outs
                return -min(0.15, original_confidence * 0.3)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Error calculating confidence adjustment: {e}")
            return 0.0

    def _assess_reasoning_quality(self, original_decision: Dict[str, Any],
                                outcome: TradeOutcome) -> float:
        """Assess the quality of original reasoning (0-1 score)."""
        try:
            reasoning = original_decision.get('reasoning', '')
            confidence = original_decision.get('confidence', 0.0)

            base_score = 0.5

            # Quality indicators in reasoning
            quality_indicators = [
                'model', 'signal', 'analysis', 'market', 'trend', 'support', 'resistance',
                'volume', 'volatility', 'momentum', 'risk', 'context'
            ]

            indicator_count = sum(1 for indicator in quality_indicators
                                if indicator.lower() in reasoning.lower())

            # Reasoning length and detail
            length_score = min(len(reasoning) / 200, 1.0)  # Normalize to 200 chars
            detail_score = min(indicator_count / 5, 1.0)   # Normalize to 5 indicators

            # Outcome alignment
            outcome_score = 0.5
            if outcome == TradeOutcome.WIN and confidence > 0.7:
                outcome_score = 0.8  # Good reasoning led to good outcome
            elif outcome == TradeOutcome.LOSS and confidence < 0.5:
                outcome_score = 0.6  # Cautious reasoning was appropriate
            elif outcome == TradeOutcome.LOSS and confidence > 0.8:
                outcome_score = 0.2  # Overconfident reasoning

            # Combined score
            quality_score = (base_score * 0.3) + (length_score * 0.2) + \
                          (detail_score * 0.3) + (outcome_score * 0.2)

            return max(0.0, min(1.0, quality_score))

        except Exception as e:
            logger.error(f"Error assessing reasoning quality: {e}")
            return 0.5

    def _update_reflection_insights(self, reflection: TradeReflection):
        """Update accumulated insights from reflections."""
        try:
            # Update common mistakes
            if reflection.actual_outcome in [TradeOutcome.LOSS, TradeOutcome.STOPPED_OUT]:
                for lesson in reflection.lessons_learned:
                    if lesson not in self.reflection_insights['common_mistakes']:
                        self.reflection_insights['common_mistakes'].append(lesson)

            # Update successful patterns
            if reflection.actual_outcome == TradeOutcome.WIN:
                reasoning = reflection.original_decision.get('reasoning', '')
                if reasoning and len(reasoning) > 50:
                    pattern = f"High-quality reasoning: {reasoning[:100]}..."
                    if pattern not in self.reflection_insights['successful_patterns']:
                        self.reflection_insights['successful_patterns'].append(pattern)

            # Update confidence calibration
            confidence_bucket = round(reflection.original_decision.get('confidence', 0.0), 1)
            if confidence_bucket not in self.reflection_insights['confidence_calibration']:
                self.reflection_insights['confidence_calibration'][confidence_bucket] = {
                    'wins': 0, 'losses': 0, 'total': 0
                }

            calibration = self.reflection_insights['confidence_calibration'][confidence_bucket]
            calibration['total'] += 1
            if reflection.actual_outcome == TradeOutcome.WIN:
                calibration['wins'] += 1
            else:
                calibration['losses'] += 1

            # Update market regime lessons
            regime = reflection.original_decision.get('market_regime', 'unknown')
            if regime not in self.reflection_insights['market_regime_lessons']:
                self.reflection_insights['market_regime_lessons'][regime] = []

            regime_lessons = self.reflection_insights['market_regime_lessons'][regime]
            for lesson in reflection.lessons_learned:
                if lesson not in regime_lessons:
                    regime_lessons.append(lesson)

        except Exception as e:
            logger.error(f"Error updating reflection insights: {e}")

    def _generate_fallback_reflection(self, trade_data: Dict[str, Any],
                                    outcome: TradeOutcome) -> str:
        """Generate fallback reflection when LLM is unavailable."""
        try:
            pnl = trade_data.get('pnl', 0.0)
            confidence = trade_data.get('original_decision', {}).get('confidence', 0.0)

            if outcome == TradeOutcome.WIN:
                return f"Trade was successful with ${pnl:.2f} profit. " \
                       f"Original confidence of {confidence*100:.0f}% was appropriate. " \
                       f"Continue monitoring similar setups."
            elif outcome == TradeOutcome.LOSS:
                return f"Trade resulted in ${pnl:.2f} loss. " \
                       f"Review entry criteria and risk management. " \
                       f"Consider reducing position size for similar confidence levels."
            elif outcome == TradeOutcome.STOPPED_OUT:
                return f"Trade was stopped out with ${pnl:.2f} loss. " \
                       f"Evaluate stop-loss placement and market volatility. " \
                       f"Consider wider stops or better entry timing."
            else:
                return f"Trade outcome: {outcome.value}. Review setup and execution."

        except Exception as e:
            logger.error(f"Error generating fallback reflection: {e}")
            return "Trade completed. Review performance and adjust strategy as needed."

    def get_reflection_summary(self, lookback_days: int = 7) -> Dict[str, Any]:
        """Get summary of reflections and insights."""
        try:
            cutoff_time = time.time() - (lookback_days * 24 * 3600)
            recent_reflections = [r for r in self.reflections if r.timestamp >= cutoff_time]

            if not recent_reflections:
                return {'message': 'No recent reflections available'}

            # Outcome distribution
            outcomes = [r.actual_outcome.value for r in recent_reflections]
            outcome_counts = {outcome: outcomes.count(outcome) for outcome in set(outcomes)}

            # Average adjustments
            avg_confidence_adjustment = sum(r.confidence_adjustment for r in recent_reflections) / len(recent_reflections)
            avg_reasoning_quality = sum(r.reasoning_quality_score for r in recent_reflections) / len(recent_reflections)

            # Top lessons
            all_lessons = []
            for r in recent_reflections:
                all_lessons.extend(r.lessons_learned)

            lesson_counts = {}
            for lesson in all_lessons:
                lesson_counts[lesson] = lesson_counts.get(lesson, 0) + 1

            top_lessons = sorted(lesson_counts.items(), key=lambda x: x[1], reverse=True)[:5]

            return {
                'lookback_days': lookback_days,
                'total_reflections': len(recent_reflections),
                'outcome_distribution': outcome_counts,
                'avg_confidence_adjustment': avg_confidence_adjustment,
                'avg_reasoning_quality': avg_reasoning_quality,
                'top_lessons': [lesson for lesson, count in top_lessons],
                'insights': self.reflection_insights
            }

        except Exception as e:
            logger.error(f"Error getting reflection summary: {e}")
            return {'error': str(e)}

    async def batch_reflect_on_trades(self, trade_batch: List[Dict[str, Any]]) -> List[TradeReflection]:
        """Perform batch reflection on multiple trades."""
        try:
            reflections = []

            for trade_data in trade_batch:
                reflection = await self.analyze_completed_trade(trade_data)
                if reflection:
                    reflections.append(reflection)

                # Small delay to avoid overwhelming LLM
                await asyncio.sleep(0.5)

            logger.info(f"Completed batch reflection on {len(reflections)} trades")
            return reflections

        except Exception as e:
            logger.error(f"Error in batch reflection: {e}")
            return []
