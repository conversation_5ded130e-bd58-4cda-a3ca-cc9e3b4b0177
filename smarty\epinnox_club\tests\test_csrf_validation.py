#!/usr/bin/env python3
"""
CSRF Validation Test
Test the fixed CSRF token validation logic
"""

import requests
import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_csrf_validation_fix():
    """Test that CSRF validation now works correctly."""
    logger.info("🔐 TESTING CSRF VALIDATION FIX")
    logger.info("=" * 40)
    
    base_url = "http://localhost:8086"
    
    try:
        # Step 1: Get login page and extract CSRF token
        logger.info("📋 Step 1: Getting CSRF token from login page...")
        response = requests.get(f"{base_url}/login", timeout=10)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to get login page: {response.status_code}")
            return False
        
        # Extract CSRF token
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if not csrf_match:
            logger.error("❌ CSRF token not found in login page")
            return False
        
        csrf_token = csrf_match.group(1)
        logger.info(f"✅ CSRF token extracted: {csrf_token}")
        logger.info(f"   Token length: {len(csrf_token)} characters")
        logger.info(f"   Token format: {'Valid hex' if all(c in '0123456789abcdef' for c in csrf_token.lower()) else 'Invalid'}")
        
        # Step 2: Test login with valid CSRF token
        logger.info("\n📋 Step 2: Testing login with valid CSRF token...")
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123',
            'csrf_token': csrf_token
        }
        
        login_response = requests.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
        logger.info(f"Login response status: {login_response.status_code}")
        
        if login_response.status_code == 302:
            location = login_response.headers.get('Location', '')
            logger.info(f"Redirected to: {location}")
            
            if '/dashboard' in location:
                logger.info("✅ Login successful with valid CSRF token!")
                login_success = True
            else:
                logger.warning("⚠️ Login redirected but not to dashboard")
                login_success = False
        elif login_response.status_code == 403:
            logger.error("❌ Login blocked by CSRF validation (token should be valid!)")
            logger.error(f"Response: {login_response.text}")
            login_success = False
        else:
            logger.error(f"❌ Unexpected login response: {login_response.status_code}")
            logger.error(f"Response: {login_response.text}")
            login_success = False
        
        # Step 3: Test login with invalid CSRF token
        logger.info("\n📋 Step 3: Testing login with invalid CSRF token...")
        invalid_login_data = {
            'username': 'epinnox',
            'password': 'securepass123',
            'csrf_token': 'invalid_token_123'
        }
        
        invalid_response = requests.post(f"{base_url}/login", data=invalid_login_data, timeout=10)
        logger.info(f"Invalid token response status: {invalid_response.status_code}")
        
        if invalid_response.status_code == 403:
            logger.info("✅ Invalid CSRF token correctly blocked")
            csrf_protection_working = True
        else:
            logger.warning(f"⚠️ Invalid token not blocked: {invalid_response.status_code}")
            csrf_protection_working = False
        
        # Step 4: Test login without CSRF token
        logger.info("\n📋 Step 4: Testing login without CSRF token...")
        no_token_data = {
            'username': 'epinnox',
            'password': 'securepass123'
            # No CSRF token
        }
        
        no_token_response = requests.post(f"{base_url}/login", data=no_token_data, timeout=10)
        logger.info(f"No token response status: {no_token_response.status_code}")
        
        if no_token_response.status_code == 403:
            logger.info("✅ Missing CSRF token correctly blocked")
            no_token_protection = True
        else:
            logger.warning(f"⚠️ Missing token not blocked: {no_token_response.status_code}")
            no_token_protection = False
        
        # Summary
        logger.info("\n" + "=" * 40)
        logger.info("📊 TEST RESULTS:")
        logger.info(f"✅ CSRF token extraction: SUCCESS")
        logger.info(f"✅ Login with valid token: {'SUCCESS' if login_success else 'FAILED'}")
        logger.info(f"✅ Block invalid token: {'SUCCESS' if csrf_protection_working else 'FAILED'}")
        logger.info(f"✅ Block missing token: {'SUCCESS' if no_token_protection else 'FAILED'}")
        
        overall_success = login_success and csrf_protection_working and no_token_protection
        
        if overall_success:
            logger.info("\n🎉 CSRF VALIDATION FIX SUCCESSFUL!")
            logger.info("✅ Valid tokens are accepted")
            logger.info("✅ Invalid tokens are blocked")
            logger.info("✅ Missing tokens are blocked")
            logger.info("✅ Login system working correctly")
        else:
            logger.error("\n❌ CSRF VALIDATION STILL HAS ISSUES")
            if not login_success:
                logger.error("🔧 Valid tokens are being rejected")
            if not csrf_protection_working:
                logger.error("🔧 Invalid tokens are not being blocked")
            if not no_token_protection:
                logger.error("🔧 Missing tokens are not being blocked")
        
        return overall_success
        
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Network error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function."""
    print("🔐 CSRF Validation Fix Test")
    print("Testing the corrected CSRF token validation")
    print()
    
    try:
        success = test_csrf_validation_fix()
        
        if success:
            print("\n🎊 CSRF VALIDATION IS NOW WORKING!")
            print("🌐 You can now login at: http://localhost:8086")
            print("🔐 Use credentials: epinnox / securepass123")
            print("🛡️ CSRF protection is active and working correctly")
            return 0
        else:
            print("\n❌ CSRF validation needs more work")
            print("🔧 Check the server logs for more details")
            return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
