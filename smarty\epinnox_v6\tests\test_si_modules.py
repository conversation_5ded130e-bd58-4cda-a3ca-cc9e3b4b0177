#!/usr/bin/env python3
"""
Simple Strategic Intelligence Modules Test
Tests the new strategic intelligence modules directly
"""

import asyncio
import logging
import time
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_modules():
    """Test strategic intelligence modules."""
    try:
        logger.info("🧠 TESTING STRATEGIC INTELLIGENCE MODULES")
        logger.info("=" * 50)
        
        # Test 1: Multi-Timeframe Analyzer
        logger.info("\n🧱 TEST 1: Multi-Timeframe Analyzer")
        logger.info("-" * 30)
        
        from autonomy.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        
        config = {
            'multi_timeframe': {
                'enabled': True,
                'timeframes': ['1m', '5m', '15m', '1h', '4h']
            }
        }
        
        mta = MultiTimeframeAnalyzer(config)
        logger.info("✅ MultiTimeframeAnalyzer created successfully")
        
        # Get performance stats
        stats = mta.get_performance_stats()
        logger.info(f"   Performance stats: {stats}")
        
        # Test 2: Strategy Evaluator
        logger.info("\n🧠 TEST 2: Strategy Evaluator")
        logger.info("-" * 30)
        
        from autonomy.strategy_evaluator import StrategyEvaluator
        
        evaluator_config = {
            'strategy_evaluator': {
                'enabled': True,
                'min_confidence': 0.6,
                'min_alignment_score': 0.4,
                'max_risk_tolerance': 0.8
            }
        }
        
        evaluator = StrategyEvaluator(evaluator_config)
        logger.info("✅ StrategyEvaluator created successfully")
        
        # Get evaluation stats
        eval_stats = evaluator.get_evaluation_stats()
        logger.info(f"   Evaluation stats: {eval_stats}")
        
        # Test 3: Strategy Tracker
        logger.info("\n📊 TEST 3: Strategy Tracker")
        logger.info("-" * 30)
        
        from autonomy.strategy_tracker import StrategyTracker, TradeResult
        
        tracker_config = {
            'strategy_tracker': {
                'enabled': True,
                'max_trade_history': 1000,
                'performance_window': 100,
                'save_interval': 300
            }
        }
        
        tracker = StrategyTracker(tracker_config)
        logger.info("✅ StrategyTracker created successfully")
        
        # Create and record a mock trade
        mock_trade = TradeResult(
            trade_id="test_001",
            symbol="DOGE/USDT:USDT",
            action="LONG",
            entry_price=0.1850,
            exit_price=0.1875,
            size=100.0,
            pnl=2.50,
            duration_minutes=45.0,
            model_contributions={'rsi': 0.3, 'vwap': 0.4, 'orderflow': 0.3},
            mta_alignment_score=0.75,
            signal_confidence=0.80,
            execution_quality=0.85,
            timestamp=time.time()
        )
        
        tracker.record_trade_result(mock_trade)
        logger.info("✅ Mock trade recorded successfully")
        
        # Get tracker stats
        tracker_stats = tracker.get_tracker_stats()
        logger.info(f"   Tracker stats: Total trades = {tracker_stats['basic_stats']['total_trades_tracked']}")
        
        # Test 4: Strategy Critic
        logger.info("\n🤖 TEST 4: Strategy Critic")
        logger.info("-" * 30)
        
        from autonomy.strategy_critic import StrategyCritic
        
        critic_config = {
            'strategy_critic': {
                'enabled': True,
                'review_frequency': 3600,
                'min_trades_for_review': 10,
                'max_trades_per_review': 20,
                'llm_model': 'phi-3.1-mini'
            }
        }
        
        critic = StrategyCritic(critic_config)
        logger.info("✅ StrategyCritic created successfully")
        
        # Get critique summary
        critique_summary = critic.get_critique_summary()
        logger.info(f"   Critique summary: {critique_summary}")
        
        # Test 5: Integration Test
        logger.info("\n🔗 TEST 5: Integration Test")
        logger.info("-" * 30)
        
        # Test strategy evaluation with mock data
        mock_signal = {
            'symbol': 'DOGE/USDT:USDT',
            'action': 'LONG',
            'confidence': 0.75,
            'model_contributions': {
                'rsi': 0.3,
                'vwap': 0.4,
                'orderflow': 0.2,
                'volatility': 0.1
            }
        }
        
        mock_account_data = {
            'margin_used_pct': 45.0,
            'available_balance': 100.0
        }
        
        mock_model_outputs = {
            'rsi': type('MockOutput', (), {'model_name': 'rsi', 'confidence': 0.7})(),
            'vwap': type('MockOutput', (), {'model_name': 'vwap', 'confidence': 0.8})(),
        }
        
        # Evaluate signal (without MTA for now)
        evaluation = await evaluator.evaluate_signal(
            mock_signal, None, mock_account_data, mock_model_outputs
        )
        
        if evaluation:
            logger.info("✅ Signal evaluation successful")
            logger.info(f"   Decision: {evaluation.decision.value}")
            logger.info(f"   Confidence Adjustment: {evaluation.confidence_adjustment:.2f}")
            logger.info(f"   Reasoning: {evaluation.reasoning}")
        else:
            logger.error("❌ Signal evaluation failed")
        
        # Test 6: Model Weight Updates
        logger.info("\n🔧 TEST 6: Model Performance Updates")
        logger.info("-" * 30)
        
        # Update model performance in evaluator
        evaluator.update_model_performance('rsi', 0.65, 0.015, 0.8)
        evaluator.update_model_performance('vwap', 0.72, 0.020, 0.85)
        
        logger.info("✅ Model performance updated successfully")
        
        # Final Summary
        logger.info("\n🎉 STRATEGIC INTELLIGENCE MODULES TEST SUMMARY")
        logger.info("=" * 50)
        logger.info("✅ MultiTimeframeAnalyzer: WORKING")
        logger.info("✅ StrategyEvaluator: WORKING") 
        logger.info("✅ StrategyTracker: WORKING")
        logger.info("✅ StrategyCritic: WORKING")
        logger.info("✅ Integration: WORKING")
        logger.info("✅ Model Updates: WORKING")
        
        logger.info("\n🧠 ALL STRATEGIC INTELLIGENCE MODULES: OPERATIONAL")
        
        # Cleanup
        tracker.shutdown()
        critic.shutdown()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Module test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Strategic Intelligence Modules Test...")
    
    success = await test_modules()
    
    if success:
        logger.info("\n🎉 ALL MODULE TESTS PASSED!")
        logger.info("🧠 Strategic Intelligence modules are ready for integration!")
        logger.info("\n📋 NEXT STEPS:")
        logger.info("1. 🔌 Inject HTX exchange client for live MTA data")
        logger.info("2. 🧵 Start background tasks in main system")
        logger.info("3. 📊 Monitor performance in dashboard")
        logger.info("4. 🤖 Review LLM feedback in logs/")
    else:
        logger.error("\n❌ MODULE TESTS FAILED - CHECK IMPLEMENTATION")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
