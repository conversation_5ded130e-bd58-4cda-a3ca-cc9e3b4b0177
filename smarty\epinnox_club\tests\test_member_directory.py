#!/usr/bin/env python3
"""
Test the member directory functionality with our demo data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import DatabaseManager
from dashboards.enhanced_member_directory import EnhancedMemberDirectory
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_member_directory():
    """Test the member directory with demo data."""
    print("🔍 TESTING MEMBER DIRECTORY")
    print("=" * 50)

    try:
        # Initialize database and member directory
        db = DatabaseManager('data/money_circle.db')
        member_dir = EnhancedMemberDirectory(db)

        # Test getting all members
        print("Step 1: Testing member data retrieval...")
        members = member_dir._get_all_members()

        print(f"✅ Found {len(members)} members")

        for member in members:
            print(f"   {member['username']}: {member['display_name']}")
            print(f"      Trading Style: {member['trading_style']}")
            print(f"      Risk Tolerance: {member['risk_tolerance']}")
            print(f"      Reputation: {member['reputation_score']:.1f}★")
            print(f"      Trades: {member['trades_count']}, Positions: {member['positions_count']}")
            print(f"      P&L: ${member['total_pnl']:+.2f}")
            print()

        # Test leaderboards
        print("Step 2: Testing leaderboards...")
        leaderboards = member_dir._get_leaderboards()

        for board_name, board_data in leaderboards.items():
            print(f"   {board_name.title()} Leaderboard: {len(board_data)} entries")

        # Test member stats
        print("Step 3: Testing member statistics...")
        stats = member_dir._get_member_stats()

        print(f"   Total Members: {stats['total_members']}")
        print(f"   Active Traders: {stats['active_traders']}")
        print(f"   Average Performance: {stats['avg_performance']:.2f}")
        print(f"   Total Trades: {stats['total_trades']}")

        # Test achievements
        print("Step 4: Testing achievements...")
        achievements = member_dir._get_member_achievements()

        print(f"   Members with achievements: {len(achievements)}")

        # Test HTML generation
        print("Step 5: Testing HTML generation...")

        # Mock request for testing
        class MockRequest:
            def __init__(self):
                self.query = {}
                self.app = {'user_sessions': {}}

        # Mock user session
        mock_request = MockRequest()

        # Add a mock user to the request
        mock_request.app['user_sessions']['test_session'] = {
            'user_id': 1,
            'username': 'test_user',
            'role': 'admin'
        }

        # Mock the get_current_user function
        def mock_get_current_user(request):
            return {
                'user_id': 1,
                'username': 'test_user',
                'role': 'admin'
            }

        # Temporarily replace the get_current_user function
        import dashboards.enhanced_member_directory as emd
        original_get_current_user = emd.get_current_user
        emd.get_current_user = mock_get_current_user

        try:
            # Test the async method
            import asyncio
            html_response = asyncio.run(member_dir.serve_member_directory(mock_request))
            print(f"   ✅ HTML generated successfully ({len(html_response.text)} characters)")

            # Check for key elements in HTML
            key_elements = ['member-card', 'display_name', 'trading_style', 'reputation_score']
            found_elements = sum(1 for element in key_elements if element in html_response.text)

            print(f"   ✅ Key elements found: {found_elements}/{len(key_elements)}")

        except Exception as e:
            print(f"   ❌ HTML generation failed: {e}")
            return False
        finally:
            # Restore original function
            emd.get_current_user = original_get_current_user

        print("\n✅ MEMBER DIRECTORY TEST COMPLETED SUCCESSFULLY!")
        return True

    except Exception as e:
        print(f"❌ Member directory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_queries():
    """Test database queries directly."""
    print("\n🔍 TESTING DATABASE QUERIES")
    print("=" * 50)

    try:
        db = DatabaseManager('data/money_circle.db')

        # Test member profiles query
        cursor = db.conn.execute("""
            SELECT
                u.id, u.username, u.email, u.date_joined,
                mp.display_name, mp.bio, mp.trading_style, mp.risk_tolerance,
                mp.preferred_assets, mp.reputation_score, mp.joined_strategies,
                mp.total_votes,
                COALESCE(COUNT(DISTINCT ut.id), 0) as trades_count,
                COALESCE(COUNT(DISTINCT up.id), 0) as positions_count,
                COALESCE(SUM(up.pnl), 0) as total_pnl,
                COALESCE(MAX(ut.timestamp), '1970-01-01') as last_activity
            FROM users u
            LEFT JOIN member_profiles mp ON u.id = mp.user_id
            LEFT JOIN user_trades ut ON u.id = ut.user_id
            LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
            WHERE u.role IN ('member', 'admin') AND mp.user_id IS NOT NULL
            GROUP BY u.id, u.username, u.email, u.date_joined,
                     mp.display_name, mp.bio, mp.trading_style, mp.risk_tolerance,
                     mp.preferred_assets, mp.reputation_score, mp.joined_strategies,
                     mp.total_votes
            ORDER BY mp.reputation_score DESC, trades_count DESC
        """)

        members = cursor.fetchall()
        print(f"✅ Query returned {len(members)} members")

        if len(members) == 0:
            print("❌ No members found! Checking individual tables...")

            # Check users table
            users = db.conn.execute("SELECT COUNT(*) FROM users WHERE role IN ('member', 'admin')").fetchone()[0]
            print(f"   Users: {users}")

            # Check member_profiles table
            profiles = db.conn.execute("SELECT COUNT(*) FROM member_profiles").fetchone()[0]
            print(f"   Profiles: {profiles}")

            # Check user_trades table
            trades = db.conn.execute("SELECT COUNT(*) FROM user_trades").fetchone()[0]
            print(f"   Trades: {trades}")

            # Check user_positions table
            positions = db.conn.execute("SELECT COUNT(*) FROM user_positions").fetchone()[0]
            print(f"   Positions: {positions}")

            return False

        # Show sample data
        print("\nSample member data:")
        for i, member in enumerate(members[:3]):
            print(f"   {i+1}. {member[1]} ({member[4]})")
            print(f"      Style: {member[6]}, Risk: {member[7]}")
            print(f"      Reputation: {member[9]:.1f}, Trades: {member[12]}")

        return True

    except Exception as e:
        print(f"❌ Database query test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🎯 MEMBER DIRECTORY COMPREHENSIVE TEST")
    print("=" * 60)

    # Test 1: Database queries
    db_success = test_database_queries()

    # Test 2: Member directory functionality
    if db_success:
        dir_success = test_member_directory()
    else:
        print("❌ Skipping member directory test due to database issues")
        dir_success = False

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    if db_success and dir_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Member directory is working correctly")
        print("✅ All demo users should be visible")
        print("✅ Trading data is properly integrated")
        print("\n🌐 Visit: http://localhost:8084/club/members")
        return 0
    else:
        print("❌ SOME TESTS FAILED")
        if not db_success:
            print("❌ Database queries need fixing")
        if not dir_success:
            print("❌ Member directory functionality needs fixing")
        return 1

if __name__ == "__main__":
    sys.exit(main())
