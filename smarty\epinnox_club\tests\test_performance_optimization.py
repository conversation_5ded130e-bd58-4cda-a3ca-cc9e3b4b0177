#!/usr/bin/env python3
"""
Money Circle Performance Optimization Testing
Comprehensive performance analysis and optimization framework.
"""

import requests
import time
import json
import re
import os
import gzip
from datetime import datetime
from urllib.parse import urljoin

# Test configuration
BASE_URL = "http://localhost:8085"

# Dashboard pages to analyze
DASHBOARD_PAGES = [
    {"url": "/login", "name": "Login Page", "critical": True},
    {"url": "/dashboard", "name": "Personal Dashboard", "critical": True},
    {"url": "/club", "name": "Club Dashboard", "critical": True},
    {"url": "/analytics", "name": "Analytics Dashboard", "critical": False},
    {"url": "/members", "name": "Member Directory", "critical": False},
    {"url": "/strategies", "name": "Strategy Marketplace", "critical": False},
    {"url": "/responsive_test.html", "name": "Responsive Test", "critical": False},
    {"url": "/browser_test.html", "name": "Browser Test", "critical": False}
]

# CSS files to analyze
CSS_FILES = [
    {"url": "/static/css/design_system.css", "name": "Design System", "critical": True},
    {"url": "/static/css/unified_header.css", "name": "Header", "critical": True},
    {"url": "/static/css/unified_footer.css", "name": "Footer", "critical": False},
    {"url": "/static/css/dashboard.css", "name": "Dashboard", "critical": True},
    {"url": "/static/css/club.css", "name": "Club", "critical": False},
    {"url": "/static/css/club_analytics.css", "name": "Analytics", "critical": False},
    {"url": "/static/css/member_directory.css", "name": "Member Directory", "critical": False},
    {"url": "/static/css/strategy_marketplace.css", "name": "Strategy Marketplace", "critical": False},
    {"url": "/static/css/browser_fallbacks.css", "name": "Browser Fallbacks", "critical": False}
]

# JavaScript files to analyze
JS_FILES = [
    {"url": "/static/js/common.js", "name": "Common JS", "critical": True},
    {"url": "/static/js/personal_dashboard.js", "name": "Dashboard JS", "critical": True},
    {"url": "/static/js/club_dashboard.js", "name": "Club JS", "critical": False}
]

def measure_page_performance(url, name):
    """Measure comprehensive page performance metrics"""
    print(f"\n📊 Analyzing {name} ({url})")
    
    metrics = {
        "url": url,
        "name": name,
        "response_time": 0,
        "content_size": 0,
        "status_code": 0,
        "headers": {},
        "compression": False,
        "cache_headers": False,
        "performance_score": 0
    }
    
    try:
        # Measure response time
        start_time = time.time()
        response = requests.get(f"{BASE_URL}{url}", timeout=30)
        end_time = time.time()
        
        metrics["response_time"] = round((end_time - start_time) * 1000, 2)  # ms
        metrics["content_size"] = len(response.content)
        metrics["status_code"] = response.status_code
        metrics["headers"] = dict(response.headers)
        
        # Check compression
        if 'content-encoding' in response.headers:
            metrics["compression"] = response.headers['content-encoding']
        
        # Check cache headers
        cache_headers = ['cache-control', 'expires', 'etag', 'last-modified']
        metrics["cache_headers"] = any(header in response.headers for header in cache_headers)
        
        # Calculate performance score (0-100)
        score = 100
        if metrics["response_time"] > 1000:  # > 1s
            score -= 30
        elif metrics["response_time"] > 500:  # > 500ms
            score -= 15
        
        if metrics["content_size"] > 100000:  # > 100KB
            score -= 20
        elif metrics["content_size"] > 50000:  # > 50KB
            score -= 10
        
        if not metrics["compression"]:
            score -= 15
        
        if not metrics["cache_headers"]:
            score -= 10
        
        metrics["performance_score"] = max(0, score)
        
        print(f"  ⏱️  Response Time: {metrics['response_time']}ms")
        print(f"  📦 Content Size: {metrics['content_size']:,} bytes")
        print(f"  🗜️  Compression: {metrics['compression'] or 'None'}")
        print(f"  💾 Cache Headers: {'✅' if metrics['cache_headers'] else '❌'}")
        print(f"  🎯 Performance Score: {metrics['performance_score']}/100")
        
    except Exception as e:
        print(f"  ❌ Error measuring {name}: {str(e)}")
        metrics["error"] = str(e)
    
    return metrics

def analyze_css_performance():
    """Analyze CSS file performance and optimization opportunities"""
    print("\n🎨 CSS Performance Analysis")
    print("=" * 50)
    
    css_metrics = []
    total_css_size = 0
    
    for css_file in CSS_FILES:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{css_file['url']}", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                content = response.text
                size = len(content.encode('utf-8'))
                total_css_size += size
                
                # Analyze CSS content
                metrics = {
                    "name": css_file["name"],
                    "url": css_file["url"],
                    "critical": css_file["critical"],
                    "size": size,
                    "load_time": round((end_time - start_time) * 1000, 2),
                    "rules_count": len(re.findall(r'{[^}]*}', content)),
                    "media_queries": len(re.findall(r'@media[^{]+{', content)),
                    "selectors": len(re.findall(r'[^{}]+{', content)),
                    "compression_ratio": 0
                }
                
                # Test gzip compression potential
                compressed = gzip.compress(content.encode('utf-8'))
                metrics["compression_ratio"] = round((1 - len(compressed) / size) * 100, 1)
                
                css_metrics.append(metrics)
                
                print(f"  📄 {css_file['name']}:")
                print(f"    📦 Size: {size:,} bytes")
                print(f"    ⏱️  Load Time: {metrics['load_time']}ms")
                print(f"    📏 Rules: {metrics['rules_count']}")
                print(f"    📱 Media Queries: {metrics['media_queries']}")
                print(f"    🗜️  Compression Potential: {metrics['compression_ratio']}%")
                
        except Exception as e:
            print(f"  ❌ Error analyzing {css_file['name']}: {str(e)}")
    
    print(f"\n📊 CSS Summary:")
    print(f"  Total CSS Size: {total_css_size:,} bytes")
    print(f"  Critical CSS Size: {sum(m['size'] for m in css_metrics if m['critical']):,} bytes")
    print(f"  Average Compression: {sum(m['compression_ratio'] for m in css_metrics) / len(css_metrics):.1f}%")
    
    return css_metrics

def analyze_javascript_performance():
    """Analyze JavaScript performance"""
    print("\n🔧 JavaScript Performance Analysis")
    print("=" * 50)
    
    js_metrics = []
    
    for js_file in JS_FILES:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{js_file['url']}", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                content = response.text
                size = len(content.encode('utf-8'))
                
                metrics = {
                    "name": js_file["name"],
                    "url": js_file["url"],
                    "critical": js_file["critical"],
                    "size": size,
                    "load_time": round((end_time - start_time) * 1000, 2),
                    "functions": len(re.findall(r'function\s+\w+', content)),
                    "event_listeners": len(re.findall(r'addEventListener', content)),
                    "async_operations": len(re.findall(r'async\s+function|await\s+', content))
                }
                
                js_metrics.append(metrics)
                
                print(f"  📄 {js_file['name']}:")
                print(f"    📦 Size: {size:,} bytes")
                print(f"    ⏱️  Load Time: {metrics['load_time']}ms")
                print(f"    🔧 Functions: {metrics['functions']}")
                print(f"    👂 Event Listeners: {metrics['event_listeners']}")
                
        except Exception as e:
            print(f"  ❌ Error analyzing {js_file['name']}: {str(e)}")
    
    return js_metrics

def analyze_critical_rendering_path():
    """Analyze critical rendering path optimization"""
    print("\n🚀 Critical Rendering Path Analysis")
    print("=" * 50)
    
    try:
        # Analyze login page (most critical)
        response = requests.get(f"{BASE_URL}/login", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Count render-blocking resources
            css_links = len(re.findall(r'<link[^>]*rel=["\']stylesheet["\'][^>]*>', content))
            js_scripts = len(re.findall(r'<script[^>]*src=[^>]*>', content))
            inline_css = len(re.findall(r'<style[^>]*>.*?</style>', content, re.DOTALL))
            inline_js = len(re.findall(r'<script[^>]*>.*?</script>', content, re.DOTALL))
            
            print(f"  📄 Login Page Analysis:")
            print(f"    🎨 External CSS Files: {css_links}")
            print(f"    🔧 External JS Files: {js_scripts}")
            print(f"    🎨 Inline CSS Blocks: {inline_css}")
            print(f"    🔧 Inline JS Blocks: {inline_js}")
            
            # Check for critical CSS inlining
            if inline_css > 0:
                print(f"    ✅ Has inline CSS (good for critical path)")
            else:
                print(f"    ⚠️  No inline CSS (consider critical CSS inlining)")
            
            # Check for async/defer scripts
            async_scripts = len(re.findall(r'<script[^>]*async[^>]*>', content))
            defer_scripts = len(re.findall(r'<script[^>]*defer[^>]*>', content))
            
            print(f"    ⚡ Async Scripts: {async_scripts}")
            print(f"    ⏳ Defer Scripts: {defer_scripts}")
            
            if async_scripts + defer_scripts < js_scripts:
                print(f"    ⚠️  Consider async/defer for non-critical scripts")
            
    except Exception as e:
        print(f"  ❌ Error analyzing critical rendering path: {str(e)}")

def generate_optimization_recommendations():
    """Generate specific optimization recommendations"""
    print("\n💡 Performance Optimization Recommendations")
    print("=" * 50)
    
    recommendations = []
    
    # CSS Optimization
    recommendations.extend([
        "🎨 CSS Optimizations:",
        "  • Minify CSS files to reduce size by ~20-30%",
        "  • Enable gzip compression for CSS files",
        "  • Consider critical CSS inlining for above-the-fold content",
        "  • Optimize CSS selector specificity",
        "  • Remove unused CSS rules"
    ])
    
    # JavaScript Optimization
    recommendations.extend([
        "\n🔧 JavaScript Optimizations:",
        "  • Minify JavaScript files",
        "  • Use async/defer for non-critical scripts",
        "  • Implement code splitting for large bundles",
        "  • Optimize event listeners and DOM queries",
        "  • Consider service worker for caching"
    ])
    
    # Asset Loading
    recommendations.extend([
        "\n📦 Asset Loading Optimizations:",
        "  • Implement resource hints (preload, prefetch)",
        "  • Optimize image formats and sizes",
        "  • Enable browser caching with proper headers",
        "  • Use CDN for static assets",
        "  • Implement lazy loading for non-critical resources"
    ])
    
    # Mobile Performance
    recommendations.extend([
        "\n📱 Mobile Performance:",
        "  • Optimize touch interactions for 60fps",
        "  • Reduce layout thrashing in responsive design",
        "  • Optimize viewport meta tag",
        "  • Consider AMP or PWA features",
        "  • Test on actual mobile devices"
    ])
    
    for rec in recommendations:
        print(rec)
    
    return recommendations

def benchmark_performance():
    """Run comprehensive performance benchmark"""
    print("\n🏁 Performance Benchmark")
    print("=" * 50)
    
    all_metrics = []
    
    # Test all dashboard pages
    for page in DASHBOARD_PAGES:
        metrics = measure_page_performance(page["url"], page["name"])
        metrics["critical"] = page["critical"]
        all_metrics.append(metrics)
    
    # Calculate summary statistics
    response_times = [m["response_time"] for m in all_metrics if "response_time" in m]
    content_sizes = [m["content_size"] for m in all_metrics if "content_size" in m]
    performance_scores = [m["performance_score"] for m in all_metrics if "performance_score" in m]
    
    print(f"\n📈 Performance Summary:")
    print(f"  Average Response Time: {sum(response_times) / len(response_times):.1f}ms")
    print(f"  Average Content Size: {sum(content_sizes) / len(content_sizes):,.0f} bytes")
    print(f"  Average Performance Score: {sum(performance_scores) / len(performance_scores):.1f}/100")
    
    # Identify performance issues
    slow_pages = [m for m in all_metrics if m.get("response_time", 0) > 1000]
    large_pages = [m for m in all_metrics if m.get("content_size", 0) > 100000]
    
    if slow_pages:
        print(f"\n⚠️  Slow Pages (>1s):")
        for page in slow_pages:
            print(f"    • {page['name']}: {page['response_time']}ms")
    
    if large_pages:
        print(f"\n⚠️  Large Pages (>100KB):")
        for page in large_pages:
            print(f"    • {page['name']}: {page['content_size']:,} bytes")
    
    return all_metrics

def main():
    """Run comprehensive performance optimization analysis"""
    print("🚀 Money Circle Performance Optimization")
    print("=" * 60)
    print(f"Testing at: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all performance analyses
    page_metrics = benchmark_performance()
    css_metrics = analyze_css_performance()
    js_metrics = analyze_javascript_performance()
    analyze_critical_rendering_path()
    recommendations = generate_optimization_recommendations()
    
    print("\n" + "=" * 60)
    print("🎯 Performance Optimization Summary")
    print("=" * 60)
    
    # Calculate overall performance grade
    avg_score = sum(m.get("performance_score", 0) for m in page_metrics) / len(page_metrics)
    
    if avg_score >= 90:
        grade = "A+ (Excellent)"
    elif avg_score >= 80:
        grade = "A (Good)"
    elif avg_score >= 70:
        grade = "B (Fair)"
    elif avg_score >= 60:
        grade = "C (Needs Improvement)"
    else:
        grade = "D (Poor)"
    
    print(f"\n🏆 Overall Performance Grade: {grade} ({avg_score:.1f}/100)")
    
    print(f"\n🎯 Priority Optimizations:")
    print(f"  1. Enable gzip compression for all text assets")
    print(f"  2. Minify CSS and JavaScript files")
    print(f"  3. Implement critical CSS inlining")
    print(f"  4. Add proper cache headers")
    print(f"  5. Optimize image assets and formats")
    
    print(f"\n📊 Next Steps:")
    print(f"  1. Implement high-priority optimizations")
    print(f"  2. Set up performance monitoring")
    print(f"  3. Test optimizations across all browsers")
    print(f"  4. Establish performance budgets")
    print(f"  5. Create automated performance testing")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import sys
        sys.exit(1)
