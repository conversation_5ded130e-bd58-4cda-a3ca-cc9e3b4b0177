#!/usr/bin/env python3
"""
Test Dashboard Startup
Quick test to verify dashboard components work.
"""

import sys
import time
import requests
import subprocess
from pathlib import Path

def test_dashboard_imports():
    """Test if dashboard imports work."""
    print("🧪 Testing Dashboard Imports...")
    
    try:
        import live_data_bridge
        print("  ✅ live_data_bridge imported successfully")
    except Exception as e:
        print(f"  ❌ live_data_bridge import failed: {e}")
        return False
    
    try:
        import auth
        print("  ✅ auth imported successfully")
    except Exception as e:
        print(f"  ❌ auth import failed: {e}")
        return False
    
    try:
        import live_dashboard
        print("  ✅ live_dashboard imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ live_dashboard import failed: {e}")
        return False

def test_dashboard_startup():
    """Test dashboard startup."""
    print("\n🚀 Testing Dashboard Startup...")
    
    try:
        # Start dashboard process
        process = subprocess.Popen(
            [sys.executable, "live_dashboard.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("  📊 Dashboard process started, waiting for startup...")
        time.sleep(8)  # Wait for startup
        
        # Check if process is still running
        if process.poll() is None:
            print("  ✅ Dashboard process is running")
            
            # Test HTTP connection
            try:
                response = requests.get("http://localhost:8082", timeout=5)
                if response.status_code == 200:
                    print("  ✅ Dashboard HTTP server responding")
                    print("  🌐 Dashboard available at: http://localhost:8082")
                    
                    # Stop the process
                    process.terminate()
                    process.wait(timeout=5)
                    print("  ✅ Dashboard stopped successfully")
                    return True
                else:
                    print(f"  ❌ Dashboard HTTP error: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"  ❌ Dashboard HTTP connection failed: {e}")
        else:
            # Process died, get error output
            stdout, stderr = process.communicate()
            print(f"  ❌ Dashboard process died")
            print(f"  📝 stdout: {stdout}")
            print(f"  📝 stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Dashboard startup test failed: {e}")
        return False
    
    return False

def test_smart_trader_main():
    """Test smart trader main startup."""
    print("\n🧠 Testing Smart Trader Main...")
    
    try:
        # Import test
        import smart_trader_main
        print("  ✅ smart_trader_main imported successfully")
        
        # Check if it can create the main class
        trader = smart_trader_main.SmartTraderMain()
        print("  ✅ SmartTraderMain class created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Smart trader main test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🎯 Smart Trader Dashboard Diagnostic Test")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_dashboard_imports()
    
    # Test smart trader main
    main_ok = test_smart_trader_main()
    
    # Test dashboard startup (only if imports work)
    if imports_ok:
        dashboard_ok = test_dashboard_startup()
    else:
        dashboard_ok = False
        print("\n⚠️  Skipping dashboard startup test due to import failures")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Imports: {'PASS' if imports_ok else 'FAIL'}")
    print(f"✅ Smart Trader Main: {'PASS' if main_ok else 'FAIL'}")
    print(f"✅ Dashboard Startup: {'PASS' if dashboard_ok else 'FAIL'}")
    
    if imports_ok and main_ok and dashboard_ok:
        print("\n🎉 All tests passed! Dashboard should work correctly.")
        print("\n🚀 To run Smart Trader with dashboard:")
        print("   1. Terminal 1: python live_dashboard.py")
        print("   2. Terminal 2: python smart_trader_main.py")
        print("   3. Access: http://localhost:8082")
    elif imports_ok and main_ok:
        print("\n⚠️  Dashboard imports work but startup failed.")
        print("   Try running manually: python live_dashboard.py")
    else:
        print("\n❌ Some components failed. Check error messages above.")
        print("\n💡 Alternative: Use Money Circle platform instead:")
        print("   cd epinnox_club && python app.py")
        print("   Access: http://localhost:8086/dashboard")
    
    return imports_ok and main_ok and dashboard_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
