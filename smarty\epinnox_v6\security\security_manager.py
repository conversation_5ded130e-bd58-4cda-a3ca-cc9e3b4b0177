#!/usr/bin/env python3
"""
Security Manager - Phase 10.4
Comprehensive security hardening for Onnyx V6 trading system
"""

import asyncio
import logging
import time
import hashlib
import hmac
import secrets
import base64
import json
import os
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime, timedelta
import ipaddress
import re

# Cryptography imports with fallback
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """Security event for audit logging."""
    timestamp: float
    event_type: str  # 'auth_success', 'auth_failure', 'api_access', 'rate_limit', 'suspicious'
    source_ip: str
    user_agent: str
    endpoint: str
    details: Dict[str, Any]
    severity: str  # 'info', 'warning', 'critical'
    resolved: bool = False

@dataclass
class AccessAttempt:
    """Track access attempts for rate limiting."""
    ip_address: str
    timestamp: float
    endpoint: str
    success: bool
    user_agent: str

@dataclass
class SecurityConfig:
    """Security configuration settings."""
    api_key_encryption: bool = True
    rate_limiting_enabled: bool = True
    ip_whitelist_enabled: bool = False
    session_timeout_minutes: int = 60
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15
    password_min_length: int = 12
    require_2fa: bool = False
    audit_logging: bool = True
    secure_headers: bool = True

class SecurityManager:
    """
    Comprehensive security manager for Onnyx V6.

    Features:
    - API key encryption and secure storage
    - Access control and authentication
    - Rate limiting and DDoS protection
    - Security audit logging
    - Input validation and sanitization
    - Session management
    - IP whitelisting/blacklisting
    - Security event monitoring
    - Vulnerability assessment
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.security_config = SecurityConfig(**config.get('security', {}))

        # Security state
        self.access_attempts: List[AccessAttempt] = []
        self.security_events: List[SecurityEvent] = []
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.blocked_ips: Dict[str, float] = {}  # IP -> block_until_timestamp
        self.rate_limit_buckets: Dict[str, List[float]] = {}  # IP -> timestamps

        # Encryption setup
        self.encryption_key = None
        self.cipher_suite = None
        if CRYPTO_AVAILABLE and self.security_config.api_key_encryption:
            self._setup_encryption()

        # Security patterns
        self.suspicious_patterns = [
            r'(?i)(union|select|insert|delete|drop|create|alter)',  # SQL injection
            r'(?i)(<script|javascript:|vbscript:|onload=|onerror=)',  # XSS
            r'(?i)(\.\.\/|\.\.\\)',  # Path traversal
            r'(?i)(eval\(|exec\(|system\(|shell_exec)',  # Code injection
        ]

        # Whitelisted IPs (localhost and private networks by default)
        self.whitelisted_ips = [
            '127.0.0.1',
            '::1',
            '10.0.0.0/8',
            '**********/12',
            '***********/16'
        ]

        # Rate limiting settings
        self.rate_limits = {
            'api_general': {'requests': 100, 'window': 60},  # 100 requests per minute
            'api_trading': {'requests': 20, 'window': 60},   # 20 trading requests per minute
            'auth_login': {'requests': 5, 'window': 300},    # 5 login attempts per 5 minutes
        }

        logger.info("🔒 Security Manager initialized")
        if not CRYPTO_AVAILABLE:
            logger.warning("⚠️ Cryptography library not available - encryption disabled")

    def _setup_encryption(self):
        """Setup encryption for sensitive data."""
        try:
            # Generate or load encryption key
            key_file = Path(__file__).parent / ".security_key"

            if key_file.exists():
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                password = os.environ.get('ONNYX_SECURITY_PASSWORD', 'default_password_change_me').encode()
                salt = os.urandom(16)

                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                self.encryption_key = base64.urlsafe_b64encode(kdf.derive(password))

                # Save key securely
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)

                # Set restrictive permissions
                os.chmod(key_file, 0o600)

            self.cipher_suite = Fernet(self.encryption_key)
            logger.info("🔐 Encryption system initialized")

        except Exception as e:
            logger.error(f"Error setting up encryption: {e}")
            self.encryption_key = None
            self.cipher_suite = None

    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data like API keys."""
        if not self.cipher_suite:
            logger.warning("Encryption not available, storing data in plain text")
            return data

        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            return data

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        if not self.cipher_suite:
            return encrypted_data

        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            return encrypted_data

    def validate_input(self, input_data: str, input_type: str = 'general') -> Tuple[bool, str]:
        """Validate and sanitize input data."""
        try:
            if not input_data:
                return True, input_data

            # Check for suspicious patterns
            for pattern in self.suspicious_patterns:
                if re.search(pattern, input_data):
                    self._log_security_event(
                        'suspicious_input',
                        '0.0.0.0',
                        'unknown',
                        'input_validation',
                        {'pattern': pattern, 'input': input_data[:100]},
                        'warning'
                    )
                    return False, "Input contains suspicious patterns"

            # Type-specific validation
            if input_type == 'symbol':
                # Trading symbol validation
                if not re.match(r'^[A-Z0-9/:-]+$', input_data):
                    return False, "Invalid symbol format"

            elif input_type == 'amount':
                # Amount validation
                try:
                    amount = float(input_data)
                    if amount < 0 or amount > 1000000:  # Reasonable limits
                        return False, "Amount out of valid range"
                except ValueError:
                    return False, "Invalid amount format"

            elif input_type == 'api_key':
                # API key validation
                if len(input_data) < 10 or len(input_data) > 200:
                    return False, "API key length invalid"
                if not re.match(r'^[a-zA-Z0-9\-_]+$', input_data):
                    return False, "API key contains invalid characters"

            return True, input_data

        except Exception as e:
            logger.error(f"Error validating input: {e}")
            return False, "Input validation error"

    def check_rate_limit(self, ip_address: str, endpoint: str) -> Tuple[bool, Dict[str, Any]]:
        """Check if request is within rate limits."""
        try:
            if not self.security_config.rate_limiting_enabled:
                return True, {}

            current_time = time.time()

            # Determine rate limit category
            if 'trade' in endpoint.lower() or 'order' in endpoint.lower():
                limit_key = 'api_trading'
            elif 'login' in endpoint.lower() or 'auth' in endpoint.lower():
                limit_key = 'auth_login'
            else:
                limit_key = 'api_general'

            rate_limit = self.rate_limits.get(limit_key, self.rate_limits['api_general'])
            bucket_key = f"{ip_address}:{limit_key}"

            # Initialize bucket if not exists
            if bucket_key not in self.rate_limit_buckets:
                self.rate_limit_buckets[bucket_key] = []

            # Clean old timestamps
            window_start = current_time - rate_limit['window']
            self.rate_limit_buckets[bucket_key] = [
                ts for ts in self.rate_limit_buckets[bucket_key]
                if ts > window_start
            ]

            # Check if limit exceeded
            if len(self.rate_limit_buckets[bucket_key]) >= rate_limit['requests']:
                self._log_security_event(
                    'rate_limit_exceeded',
                    ip_address,
                    'unknown',
                    endpoint,
                    {'limit': rate_limit, 'current_count': len(self.rate_limit_buckets[bucket_key])},
                    'warning'
                )

                return False, {
                    'error': 'Rate limit exceeded',
                    'limit': rate_limit['requests'],
                    'window': rate_limit['window'],
                    'retry_after': rate_limit['window']
                }

            # Add current request
            self.rate_limit_buckets[bucket_key].append(current_time)

            return True, {
                'remaining': rate_limit['requests'] - len(self.rate_limit_buckets[bucket_key]),
                'reset_time': window_start + rate_limit['window']
            }

        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return True, {}  # Allow on error to avoid blocking legitimate requests

    def check_ip_access(self, ip_address: str) -> Tuple[bool, str]:
        """Check if IP address is allowed access."""
        try:
            current_time = time.time()

            # Check if IP is blocked
            if ip_address in self.blocked_ips:
                if current_time < self.blocked_ips[ip_address]:
                    return False, "IP address is temporarily blocked"
                else:
                    # Unblock expired blocks
                    del self.blocked_ips[ip_address]

            # Check whitelist if enabled
            if self.security_config.ip_whitelist_enabled:
                if not self._is_ip_whitelisted(ip_address):
                    self._log_security_event(
                        'ip_not_whitelisted',
                        ip_address,
                        'unknown',
                        'ip_check',
                        {},
                        'warning'
                    )
                    return False, "IP address not in whitelist"

            return True, "Access allowed"

        except Exception as e:
            logger.error(f"Error checking IP access: {e}")
            return True, "Access check error - allowing"

    def _is_ip_whitelisted(self, ip_address: str) -> bool:
        """Check if IP is in whitelist."""
        try:
            ip = ipaddress.ip_address(ip_address)

            for whitelisted in self.whitelisted_ips:
                if '/' in whitelisted:
                    # CIDR notation
                    network = ipaddress.ip_network(whitelisted, strict=False)
                    if ip in network:
                        return True
                else:
                    # Single IP
                    if str(ip) == whitelisted:
                        return True

            return False

        except Exception as e:
            logger.error(f"Error checking IP whitelist: {e}")
            return False

    def authenticate_request(self, api_key: str, signature: str, timestamp: str,
                           request_data: str) -> Tuple[bool, Dict[str, Any]]:
        """Authenticate API request with signature verification."""
        try:
            # Validate timestamp (prevent replay attacks)
            current_time = time.time()
            request_time = float(timestamp)

            if abs(current_time - request_time) > 300:  # 5 minutes tolerance
                return False, {'error': 'Request timestamp too old or too far in future'}

            # Decrypt API key if encrypted
            decrypted_key = self.decrypt_sensitive_data(api_key)

            # Validate API key format
            is_valid, error = self.validate_input(decrypted_key, 'api_key')
            if not is_valid:
                return False, {'error': f'Invalid API key: {error}'}

            # Verify signature (simplified - in production use proper HMAC)
            expected_signature = hmac.new(
                decrypted_key.encode(),
                f"{timestamp}{request_data}".encode(),
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(signature, expected_signature):
                return False, {'error': 'Invalid signature'}

            return True, {'user_id': 'authenticated_user', 'api_key_valid': True}

        except Exception as e:
            logger.error(f"Error authenticating request: {e}")
            return False, {'error': 'Authentication error'}

    def create_session(self, user_id: str, ip_address: str) -> str:
        """Create secure session."""
        try:
            session_id = secrets.token_urlsafe(32)
            current_time = time.time()

            session_data = {
                'user_id': user_id,
                'ip_address': ip_address,
                'created_at': current_time,
                'last_activity': current_time,
                'expires_at': current_time + (self.security_config.session_timeout_minutes * 60)
            }

            self.active_sessions[session_id] = session_data

            self._log_security_event(
                'session_created',
                ip_address,
                'unknown',
                'session_management',
                {'user_id': user_id, 'session_id': session_id[:8] + '...'},
                'info'
            )

            return session_id

        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return ""

    def validate_session(self, session_id: str, ip_address: str) -> Tuple[bool, Dict[str, Any]]:
        """Validate session and update activity."""
        try:
            if session_id not in self.active_sessions:
                return False, {'error': 'Invalid session'}

            session = self.active_sessions[session_id]
            current_time = time.time()

            # Check expiration
            if current_time > session['expires_at']:
                del self.active_sessions[session_id]
                return False, {'error': 'Session expired'}

            # Check IP consistency (optional security measure)
            if session['ip_address'] != ip_address:
                self._log_security_event(
                    'session_ip_mismatch',
                    ip_address,
                    'unknown',
                    'session_validation',
                    {'original_ip': session['ip_address'], 'current_ip': ip_address},
                    'warning'
                )
                # Could choose to invalidate session here for stricter security

            # Update activity
            session['last_activity'] = current_time
            session['expires_at'] = current_time + (self.security_config.session_timeout_minutes * 60)

            return True, {'user_id': session['user_id'], 'session_valid': True}

        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return False, {'error': 'Session validation error'}

    def _log_security_event(self, event_type: str, source_ip: str, user_agent: str,
                           endpoint: str, details: Dict[str, Any], severity: str):
        """Log security event for audit trail."""
        try:
            if not self.security_config.audit_logging:
                return

            event = SecurityEvent(
                timestamp=time.time(),
                event_type=event_type,
                source_ip=source_ip,
                user_agent=user_agent,
                endpoint=endpoint,
                details=details,
                severity=severity
            )

            self.security_events.append(event)

            # Log to file for persistence
            self._write_audit_log(event)

            # Alert on critical events
            if severity == 'critical':
                logger.critical(f"🚨 CRITICAL SECURITY EVENT: {event_type} from {source_ip}")
            elif severity == 'warning':
                logger.warning(f"⚠️ Security warning: {event_type} from {source_ip}")

        except Exception as e:
            logger.error(f"Error logging security event: {e}")

    def _write_audit_log(self, event: SecurityEvent):
        """Write security event to audit log file."""
        try:
            log_dir = Path(__file__).parent / "audit_logs"
            log_dir.mkdir(exist_ok=True)

            log_file = log_dir / f"security_audit_{datetime.now().strftime('%Y%m%d')}.log"

            log_entry = {
                'timestamp': datetime.fromtimestamp(event.timestamp).isoformat(),
                'event_type': event.event_type,
                'source_ip': event.source_ip,
                'user_agent': event.user_agent,
                'endpoint': event.endpoint,
                'details': event.details,
                'severity': event.severity
            }

            with open(log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

        except Exception as e:
            logger.error(f"Error writing audit log: {e}")

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP responses."""
        if not self.security_config.secure_headers:
            return {}

        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }

    def block_ip(self, ip_address: str, duration_minutes: int = None):
        """Block IP address for specified duration."""
        try:
            if duration_minutes is None:
                duration_minutes = self.security_config.lockout_duration_minutes

            block_until = time.time() + (duration_minutes * 60)
            self.blocked_ips[ip_address] = block_until

            self._log_security_event(
                'ip_blocked',
                ip_address,
                'system',
                'security_action',
                {'duration_minutes': duration_minutes, 'block_until': block_until},
                'warning'
            )

            logger.warning(f"🚫 Blocked IP {ip_address} for {duration_minutes} minutes")

        except Exception as e:
            logger.error(f"Error blocking IP: {e}")

    def run_security_scan(self) -> Dict[str, Any]:
        """Run comprehensive security vulnerability scan."""
        try:
            logger.info("🔍 Running security vulnerability scan...")

            scan_results = {
                'timestamp': time.time(),
                'scan_duration_ms': 0,
                'vulnerabilities': [],
                'recommendations': [],
                'security_score': 0,
                'status': 'unknown'
            }

            start_time = time.time()

            # Check 1: Encryption status
            if not CRYPTO_AVAILABLE:
                scan_results['vulnerabilities'].append({
                    'type': 'missing_encryption',
                    'severity': 'high',
                    'description': 'Cryptography library not available',
                    'recommendation': 'Install cryptography library for data encryption'
                })
            elif not self.cipher_suite:
                scan_results['vulnerabilities'].append({
                    'type': 'encryption_not_configured',
                    'severity': 'high',
                    'description': 'Encryption not properly configured',
                    'recommendation': 'Configure encryption with proper key management'
                })

            # Check 2: Default passwords
            if os.environ.get('ONNYX_SECURITY_PASSWORD') == 'default_password_change_me':
                scan_results['vulnerabilities'].append({
                    'type': 'default_password',
                    'severity': 'critical',
                    'description': 'Default security password in use',
                    'recommendation': 'Change ONNYX_SECURITY_PASSWORD environment variable'
                })

            # Check 3: Rate limiting
            if not self.security_config.rate_limiting_enabled:
                scan_results['vulnerabilities'].append({
                    'type': 'no_rate_limiting',
                    'severity': 'medium',
                    'description': 'Rate limiting is disabled',
                    'recommendation': 'Enable rate limiting to prevent abuse'
                })

            # Check 4: IP whitelisting
            if not self.security_config.ip_whitelist_enabled:
                scan_results['recommendations'].append({
                    'type': 'ip_whitelist',
                    'priority': 'medium',
                    'description': 'Consider enabling IP whitelisting for production',
                    'action': 'Configure trusted IP addresses'
                })

            # Check 5: Session security
            if self.security_config.session_timeout_minutes > 120:
                scan_results['vulnerabilities'].append({
                    'type': 'long_session_timeout',
                    'severity': 'low',
                    'description': 'Session timeout is very long',
                    'recommendation': 'Consider shorter session timeout for better security'
                })

            # Check 6: Audit logging
            if not self.security_config.audit_logging:
                scan_results['vulnerabilities'].append({
                    'type': 'no_audit_logging',
                    'severity': 'medium',
                    'description': 'Security audit logging is disabled',
                    'recommendation': 'Enable audit logging for security monitoring'
                })

            # Check 7: File permissions
            key_file = Path(__file__).parent / ".security_key"
            if key_file.exists():
                file_stat = key_file.stat()
                if oct(file_stat.st_mode)[-3:] != '600':
                    scan_results['vulnerabilities'].append({
                        'type': 'insecure_file_permissions',
                        'severity': 'medium',
                        'description': 'Security key file has insecure permissions',
                        'recommendation': 'Set file permissions to 600 (owner read/write only)'
                    })

            # Calculate security score
            total_checks = 7
            vulnerabilities = len(scan_results['vulnerabilities'])
            critical_vulns = len([v for v in scan_results['vulnerabilities'] if v['severity'] == 'critical'])
            high_vulns = len([v for v in scan_results['vulnerabilities'] if v['severity'] == 'high'])
            medium_vulns = len([v for v in scan_results['vulnerabilities'] if v['severity'] == 'medium'])

            # Scoring: Start with 100, deduct points for vulnerabilities
            security_score = 100
            security_score -= critical_vulns * 30  # Critical: -30 points each
            security_score -= high_vulns * 20      # High: -20 points each
            security_score -= medium_vulns * 10    # Medium: -10 points each

            scan_results['security_score'] = max(0, security_score)

            # Determine status
            if security_score >= 90:
                scan_results['status'] = 'excellent'
            elif security_score >= 75:
                scan_results['status'] = 'good'
            elif security_score >= 50:
                scan_results['status'] = 'needs_improvement'
            else:
                scan_results['status'] = 'critical'

            scan_results['scan_duration_ms'] = (time.time() - start_time) * 1000

            logger.info(f"✅ Security scan completed: Score {security_score}/100 ({scan_results['status']})")

            return scan_results

        except Exception as e:
            logger.error(f"Error running security scan: {e}")
            return {'error': str(e)}

    def get_security_status(self) -> Dict[str, Any]:
        """Get current security system status."""
        try:
            current_time = time.time()

            # Count recent security events
            one_hour_ago = current_time - 3600
            recent_events = [e for e in self.security_events if e.timestamp > one_hour_ago]

            critical_events = len([e for e in recent_events if e.severity == 'critical'])
            warning_events = len([e for e in recent_events if e.severity == 'warning'])

            return {
                'timestamp': current_time,
                'encryption_enabled': self.cipher_suite is not None,
                'rate_limiting_enabled': self.security_config.rate_limiting_enabled,
                'audit_logging_enabled': self.security_config.audit_logging,
                'active_sessions': len(self.active_sessions),
                'blocked_ips': len(self.blocked_ips),
                'recent_events_1h': len(recent_events),
                'critical_events_1h': critical_events,
                'warning_events_1h': warning_events,
                'total_security_events': len(self.security_events),
                'rate_limit_buckets': len(self.rate_limit_buckets),
                'security_config': asdict(self.security_config)
            }

        except Exception as e:
            logger.error(f"Error getting security status: {e}")
            return {'error': str(e)}

    def cleanup_expired_data(self):
        """Clean up expired sessions, blocks, and old events."""
        try:
            current_time = time.time()

            # Clean expired sessions
            expired_sessions = [
                sid for sid, session in self.active_sessions.items()
                if current_time > session['expires_at']
            ]
            for sid in expired_sessions:
                del self.active_sessions[sid]

            # Clean expired IP blocks
            expired_blocks = [
                ip for ip, block_until in self.blocked_ips.items()
                if current_time > block_until
            ]
            for ip in expired_blocks:
                del self.blocked_ips[ip]

            # Clean old security events (keep 7 days)
            cutoff_time = current_time - (7 * 24 * 3600)
            self.security_events = [
                e for e in self.security_events if e.timestamp > cutoff_time
            ]

            # Clean old rate limit buckets
            for bucket_key in list(self.rate_limit_buckets.keys()):
                # Keep only recent timestamps
                self.rate_limit_buckets[bucket_key] = [
                    ts for ts in self.rate_limit_buckets[bucket_key]
                    if ts > current_time - 3600  # Keep 1 hour
                ]

                # Remove empty buckets
                if not self.rate_limit_buckets[bucket_key]:
                    del self.rate_limit_buckets[bucket_key]

            if expired_sessions or expired_blocks:
                logger.debug(f"🧹 Cleaned {len(expired_sessions)} expired sessions, "
                           f"{len(expired_blocks)} expired IP blocks")

        except Exception as e:
            logger.error(f"Error cleaning up expired data: {e}")

    async def start_security_monitoring(self):
        """Start security monitoring background task."""
        logger.info("🚀 Starting security monitoring...")

        while True:
            try:
                # Clean up expired data every 5 minutes
                self.cleanup_expired_data()

                # Check for suspicious activity patterns
                await self._analyze_security_patterns()

                await asyncio.sleep(300)  # 5 minutes

            except Exception as e:
                logger.error(f"Error in security monitoring: {e}")
                await asyncio.sleep(300)

    async def _analyze_security_patterns(self):
        """Analyze security events for suspicious patterns."""
        try:
            current_time = time.time()
            one_hour_ago = current_time - 3600

            # Get recent events
            recent_events = [e for e in self.security_events if e.timestamp > one_hour_ago]

            # Check for brute force attempts
            failed_auths = [e for e in recent_events if e.event_type == 'auth_failure']
            if len(failed_auths) > 10:  # More than 10 failed auths in 1 hour
                # Group by IP
                ip_failures = {}
                for event in failed_auths:
                    ip = event.source_ip
                    if ip not in ip_failures:
                        ip_failures[ip] = 0
                    ip_failures[ip] += 1

                # Block IPs with excessive failures
                for ip, count in ip_failures.items():
                    if count > 5:  # More than 5 failures from same IP
                        self.block_ip(ip, 60)  # Block for 1 hour

                        self._log_security_event(
                            'brute_force_detected',
                            ip,
                            'system',
                            'security_analysis',
                            {'failed_attempts': count},
                            'critical'
                        )

            # Check for rate limit violations
            rate_limit_events = [e for e in recent_events if e.event_type == 'rate_limit_exceeded']
            if len(rate_limit_events) > 20:  # Excessive rate limiting
                self._log_security_event(
                    'excessive_rate_limiting',
                    'multiple',
                    'system',
                    'security_analysis',
                    {'events_count': len(rate_limit_events)},
                    'warning'
                )

        except Exception as e:
            logger.error(f"Error analyzing security patterns: {e}")

    def export_security_report(self) -> Dict[str, Any]:
        """Export comprehensive security report."""
        try:
            current_time = time.time()

            # Run security scan
            scan_results = self.run_security_scan()

            # Get security status
            status = self.get_security_status()

            # Analyze recent events
            one_day_ago = current_time - 86400
            recent_events = [e for e in self.security_events if e.timestamp > one_day_ago]

            event_summary = {}
            for event in recent_events:
                event_type = event.event_type
                if event_type not in event_summary:
                    event_summary[event_type] = 0
                event_summary[event_type] += 1

            report = {
                'report_timestamp': current_time,
                'report_date': datetime.fromtimestamp(current_time).isoformat(),
                'security_scan': scan_results,
                'security_status': status,
                'event_summary_24h': event_summary,
                'recent_events': [asdict(e) for e in recent_events[-10:]],  # Last 10 events
                'recommendations': self._generate_security_recommendations()
            }

            return report

        except Exception as e:
            logger.error(f"Error exporting security report: {e}")
            return {'error': str(e)}

    def _generate_security_recommendations(self) -> List[Dict[str, str]]:
        """Generate security recommendations based on current state."""
        recommendations = []

        if not CRYPTO_AVAILABLE:
            recommendations.append({
                'priority': 'high',
                'category': 'encryption',
                'title': 'Install Cryptography Library',
                'description': 'Install the cryptography library to enable data encryption',
                'action': 'pip install cryptography'
            })

        if not self.security_config.rate_limiting_enabled:
            recommendations.append({
                'priority': 'medium',
                'category': 'rate_limiting',
                'title': 'Enable Rate Limiting',
                'description': 'Enable rate limiting to prevent API abuse',
                'action': 'Set rate_limiting_enabled: true in security config'
            })

        if len(self.active_sessions) > 100:
            recommendations.append({
                'priority': 'low',
                'category': 'sessions',
                'title': 'Monitor Session Count',
                'description': 'High number of active sessions detected',
                'action': 'Review session management and consider shorter timeouts'
            })

        if len(self.blocked_ips) > 10:
            recommendations.append({
                'priority': 'medium',
                'category': 'security',
                'title': 'Review Blocked IPs',
                'description': 'Many IPs are currently blocked',
                'action': 'Review security logs for potential attacks'
            })

        return recommendations
