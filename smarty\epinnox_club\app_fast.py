#!/usr/bin/env python3
"""
Fast Money Circle Application
Optimized version with lazy loading and minimal startup time
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from aiohttp import web, WSMsgType
import aiohttp_cors
from aiohttp_session import setup
from aiohttp_session.cookie_storage import EncryptedCookieStorage
import aiohttp_jinja2
import jinja2

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Minimal logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FastMoneyCircleApp:
    """Fast Money Circle application with lazy loading."""

    def __init__(self, config_name: str = None):
        # Minimal initialization - only load config
        from config import get_config
        self.config = get_config(config_name)
        self.config.init_directories()

        # Lazy-loaded components
        self._db_manager = None
        self._user_manager = None
        self._components_loaded = False

        logger.info(f"[FAST] Fast Money Circle initialized with config: {config_name or 'default'}")

    @property
    def db_manager(self):
        """Lazy load database manager."""
        if self._db_manager is None:
            from database.models import DatabaseManager
            self._db_manager = DatabaseManager(self.config.DATABASE_PATH)
        return self._db_manager

    @property
    def user_manager(self):
        """Lazy load user manager."""
        if self._user_manager is None:
            from auth.user_manager import UserManager
            self._user_manager = UserManager(self.db_manager)
        return self._user_manager

    def load_components_if_needed(self):
        """Load heavy components only when needed."""
        if self._components_loaded:
            return

        try:
            # Load only essential components
            from exchanges.account_manager import ExchangeAccountManager
            self.exchange_manager = ExchangeAccountManager(self.db_manager)

            # Load basic dashboards
            from dashboards.personal_dashboard import PersonalDashboard
            self.personal_dashboard = PersonalDashboard(
                self.db_manager, self.user_manager, self.exchange_manager
            )

            self._components_loaded = True
            logger.info("[FAST] Essential components loaded")
        except Exception as e:
            logger.warning(f"[FAST] Component loading error: {e}")

    async def create_app(self) -> web.Application:
        """Create and configure the web application quickly."""
        app = web.Application()

        # Setup session middleware
        if isinstance(self.config.SECRET_KEY, str):
            secret_key = bytes.fromhex(self.config.SECRET_KEY)[:32]
            secret_key = secret_key.ljust(32, b'\0')
        else:
            secret_key = self.config.SECRET_KEY[:32].ljust(32, b'\0')

        setup(app, EncryptedCookieStorage(secret_key))

        # Setup Jinja2 templates
        template_path = Path(__file__).parent / 'templates'
        aiohttp_jinja2.setup(
            app,
            loader=jinja2.FileSystemLoader(str(template_path))
        )

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Minimal authentication middleware
        @web.middleware
        async def fast_auth_middleware(request: web.Request, handler):
            path = request.path
            public_routes = ['/login', '/logout', '/register', '/static/', '/favicon.ico', '/health']

            if any(path.startswith(route) for route in public_routes):
                return await handler(request)

            session_id = request.cookies.get('session_id')
            if session_id:
                session = self.user_manager.validate_session(session_id)
                if session:
                    request['user'] = session
                    request['user_id'] = session['user_id']
                    return await handler(request)

            # Redirect to login
            if path.startswith('/api/'):
                return web.json_response({'error': 'Authentication required'}, status=401)
            else:
                return web.Response(status=302, headers={'Location': '/login'})

        app.middlewares.append(fast_auth_middleware)

        # Setup routes
        self.setup_routes(app, cors)

        logger.info("[FAST] Web application configured quickly")
        return app

    def setup_routes(self, app: web.Application, cors):
        """Setup essential routes only."""

        # Static files
        app.router.add_static('/static/', path='static', name='static')

        # Essential routes
        app.router.add_get('/', self.home)
        app.router.add_get('/login', self.login_page)
        app.router.add_post('/login', self.login_post)
        app.router.add_get('/logout', self.logout)
        app.router.add_get('/personal-dashboard', self.personal_dashboard_page)
        app.router.add_get('/dashboard', self.personal_dashboard_page)  # Alias for dashboard
        app.router.add_get('/health', self.health_check)

        # Essential API routes
        app.router.add_get('/api/portfolio', self.api_get_portfolio)
        app.router.add_get('/api/notifications/count', self.api_notifications_count)
        app.router.add_get('/api/csrf-token', self.api_csrf_token)
        app.router.add_get('/api/exchanges/list', self.api_exchanges_list)
        app.router.add_get('/ws', self.websocket_handler)

        # Additional dashboard routes that were being requested
        app.router.add_get('/live-trading', self.live_trading_page)
        app.router.add_get('/auto-trader', self.auto_trader_page)

        # Add authentication middleware
        app.middlewares.append(self.auth_middleware)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        return app

    async def auth_middleware(self, app, handler):
        """Authentication middleware."""
        async def middleware_handler(request):
            # Skip auth for login page and static files
            if request.path in ['/login', '/health'] or request.path.startswith('/static/'):
                return await handler(request)

            # Check for session
            session_id = request.cookies.get('session_id')
            if session_id:
                session = self.user_manager.validate_session(session_id)
                if session:
                    # Add user to request
                    request['user'] = session
                    return await handler(request)

            # No valid session - redirect to login for pages, 401 for API
            if request.path.startswith('/api/'):
                return web.json_response({'error': 'Authentication required'}, status=401)
            else:
                return web.Response(status=302, headers={'Location': '/login'})

        return middleware_handler

    async def home(self, request: web.Request) -> web.Response:
        """Home page - redirect to dashboard or login."""
        if 'user' in request:
            return web.Response(status=302, headers={'Location': '/personal-dashboard'})
        return web.Response(status=302, headers={'Location': '/login'})

    async def login_page(self, request: web.Request) -> web.Response:
        """Login page."""
        template = aiohttp_jinja2.get_env(request.app).get_template('login.html')
        html = template.render()
        return web.Response(text=html, content_type='text/html')

    async def login_post(self, request: web.Request) -> web.Response:
        """Handle login."""
        try:
            data = await request.post()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return web.Response(status=302, headers={'Location': '/login?error=missing'})

            # Authenticate user
            user = self.user_manager.authenticate_user(username, password)
            if user:
                logger.info(f"[DEBUG] User object type: {type(user)}, User: {user}")
                session_id = self.user_manager.create_session(user)  # Pass the User object, not user['id']
                logger.info(f"[DEBUG] Session created: {session_id}")
                response = web.Response(status=302, headers={'Location': '/personal-dashboard'})
                response.set_cookie('session_id', session_id, httponly=True, secure=False)
                return response
            else:
                return web.Response(status=302, headers={'Location': '/login?error=invalid'})

        except Exception as e:
            logger.error(f"Login error: {e}")
            return web.Response(status=302, headers={'Location': '/login?error=system'})

    async def logout(self, request: web.Request) -> web.Response:
        """Logout."""
        session_id = request.cookies.get('session_id')
        if session_id:
            self.user_manager.destroy_session(session_id)  # Use destroy_session, not invalidate_session

        response = web.Response(status=302, headers={'Location': '/login'})
        response.del_cookie('session_id')
        return response

    async def personal_dashboard_page(self, request: web.Request) -> web.Response:
        """Personal dashboard page."""
        # Load components only when needed
        self.load_components_if_needed()

        try:
            user = request['user']
            template = aiohttp_jinja2.get_env(request.app).get_template('personal_dashboard.html')

            # Get comprehensive dashboard data to match template expectations
            dashboard_data = {
                'user': user,
                'portfolio': {
                    'total_value': 0.0,
                    'daily_change': 0.0,
                    'available_balance': 0.0,
                    'open_positions': 0,
                    'daily_pnl': 0.0,
                    'win_rate': 0.0,
                    'avg_trade': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0,
                    'positions': [],
                    'recent_trades': [],
                    'risk_metrics': {
                        'risk_level': 'low',
                        'leverage_ratio': 0.0,
                        'diversification_score': 0.0,
                        'var_95': 0.0,
                        'total_exposure': 0.0,
                        'max_position_risk': 0.0
                    },
                    'performance': {
                        'daily_pnl': 0.0,
                        'weekly_pnl': 0.0,
                        'monthly_pnl': 0.0,
                        'total_trades': 0,
                        'win_rate': 0.0,
                        'avg_trade': 0.0,
                        'max_drawdown': 0.0,
                        'sharpe_ratio': 0.0
                    }
                },
                'exchanges': [],
                'recent_trades': []
            }

            html = template.render(**dashboard_data)
            return web.Response(text=html, content_type='text/html')
        except Exception as e:
            logger.error(f"Dashboard error: {e}")
            return web.Response(text=f"Dashboard error: {e}", status=500)

    async def health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint."""
        return web.json_response({'status': 'ok', 'app': 'fast_money_circle'})

    async def api_get_portfolio(self, request: web.Request) -> web.Response:
        """Get user portfolio data."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Return minimal portfolio data for fast response
            portfolio_data = {
                'total_value': 0.0,
                'daily_change': 0.0,
                'daily_change_pct': 0.0,
                'positions': [],
                'recent_trades': [],
                'exchanges': []
            }

            return web.json_response(portfolio_data)
        except Exception as e:
            logger.error(f"Portfolio API error: {e}")
            return web.json_response({'error': 'Failed to get portfolio'}, status=500)

    async def websocket_handler(self, request: web.Request) -> web.WebSocketResponse:
        """Handle WebSocket connections."""
        ws = web.WebSocketResponse()

        # Check authentication for WebSocket
        session_id = request.cookies.get('session_id')
        if not session_id:
            logger.warning("[WS] WebSocket connection denied - no session")
            return web.Response(status=401, text='Authentication required')

        session = self.user_manager.validate_session(session_id)
        if not session:
            logger.warning("[WS] WebSocket connection denied - invalid session")
            return web.Response(status=401, text='Invalid session')

        await ws.prepare(request)
        logger.info(f"[WS] WebSocket connected for user: {session['username']}")

        try:
            # Send welcome message
            await ws.send_str('{"type": "welcome", "message": "Connected to Money Circle"}')

            # Keep connection alive
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = msg.json()
                        # Echo back for now
                        await ws.send_str(f'{{"type": "echo", "data": {data}}}')
                    except Exception as e:
                        logger.error(f"[WS] Message error: {e}")
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f"[WS] WebSocket error: {ws.exception()}")
                    break
        except Exception as e:
            logger.error(f"[WS] WebSocket handler error: {e}")
        finally:
            logger.info(f"[WS] WebSocket disconnected for user: {session['username']}")

        return ws

    async def api_notifications_count(self, request: web.Request) -> web.Response:
        """Get notification count for user."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Return minimal notification data for fast response
            return web.json_response({'count': 0, 'unread': 0})
        except Exception as e:
            logger.error(f"Notifications API error: {e}")
            return web.json_response({'error': 'Failed to get notifications'}, status=500)

    async def api_csrf_token(self, request: web.Request) -> web.Response:
        """Get CSRF token."""
        try:
            # Generate a simple token for development
            import secrets
            token = secrets.token_urlsafe(32)
            return web.json_response({'csrf_token': token})
        except Exception as e:
            logger.error(f"CSRF token API error: {e}")
            return web.json_response({'error': 'Failed to get CSRF token'}, status=500)

    async def api_exchanges_list(self, request: web.Request) -> web.Response:
        """Get list of user exchanges."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Return minimal exchange data for fast response
            exchanges_data = []
            return web.json_response({'exchanges': exchanges_data})
        except Exception as e:
            logger.error(f"Exchanges API error: {e}")
            return web.json_response({'error': 'Failed to get exchanges'}, status=500)

    async def live_trading_page(self, request: web.Request) -> web.Response:
        """Live trading page."""
        try:
            user = request['user']
            # For now, redirect to dashboard since live trading is not implemented in fast server
            return web.Response(status=302, headers={'Location': '/personal-dashboard'})
        except Exception as e:
            logger.error(f"Live trading page error: {e}")
            return web.Response(text=f"Live trading page error: {e}", status=500)

    async def auto_trader_page(self, request: web.Request) -> web.Response:
        """Auto trader page."""
        try:
            user = request['user']
            # For now, redirect to dashboard since auto trader is not implemented in fast server
            return web.Response(status=302, headers={'Location': '/personal-dashboard'})
        except Exception as e:
            logger.error(f"Auto trader page error: {e}")
            return web.Response(text=f"Auto trader page error: {e}", status=500)

    async def start_server(self):
        """Start the server quickly."""
        app = await self.create_app()

        runner = web.AppRunner(app)
        await runner.setup()

        site = web.TCPSite(runner, self.config.HOST, self.config.PORT)
        await site.start()

        logger.info(f"[FAST] Money Circle running at http://{self.config.HOST}:{self.config.PORT}")
        logger.info("[FAST] Fast startup complete - ready for connections")

        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("[FAST] Server stopped")
        finally:
            await runner.cleanup()

async def main():
    """Main entry point."""
    config_name = os.getenv('FLASK_ENV', 'development')
    app = FastMoneyCircleApp(config_name)
    await app.start_server()

if __name__ == '__main__':
    asyncio.run(main())
