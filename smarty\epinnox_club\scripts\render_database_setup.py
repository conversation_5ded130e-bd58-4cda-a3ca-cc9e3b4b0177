#!/usr/bin/env python3
"""
Render Database Setup for Money Circle
PostgreSQL database initialization and migration from SQLite schema
"""

import os
import asyncio
import logging
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import bcrypt

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RenderDatabaseSetup:
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable not set")
        
    def get_connection(self):
        """Get PostgreSQL connection."""
        return psycopg2.connect(self.database_url, cursor_factory=RealDictCursor)
    
    def create_tables(self):
        """Create all necessary tables for Money Circle."""
        logger.info("🗄️ Creating Money Circle database tables...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    role VARCHAR(20) DEFAULT 'member',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    profile_data JSONB DEFAULT '{}'
                )
            """)
            
            # User sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)
            
            # User trades table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_trades (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    amount DECIMAL(20, 8) NOT NULL,
                    price DECIMAL(20, 8) NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    strategy_name VARCHAR(100),
                    exchange VARCHAR(20),
                    trade_data JSONB DEFAULT '{}'
                )
            """)
            
            # User positions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_positions (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    size DECIMAL(20, 8) NOT NULL,
                    entry_price DECIMAL(20, 8) NOT NULL,
                    current_price DECIMAL(20, 8),
                    pnl DECIMAL(20, 8) DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'open',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    position_data JSONB DEFAULT '{}'
                )
            """)
            
            # Strategy proposals table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_proposals (
                    id SERIAL PRIMARY KEY,
                    creator_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    strategy_type VARCHAR(50),
                    risk_level VARCHAR(20),
                    expected_return DECIMAL(5, 2),
                    status VARCHAR(20) DEFAULT 'proposed',
                    votes_for INTEGER DEFAULT 0,
                    votes_against INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    voting_deadline TIMESTAMP,
                    strategy_data JSONB DEFAULT '{}'
                )
            """)
            
            # Strategy following table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_following (
                    id SERIAL PRIMARY KEY,
                    follower_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    strategy_id INTEGER REFERENCES strategy_proposals(id) ON DELETE CASCADE,
                    strategy_creator_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    allocation_amount DECIMAL(20, 8) DEFAULT 0,
                    UNIQUE(follower_id, strategy_id)
                )
            """)
            
            # Notifications table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    type VARCHAR(50) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    message TEXT,
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data JSONB DEFAULT '{}'
                )
            """)
            
            # System settings table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    id SERIAL PRIMARY KEY,
                    key VARCHAR(100) UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market data table (for caching)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    exchange VARCHAR(20) NOT NULL,
                    price DECIMAL(20, 8) NOT NULL,
                    volume DECIMAL(20, 8),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data JSONB DEFAULT '{}'
                )
            """)
            
            # Trading signals table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    signal_type VARCHAR(20) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    strength DECIMAL(3, 2),
                    price DECIMAL(20, 8),
                    strategy_name VARCHAR(100),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    signal_data JSONB DEFAULT '{}'
                )
            """)
            
            conn.commit()
            logger.info("✅ Database tables created successfully")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"❌ Error creating tables: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def create_indexes(self):
        """Create database indexes for performance."""
        logger.info("📊 Creating database indexes...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
                "CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_user_trades_user_id ON user_trades(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_user_trades_symbol ON user_trades(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_user_trades_timestamp ON user_trades(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_user_positions_user_id ON user_positions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_user_positions_symbol ON user_positions(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_creator_id ON strategy_proposals(creator_id)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_status ON strategy_proposals(status)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_following_follower_id ON strategy_following(follower_id)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_following_strategy_id ON strategy_following(strategy_id)",
                "CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read)",
                "CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_trading_signals_timestamp ON trading_signals(timestamp)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            conn.commit()
            logger.info("✅ Database indexes created successfully")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"❌ Error creating indexes: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def seed_initial_data(self):
        """Seed initial data for Money Circle."""
        logger.info("🌱 Seeding initial data...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Create admin user
            admin_password = bcrypt.hashpw("securepass123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, role, created_at, is_active)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (username) DO NOTHING
            """, ('epinnox', '<EMAIL>', admin_password, 'admin', datetime.now(), True))
            
            # Create demo member users
            demo_users = [
                ('alex_trader', '<EMAIL>', 'member'),
                ('sarah_investor', '<EMAIL>', 'member'),
                ('mike_analyst', '<EMAIL>', 'member')
            ]
            
            demo_password = bcrypt.hashpw("demo123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            for username, email, role in demo_users:
                cursor.execute("""
                    INSERT INTO users (username, email, password_hash, role, created_at, is_active)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (username) DO NOTHING
                """, (username, email, demo_password, role, datetime.now(), True))
            
            # System settings
            system_settings = [
                ('platform_name', 'Money Circle Investment Club'),
                ('platform_version', '1.0.0'),
                ('maintenance_mode', 'false'),
                ('registration_enabled', 'true'),
                ('max_users', '1000'),
                ('session_timeout', '7200'),
                ('backup_enabled', 'true'),
                ('monitoring_enabled', 'true')
            ]
            
            for key, value in system_settings:
                cursor.execute("""
                    INSERT INTO system_settings (key, value, created_at)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (key) DO NOTHING
                """, (key, value, datetime.now()))
            
            conn.commit()
            logger.info("✅ Initial data seeded successfully")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"❌ Error seeding data: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def verify_setup(self):
        """Verify database setup is correct."""
        logger.info("🔍 Verifying database setup...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Check tables exist
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            tables = [row['table_name'] for row in cursor.fetchall()]
            
            expected_tables = [
                'users', 'user_sessions', 'user_trades', 'user_positions',
                'strategy_proposals', 'strategy_following', 'notifications',
                'system_settings', 'market_data', 'trading_signals'
            ]
            
            missing_tables = [t for t in expected_tables if t not in tables]
            if missing_tables:
                logger.error(f"❌ Missing tables: {missing_tables}")
                return False
            
            # Check admin user exists
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE username = 'epinnox' AND role = 'admin'")
            admin_count = cursor.fetchone()['count']
            
            if admin_count == 0:
                logger.error("❌ Admin user not found")
                return False
            
            logger.info("✅ Database setup verification passed")
            logger.info(f"📊 Found {len(tables)} tables")
            logger.info(f"👤 Admin user configured")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Verification error: {e}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    def run_setup(self):
        """Run complete database setup."""
        logger.info("🚀 Starting Money Circle database setup for Render...")
        
        try:
            self.create_tables()
            self.create_indexes()
            self.seed_initial_data()
            
            if self.verify_setup():
                logger.info("🎉 Database setup completed successfully!")
                return True
            else:
                logger.error("❌ Database setup verification failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False

def main():
    """Main setup function."""
    if not os.getenv('DATABASE_URL'):
        logger.error("❌ DATABASE_URL environment variable not set")
        logger.info("💡 Make sure you've added a PostgreSQL database in Render")
        return False
    
    setup = RenderDatabaseSetup()
    return setup.run_setup()

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
