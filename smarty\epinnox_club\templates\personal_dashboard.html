{% extends "base.html" %}

{% block title %}Money Circle - Personal Dashboard{% endblock %}

{% block extra_css %}
<!-- Dashboard CSS loaded asynchronously for performance -->
<link rel="preload" href="/static/css/dashboard.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="/static/css/club.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/club.css">
</noscript>

<!-- Preload Chart.js for better performance -->
<link rel="preload" href="https://cdn.jsdelivr.net/npm/chart.js" as="script">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Main Dashboard Content -->
    <div class="dashboard-grid">
        <!-- Portfolio Overview -->
        <section class="portfolio-overview">
            <h2>💼 Portfolio Overview</h2>
            <div class="portfolio-cards">
                <div class="portfolio-card">
                    <h3>Total Value</h3>
                    <div class="value">${{ portfolio.total_value|round(2) }}</div>
                    <div class="change {{ 'positive' if portfolio.daily_change >= 0 else 'negative' }}">
                        {{ '+' if portfolio.daily_change >= 0 else '' }}{{ portfolio.daily_change|round(2) }}%
                    </div>
                </div>
                <div class="portfolio-card">
                    <h3>Available Balance</h3>
                    <div class="value">${{ portfolio.available_balance|round(2) }}</div>
                </div>
                <div class="portfolio-card">
                    <h3>Open Positions</h3>
                    <div class="value">{{ portfolio.open_positions }}</div>
                </div>
                <div class="portfolio-card">
                    <h3>P&L Today</h3>
                    <div class="value {{ 'positive' if portfolio.daily_pnl >= 0 else 'negative' }}">
                        ${{ portfolio.daily_pnl|round(2) }}
                    </div>
                </div>
            </div>
        </section>

        <!-- Exchange Connections -->
        <section class="exchange-connections">
            <h2>🔗 Exchange Connections</h2>
            <div class="section-status">
                <div id="exchanges-loading" class="loading-status">Loading exchange data...</div>
            </div>
            <div class="exchange-grid">
                {% for exchange in exchanges %}
                <div class="exchange-card {{ 'connected' if exchange.connected else 'disconnected' }}">
                    <h4>{{ exchange.exchange_name }}</h4>
                    <div class="connection-status">
                        {{ '🟢 Connected' if exchange.connected else '🔴 Disconnected' }}
                    </div>
                    {% if exchange.connected and exchange.balance %}
                    <div class="balance-summary">
                        <div class="balance-item">
                            <span>USDT:</span>
                            <span>${{ exchange.balance.get('USDT', 0)|round(2) }}</span>
                        </div>
                    </div>
                    {% endif %}
                    <div class="exchange-actions">
                        <button onclick="refreshExchange('{{ exchange.exchange_name }}')">🔄 Refresh</button>
                        <button onclick="removeExchange({{ exchange.id }})">🗑️ Remove</button>
                    </div>
                </div>
                {% endfor %}

                <!-- Add Exchange Card -->
                <div class="exchange-card add-exchange">
                    <h4>➕ Add Exchange</h4>
                    <p>Connect a new exchange account</p>
                    <button onclick="showAddExchangeModal()">Add Exchange</button>
                </div>
            </div>
        </section>

        <!-- Quick Trade Controls -->
        <section class="quick-trade-section">
            <h2>⚡ Quick Trade</h2>
            <div class="quick-trade-buttons">
                <!-- Quick trade buttons will be dynamically generated -->
            </div>
        </section>

        <!-- Trading Interface -->
        <section class="trading-interface">
            <h2>📈 Advanced Trading</h2>
            <div class="trading-tabs">
                <button class="tab-btn active" onclick="switchTab('market')">Market Order</button>
                <button class="tab-btn" onclick="switchTab('limit')">Limit Order</button>
                <button class="tab-btn" onclick="switchTab('positions')">Positions</button>
                <button class="tab-btn" onclick="switchTab('risk')">Risk Management</button>
            </div>

            <!-- Market Order Tab -->
            <div id="market-tab" class="tab-content active">
                <div class="order-form">
                    <div class="order-type-selector">
                        <button class="order-type-btn active" data-type="buy">Buy</button>
                        <button class="order-type-btn" data-type="sell">Sell</button>
                    </div>

                    <div class="form-group">
                        <label>Symbol</label>
                        <select id="market-symbol">
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Amount (USDT)</label>
                        <input type="number" id="market-amount" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <button class="place-order-btn" onclick="placeMarketOrder()">
                        Place Market Order
                    </button>
                </div>
            </div>

            <!-- Limit Order Tab -->
            <div id="limit-tab" class="tab-content">
                <div class="order-form">
                    <div class="order-type-selector">
                        <button class="order-type-btn active" data-type="buy">Buy</button>
                        <button class="order-type-btn" data-type="sell">Sell</button>
                    </div>

                    <div class="form-group">
                        <label>Symbol</label>
                        <select id="limit-symbol">
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Price (USDT)</label>
                        <input type="number" id="limit-price" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>Amount (USDT)</label>
                        <input type="number" id="limit-amount" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <button class="place-order-btn" onclick="placeLimitOrder()">
                        Place Limit Order
                    </button>
                </div>
            </div>

            <!-- Positions Tab -->
            <div id="positions-tab" class="tab-content">
                <div class="section-status">
                    <div id="positions-loading" class="loading-status">Loading positions...</div>
                </div>
                <div class="positions-list" id="positions-list">
                    <!-- Positions will be loaded via JavaScript -->
                </div>
            </div>

            <!-- Risk Management Tab -->
            <div id="risk-tab" class="tab-content">
                <!-- Risk controls will be dynamically added by JavaScript -->
            </div>
        </section>

        <!-- Market Data -->
        <section class="market-data">
            <h2>📊 Market Data</h2>
            <div class="section-status">
                <div id="market_data-loading" class="loading-status">Loading market data...</div>
            </div>
            <div class="market-widgets">
                <!-- Price Chart -->
                <div class="market-widget">
                    <h3>Price Chart</h3>
                    <canvas id="price-chart"></canvas>
                </div>

                <!-- Order Book -->
                <div class="market-widget">
                    <h3>Order Book</h3>
                    <div class="orderbook" id="orderbook">
                        <!-- Order book data will be loaded via JavaScript -->
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="market-widget">
                    <h3>Recent Trades</h3>
                    <div class="trades-list" id="trades-list">
                        <!-- Recent trades will be loaded via JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Analytics -->
        <section class="performance-analytics">
            <h2>📈 Performance Analytics</h2>
            <div class="analytics-grid">
                <div class="analytics-card">
                    <h4>Win Rate</h4>
                    <div class="metric" id="win-rate">{{ portfolio.win_rate|round(1) }}%</div>
                </div>
                <div class="analytics-card">
                    <h4>Avg Trade</h4>
                    <div class="metric" id="avg-trade">${{ portfolio.avg_trade|round(2) }}</div>
                </div>
                <div class="analytics-card">
                    <h4>Max Drawdown</h4>
                    <div class="metric" id="max-drawdown">{{ portfolio.max_drawdown|round(1) }}%</div>
                </div>
                <div class="analytics-card">
                    <h4>Sharpe Ratio</h4>
                    <div class="metric" id="sharpe-ratio">{{ portfolio.sharpe_ratio|round(2) }}</div>
                </div>
            </div>
        </section>

        <!-- Risk Management -->
        <section class="risk-management">
            <h2>⚠️ Risk Management</h2>
            <div class="risk-grid">
                <div class="risk-card">
                    <h4>Risk Level</h4>
                    <div class="metric risk-level {{ portfolio.risk_metrics.risk_level or 'unknown' }}" id="risk-level">
                        {{ (portfolio.risk_metrics.risk_level or 'UNKNOWN')|upper }}
                    </div>
                </div>
                <div class="risk-card">
                    <h4>Leverage Ratio</h4>
                    <div class="metric" id="leverage-ratio">{{ portfolio.risk_metrics.leverage_ratio|round(2) or '0.00' }}x</div>
                </div>
                <div class="risk-card">
                    <h4>Diversification</h4>
                    <div class="metric" id="diversification-score">{{ portfolio.risk_metrics.diversification_score|round(0) or '0' }}/100</div>
                </div>
                <div class="risk-card">
                    <h4>VaR (95%)</h4>
                    <div class="metric" id="var-95">${{ portfolio.risk_metrics.var_95|round(2) or '0.00' }}</div>
                </div>
            </div>
        </section>
    </div>

    <!-- Modals -->
    <!-- Enhanced Add Exchange Modal -->
    <div id="add-exchange-modal" class="modal">
        <div class="modal-content exchange-modal">
            <div class="modal-header">
                <h2>🔗 Add Exchange Connection</h2>
                <span class="close" onclick="closeModal('add-exchange-modal')">&times;</span>
            </div>

            <div class="modal-body">
                <form id="add-exchange-form">
                    <!-- Exchange Selection -->
                    <div class="form-group">
                        <label for="exchange-type">
                            <span class="label-text">Exchange Platform</span>
                            <span class="label-required">*</span>
                        </label>
                        <select id="exchange-type" required>
                            <option value="">Select Exchange</option>
                            <option value="HTX" data-testnet="false">HTX (Huobi) - Futures Trading</option>
                            <option value="Binance" data-testnet="true">Binance - Spot & Futures</option>
                            <option value="Bybit" data-testnet="true">Bybit - Derivatives</option>
                        </select>
                        <div class="field-help">
                            Choose your preferred exchange platform for trading
                        </div>
                    </div>

                    <!-- Exchange Info Panel -->
                    <div id="exchange-info" class="exchange-info-panel" style="display: none;">
                        <div class="info-content">
                            <div class="exchange-logo">
                                <span id="exchange-icon">🏦</span>
                                <span id="exchange-name">Exchange</span>
                            </div>
                            <div class="exchange-features">
                                <div class="feature-list" id="exchange-features">
                                    <!-- Features will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Credentials Section -->
                    <div class="credentials-section">
                        <h3>🔐 API Credentials</h3>
                        <div class="security-notice">
                            <span class="security-icon">🛡️</span>
                            <span>Your credentials are encrypted and stored securely</span>
                        </div>

                        <div class="form-group">
                            <label for="api-key">
                                <span class="label-text">API Key</span>
                                <span class="label-required">*</span>
                            </label>
                            <input type="text" id="api-key" required placeholder="Enter your API key">
                            <div class="field-help">
                                Your exchange API key (read and trade permissions required)
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="api-secret">
                                <span class="label-text">API Secret</span>
                                <span class="label-required">*</span>
                            </label>
                            <input type="password" id="api-secret" required placeholder="Enter your API secret">
                            <div class="field-help">
                                Your exchange API secret key
                            </div>
                        </div>

                        <div class="form-group" id="passphrase-group" style="display: none;">
                            <label for="api-passphrase">
                                <span class="label-text">Passphrase</span>
                                <span class="label-optional">(Optional)</span>
                            </label>
                            <input type="password" id="api-passphrase" placeholder="Enter passphrase if required">
                            <div class="field-help">
                                Some exchanges require a passphrase for API access
                            </div>
                        </div>
                    </div>

                    <!-- Trading Environment -->
                    <div class="form-group environment-selection">
                        <label>
                            <span class="label-text">Trading Environment</span>
                        </label>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="environment" value="testnet" id="env-testnet">
                                <span class="radio-label">
                                    <span class="radio-title">🧪 Testnet</span>
                                    <span class="radio-desc">Safe testing environment (recommended for first setup)</span>
                                </span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="environment" value="mainnet" id="env-mainnet">
                                <span class="radio-label">
                                    <span class="radio-title">💰 Mainnet</span>
                                    <span class="radio-desc">Live trading with real funds</span>
                                </span>
                            </label>
                        </div>
                        <div class="environment-notice" id="environment-notice">
                            <!-- Notice will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Connection Test -->
                    <div class="connection-test-section">
                        <button type="button" id="test-connection-btn" class="test-connection-btn">
                            <span class="btn-icon">🔍</span>
                            <span class="btn-text">Test Connection</span>
                        </button>
                        <div id="connection-result" class="connection-result">
                            <!-- Test results will appear here -->
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" onclick="closeModal('add-exchange-modal')" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" id="add-exchange-submit" class="btn-primary" disabled>
                            <span class="btn-icon">➕</span>
                            <span class="btn-text">Add Exchange</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Exchange Management Modal -->
    <div id="manage-exchange-modal" class="modal">
        <div class="modal-content exchange-modal">
            <div class="modal-header">
                <h2>⚙️ Manage Exchange Connection</h2>
                <span class="close" onclick="closeModal('manage-exchange-modal')">&times;</span>
            </div>

            <div class="modal-body">
                <div id="exchange-details">
                    <!-- Exchange details will be populated by JavaScript -->
                </div>

                <div class="management-actions">
                    <button type="button" id="refresh-balance-btn" class="action-btn">
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">Refresh Balance</span>
                    </button>

                    <button type="button" id="test-exchange-btn" class="action-btn">
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">Test Connection</span>
                    </button>

                    <button type="button" id="remove-exchange-btn" class="action-btn danger">
                        <span class="btn-icon">🗑️</span>
                        <span class="btn-text">Remove Exchange</span>
                    </button>
                </div>

                <div id="exchange-status" class="exchange-status">
                    <!-- Status information will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Load Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Load dashboard JavaScript (synchronously to ensure functions are available) -->
<script src="/static/js/dashboard_csrf_fix.js"></script>
<script src="/static/js/personal_dashboard.js"></script>
<script src="/static/js/exchange_management.js"></script>

<script>
    // Initialize dashboard data immediately (critical for functionality)
    window.dashboardData = {
        user: {{ user|tojson }},
        portfolio: {{ portfolio|tojson }},
        exchanges: {{ exchanges|tojson }}
    };

    // Enhanced initialization with better error handling
    function initDashboard() {
        console.log('🚀 Initializing Personal Dashboard');

        try {
            if (typeof initializePersonalDashboard === 'function') {
                initializePersonalDashboard();
                console.log('✅ Personal dashboard initialized successfully');
            } else {
                console.warn('⚠️ initializePersonalDashboard function not found, retrying...');
                // Retry if script not loaded yet
                setTimeout(initDashboard, 100);
            }
        } catch (error) {
            console.error('❌ Error initializing dashboard:', error);
            // Retry on error
            setTimeout(initDashboard, 500);
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM Content Loaded - Starting dashboard initialization');
        initDashboard();

        // Also ensure header navigation is working
        if (typeof initializeHeaderNavigation === 'function') {
            console.log('🧭 Initializing header navigation');
            initializeHeaderNavigation();
        }
    });

    // Fallback initialization for buttons if functions are not available
    function ensureButtonFunctionality() {
        // Add Exchange Button
        const addExchangeBtn = document.querySelector('[onclick="showAddExchangeModal()"]');
        if (addExchangeBtn && typeof showAddExchangeModal !== 'function') {
            addExchangeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('📝 Add Exchange button clicked (fallback)');
                const modal = document.getElementById('add-exchange-modal');
                if (modal) {
                    modal.style.display = 'block';
                    modal.classList.add('active');
                }
            });
        }

        // Tab buttons
        document.querySelectorAll('[onclick^="switchTab"]').forEach(btn => {
            if (typeof switchTab !== 'function') {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabName = this.getAttribute('onclick').match(/switchTab\('(.+?)'\)/)[1];
                    console.log(`🔄 Switching to tab: ${tabName} (fallback)`);

                    // Basic tab switching functionality
                    document.querySelectorAll('.tab-content').forEach(tab => {
                        tab.classList.remove('active');
                    });
                    document.querySelectorAll('.tab-btn').forEach(tabBtn => {
                        tabBtn.classList.remove('active');
                    });

                    const targetTab = document.getElementById(`${tabName}-tab`);
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }
                    this.classList.add('active');
                });
            }
        });

        // Close modal buttons
        document.querySelectorAll('[onclick^="closeModal"]').forEach(btn => {
            if (typeof closeModal !== 'function') {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const modalId = this.getAttribute('onclick').match(/closeModal\('(.+?)'\)/)[1];
                    console.log(`❌ Closing modal: ${modalId} (fallback)`);

                    const modal = document.getElementById(modalId);
                    if (modal) {
                        modal.style.display = 'none';
                        modal.classList.remove('active');
                    }
                });
            }
        });
    }

    // Run fallback initialization after a short delay
    setTimeout(ensureButtonFunctionality, 200);
</script>
{% endblock %}
