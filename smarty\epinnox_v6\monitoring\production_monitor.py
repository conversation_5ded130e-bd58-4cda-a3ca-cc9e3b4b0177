#!/usr/bin/env python3
"""
Production Monitor - Phase 10.1
Real-time performance monitoring with metrics collection and alerting
"""

import asyncio
import logging
import time
import psutil
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import aiohttp
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    uptime_hours: float

@dataclass
class TradingMetrics:
    """Trading system specific metrics."""
    timestamp: float
    signals_per_minute: float
    execution_latency_ms: float
    api_response_time_ms: float
    active_positions: int
    daily_pnl: float
    win_rate_24h: float
    error_rate_percent: float
    websocket_connections: int
    last_signal_time: float
    last_execution_time: float

@dataclass
class PerformanceAlert:
    """Performance alert definition."""
    alert_id: str
    timestamp: float
    severity: str  # 'info', 'warning', 'critical'
    category: str  # 'system', 'trading', 'network'
    message: str
    metric_name: str
    current_value: float
    threshold_value: float
    resolved: bool = False

class ProductionMonitor:
    """
    Real-time production monitoring system for Onnyx V6.

    Features:
    - System resource monitoring (CPU, memory, disk, network)
    - Trading system metrics (latency, signals, executions)
    - Real-time alerting with configurable thresholds
    - Performance trend analysis
    - Health score calculation
    - Metrics export for external monitoring tools
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.monitor_config = config.get('production_monitor', {})

        # Monitoring intervals
        self.system_interval = self.monitor_config.get('system_interval', 30)  # 30 seconds
        self.trading_interval = self.monitor_config.get('trading_interval', 10)  # 10 seconds
        self.alert_check_interval = self.monitor_config.get('alert_check_interval', 5)  # 5 seconds

        # Alert thresholds
        self.thresholds = self.monitor_config.get('thresholds', {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'execution_latency_ms': 2000.0,
            'api_response_time_ms': 1000.0,
            'error_rate_percent': 5.0,
            'signal_gap_minutes': 2.0
        })

        # Data storage
        self.system_metrics: List[SystemMetrics] = []
        self.trading_metrics: List[TradingMetrics] = []
        self.alerts: List[PerformanceAlert] = []

        # Monitoring state
        self.monitoring_active = False
        self.start_time = time.time()
        self.last_network_stats = None

        # External integrations
        self.data_store = None
        self.execution_controller = None
        self.dashboard = None

        # Metrics retention (keep last 24 hours)
        self.max_metrics_age = 24 * 3600  # 24 hours

        logger.info("🔍 Production Monitor initialized")

    def set_integrations(self, data_store=None, execution_controller=None, dashboard=None):
        """Set external component integrations."""
        self.data_store = data_store
        self.execution_controller = execution_controller
        self.dashboard = dashboard
        logger.info("🔗 Production Monitor integrations set")

    async def start_monitoring(self):
        """Start all monitoring tasks."""
        self.monitoring_active = True
        logger.info("🚀 Starting production monitoring...")

        # Start monitoring tasks
        tasks = [
            self._monitor_system_metrics(),
            self._monitor_trading_metrics(),
            self._check_alerts(),
            self._cleanup_old_metrics()
        ]

        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error in monitoring tasks: {e}")
        finally:
            self.monitoring_active = False

    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring_active = False
        logger.info("🛑 Production monitoring stopped")

    async def _monitor_system_metrics(self):
        """Monitor system resource metrics."""
        while self.monitoring_active:
            try:
                # Get system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')

                # Network stats
                network = psutil.net_io_counters()
                network_sent_mb = network.bytes_sent / 1024 / 1024
                network_recv_mb = network.bytes_recv / 1024 / 1024

                # Process count
                process_count = len(psutil.pids())

                # Uptime
                uptime_hours = (time.time() - self.start_time) / 3600

                # Create metrics object
                metrics = SystemMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    memory_used_gb=memory.used / 1024**3,
                    memory_total_gb=memory.total / 1024**3,
                    disk_percent=disk.percent,
                    disk_used_gb=disk.used / 1024**3,
                    disk_total_gb=disk.total / 1024**3,
                    network_sent_mb=network_sent_mb,
                    network_recv_mb=network_recv_mb,
                    process_count=process_count,
                    uptime_hours=uptime_hours
                )

                # Store metrics
                self.system_metrics.append(metrics)

                # Log metrics periodically
                if len(self.system_metrics) % 10 == 0:  # Every 5 minutes
                    logger.info(f"📊 System: CPU {cpu_percent:.1f}%, "
                              f"Memory {memory.percent:.1f}%, "
                              f"Disk {disk.percent:.1f}%, "
                              f"Processes {process_count}")

                await asyncio.sleep(self.system_interval)

            except Exception as e:
                logger.error(f"Error monitoring system metrics: {e}")
                await asyncio.sleep(self.system_interval)

    async def _monitor_trading_metrics(self):
        """Monitor trading system specific metrics."""
        while self.monitoring_active:
            try:
                # Get trading metrics from components
                signals_per_minute = await self._calculate_signals_per_minute()
                execution_latency = await self._get_execution_latency()
                api_response_time = await self._get_api_response_time()
                active_positions = await self._get_active_positions()
                daily_pnl = await self._get_daily_pnl()
                win_rate_24h = await self._get_win_rate_24h()
                error_rate = await self._get_error_rate()
                websocket_connections = await self._get_websocket_connections()
                last_signal_time = await self._get_last_signal_time()
                last_execution_time = await self._get_last_execution_time()

                # Create trading metrics object
                metrics = TradingMetrics(
                    timestamp=time.time(),
                    signals_per_minute=signals_per_minute,
                    execution_latency_ms=execution_latency,
                    api_response_time_ms=api_response_time,
                    active_positions=active_positions,
                    daily_pnl=daily_pnl,
                    win_rate_24h=win_rate_24h,
                    error_rate_percent=error_rate,
                    websocket_connections=websocket_connections,
                    last_signal_time=last_signal_time,
                    last_execution_time=last_execution_time
                )

                # Store metrics
                self.trading_metrics.append(metrics)

                # Log trading metrics periodically
                if len(self.trading_metrics) % 6 == 0:  # Every minute
                    logger.info(f"📈 Trading: Signals {signals_per_minute:.1f}/min, "
                              f"Latency {execution_latency:.0f}ms, "
                              f"Positions {active_positions}, "
                              f"P&L ${daily_pnl:.2f}")

                await asyncio.sleep(self.trading_interval)

            except Exception as e:
                logger.error(f"Error monitoring trading metrics: {e}")
                await asyncio.sleep(self.trading_interval)

    async def _check_alerts(self):
        """Check for alert conditions."""
        while self.monitoring_active:
            try:
                current_time = time.time()

                # Check system alerts
                if self.system_metrics:
                    latest_system = self.system_metrics[-1]
                    await self._check_system_alerts(latest_system)

                # Check trading alerts
                if self.trading_metrics:
                    latest_trading = self.trading_metrics[-1]
                    await self._check_trading_alerts(latest_trading)

                await asyncio.sleep(self.alert_check_interval)

            except Exception as e:
                logger.error(f"Error checking alerts: {e}")
                await asyncio.sleep(self.alert_check_interval)

    async def _cleanup_old_metrics(self):
        """Clean up old metrics to prevent memory bloat."""
        while self.monitoring_active:
            try:
                current_time = time.time()
                cutoff_time = current_time - self.max_metrics_age

                # Clean system metrics
                self.system_metrics = [m for m in self.system_metrics if m.timestamp > cutoff_time]

                # Clean trading metrics
                self.trading_metrics = [m for m in self.trading_metrics if m.timestamp > cutoff_time]

                # Clean resolved alerts older than 1 hour
                alert_cutoff = current_time - 3600
                self.alerts = [a for a in self.alerts if not a.resolved or a.timestamp > alert_cutoff]

                # Run cleanup every hour
                await asyncio.sleep(3600)

            except Exception as e:
                logger.error(f"Error cleaning up metrics: {e}")
                await asyncio.sleep(3600)

    def get_health_score(self) -> Dict[str, Any]:
        """Calculate overall system health score (0-100)."""
        try:
            if not self.system_metrics or not self.trading_metrics:
                return {'score': 0, 'status': 'UNKNOWN', 'details': 'Insufficient data'}

            latest_system = self.system_metrics[-1]
            latest_trading = self.trading_metrics[-1]

            # Calculate component scores (0-100)
            cpu_score = max(0, 100 - latest_system.cpu_percent)
            memory_score = max(0, 100 - latest_system.memory_percent)
            disk_score = max(0, 100 - latest_system.disk_percent)

            # Trading performance scores
            latency_score = max(0, 100 - (latest_trading.execution_latency_ms / 20))  # 2000ms = 0 score
            error_score = max(0, 100 - (latest_trading.error_rate_percent * 20))  # 5% = 0 score

            # Signal freshness score
            signal_age = time.time() - latest_trading.last_signal_time
            signal_score = max(0, 100 - (signal_age / 120 * 100))  # 2 minutes = 0 score

            # Weighted overall score
            overall_score = (
                cpu_score * 0.15 +
                memory_score * 0.15 +
                disk_score * 0.10 +
                latency_score * 0.25 +
                error_score * 0.20 +
                signal_score * 0.15
            )

            # Determine status
            if overall_score >= 90:
                status = 'EXCELLENT'
            elif overall_score >= 75:
                status = 'GOOD'
            elif overall_score >= 50:
                status = 'WARNING'
            else:
                status = 'CRITICAL'

            return {
                'score': round(overall_score, 1),
                'status': status,
                'details': {
                    'cpu_score': round(cpu_score, 1),
                    'memory_score': round(memory_score, 1),
                    'disk_score': round(disk_score, 1),
                    'latency_score': round(latency_score, 1),
                    'error_score': round(error_score, 1),
                    'signal_score': round(signal_score, 1)
                }
            }

        except Exception as e:
            logger.error(f"Error calculating health score: {e}")
            return {'score': 0, 'status': 'ERROR', 'details': str(e)}

    # Helper methods for trading metrics
    async def _calculate_signals_per_minute(self) -> float:
        """Calculate signals per minute from data store."""
        try:
            if not self.data_store:
                return 0.0

            # Get signals from last minute
            current_time = time.time()
            one_minute_ago = current_time - 60

            signals = self.data_store.get_signals('DOGE/USDT:USDT', limit=100)
            recent_signals = [s for s in signals if s.get('timestamp', 0) > one_minute_ago]

            return len(recent_signals)

        except Exception as e:
            logger.debug(f"Error calculating signals per minute: {e}")
            return 0.0

    async def _get_execution_latency(self) -> float:
        """Get average execution latency in milliseconds."""
        try:
            if not self.execution_controller:
                return 0.0

            # Get recent execution times from execution controller
            # This would need to be implemented in execution controller
            return 1200.0  # Default placeholder

        except Exception as e:
            logger.debug(f"Error getting execution latency: {e}")
            return 0.0

    async def _get_api_response_time(self) -> float:
        """Get average API response time in milliseconds."""
        try:
            # Test API response time with a simple request
            start_time = time.time()

            if self.dashboard:
                # Test dashboard API
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get('http://localhost:8086/api/status', timeout=5) as response:
                            if response.status == 200:
                                end_time = time.time()
                                return (end_time - start_time) * 1000
                except:
                    pass

            return 500.0  # Default if no test possible

        except Exception as e:
            logger.debug(f"Error getting API response time: {e}")
            return 0.0

    async def _get_active_positions(self) -> int:
        """Get number of active positions."""
        try:
            if not self.execution_controller or not hasattr(self.execution_controller, 'account_tracker'):
                return 0

            account_tracker = self.execution_controller.account_tracker
            position_info = account_tracker.get_position_info()

            return 1 if position_info.get('has_position', False) else 0

        except Exception as e:
            logger.debug(f"Error getting active positions: {e}")
            return 0

    async def _get_daily_pnl(self) -> float:
        """Get daily P&L."""
        try:
            if not self.execution_controller or not hasattr(self.execution_controller, 'account_tracker'):
                return 0.0

            account_tracker = self.execution_controller.account_tracker
            summary = account_tracker.get_account_summary()

            return summary.get('daily_pnl', 0.0)

        except Exception as e:
            logger.debug(f"Error getting daily P&L: {e}")
            return 0.0

    async def _get_win_rate_24h(self) -> float:
        """Get win rate for last 24 hours."""
        try:
            if not self.data_store:
                return 0.0

            # Get signals from last 24 hours
            current_time = time.time()
            twenty_four_hours_ago = current_time - (24 * 3600)

            signals = self.data_store.get_signals('DOGE/USDT:USDT', limit=1000)
            recent_signals = [s for s in signals if s.get('timestamp', 0) > twenty_four_hours_ago]

            if not recent_signals:
                return 0.0

            profitable = [s for s in recent_signals if s.get('pnl', 0) > 0]
            return (len(profitable) / len(recent_signals)) * 100

        except Exception as e:
            logger.debug(f"Error getting win rate: {e}")
            return 0.0

    async def _get_error_rate(self) -> float:
        """Get error rate percentage."""
        try:
            # This would need to be tracked by the system
            # For now, return a low default
            return 1.0

        except Exception as e:
            logger.debug(f"Error getting error rate: {e}")
            return 0.0

    async def _get_websocket_connections(self) -> int:
        """Get number of active WebSocket connections."""
        try:
            if not self.dashboard or not hasattr(self.dashboard, 'websocket_clients'):
                return 0

            return len(self.dashboard.websocket_clients)

        except Exception as e:
            logger.debug(f"Error getting WebSocket connections: {e}")
            return 0

    async def _get_last_signal_time(self) -> float:
        """Get timestamp of last signal."""
        try:
            if not self.data_store:
                return 0.0

            signals = self.data_store.get_signals('DOGE/USDT:USDT', limit=1)
            if signals:
                return signals[0].get('timestamp', 0.0)

            return 0.0

        except Exception as e:
            logger.debug(f"Error getting last signal time: {e}")
            return 0.0

    async def _get_last_execution_time(self) -> float:
        """Get timestamp of last execution."""
        try:
            if not self.execution_controller:
                return 0.0

            # This would need to be tracked by execution controller
            return time.time() - 30  # Default placeholder

        except Exception as e:
            logger.debug(f"Error getting last execution time: {e}")
            return 0.0

    async def _check_system_alerts(self, metrics: SystemMetrics):
        """Check for system-level alerts."""
        current_time = time.time()

        # CPU alert
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            await self._create_alert(
                'high_cpu',
                'critical' if metrics.cpu_percent > 90 else 'warning',
                'system',
                f"High CPU usage: {metrics.cpu_percent:.1f}%",
                'cpu_percent',
                metrics.cpu_percent,
                self.thresholds['cpu_percent']
            )

        # Memory alert
        if metrics.memory_percent > self.thresholds['memory_percent']:
            await self._create_alert(
                'high_memory',
                'critical' if metrics.memory_percent > 95 else 'warning',
                'system',
                f"High memory usage: {metrics.memory_percent:.1f}%",
                'memory_percent',
                metrics.memory_percent,
                self.thresholds['memory_percent']
            )

        # Disk alert
        if metrics.disk_percent > self.thresholds['disk_percent']:
            await self._create_alert(
                'high_disk',
                'critical' if metrics.disk_percent > 95 else 'warning',
                'system',
                f"High disk usage: {metrics.disk_percent:.1f}%",
                'disk_percent',
                metrics.disk_percent,
                self.thresholds['disk_percent']
            )

    async def _check_trading_alerts(self, metrics: TradingMetrics):
        """Check for trading-level alerts."""
        current_time = time.time()

        # Execution latency alert
        if metrics.execution_latency_ms > self.thresholds['execution_latency_ms']:
            await self._create_alert(
                'high_latency',
                'warning',
                'trading',
                f"High execution latency: {metrics.execution_latency_ms:.0f}ms",
                'execution_latency_ms',
                metrics.execution_latency_ms,
                self.thresholds['execution_latency_ms']
            )

        # API response time alert
        if metrics.api_response_time_ms > self.thresholds['api_response_time_ms']:
            await self._create_alert(
                'slow_api',
                'warning',
                'trading',
                f"Slow API response: {metrics.api_response_time_ms:.0f}ms",
                'api_response_time_ms',
                metrics.api_response_time_ms,
                self.thresholds['api_response_time_ms']
            )

        # Signal gap alert
        signal_gap_minutes = (current_time - metrics.last_signal_time) / 60
        if signal_gap_minutes > self.thresholds['signal_gap_minutes']:
            await self._create_alert(
                'signal_gap',
                'warning',
                'trading',
                f"No signals for {signal_gap_minutes:.1f} minutes",
                'signal_gap_minutes',
                signal_gap_minutes,
                self.thresholds['signal_gap_minutes']
            )

        # Error rate alert
        if metrics.error_rate_percent > self.thresholds['error_rate_percent']:
            await self._create_alert(
                'high_errors',
                'critical',
                'trading',
                f"High error rate: {metrics.error_rate_percent:.1f}%",
                'error_rate_percent',
                metrics.error_rate_percent,
                self.thresholds['error_rate_percent']
            )

    async def _create_alert(self, alert_id: str, severity: str, category: str,
                          message: str, metric_name: str, current_value: float,
                          threshold_value: float):
        """Create a new alert if it doesn't already exist."""
        # Check if alert already exists and is not resolved
        existing_alert = next(
            (a for a in self.alerts if a.alert_id == alert_id and not a.resolved),
            None
        )

        if existing_alert:
            return  # Alert already exists

        # Create new alert
        alert = PerformanceAlert(
            alert_id=alert_id,
            timestamp=time.time(),
            severity=severity,
            category=category,
            message=message,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value
        )

        self.alerts.append(alert)

        # Log alert
        severity_icon = "🔴" if severity == 'critical' else "🟡" if severity == 'warning' else "🔵"
        logger.warning(f"{severity_icon} [ALERT] {message}")

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current system and trading metrics."""
        try:
            result = {
                'timestamp': time.time(),
                'system': None,
                'trading': None,
                'health': self.get_health_score(),
                'alerts': [asdict(a) for a in self.alerts if not a.resolved]
            }

            if self.system_metrics:
                result['system'] = asdict(self.system_metrics[-1])

            if self.trading_metrics:
                result['trading'] = asdict(self.trading_metrics[-1])

            return result

        except Exception as e:
            logger.error(f"Error getting current metrics: {e}")
            return {'error': str(e)}

    def export_metrics(self, format: str = 'json') -> str:
        """Export metrics in specified format."""
        try:
            data = {
                'system_metrics': [asdict(m) for m in self.system_metrics[-100:]],  # Last 100
                'trading_metrics': [asdict(m) for m in self.trading_metrics[-100:]],  # Last 100
                'alerts': [asdict(a) for a in self.alerts],
                'health': self.get_health_score(),
                'export_timestamp': time.time()
            }

            if format == 'json':
                return json.dumps(data, indent=2)
            else:
                return str(data)

        except Exception as e:
            logger.error(f"Error exporting metrics: {e}")
            return f"Error: {e}"
