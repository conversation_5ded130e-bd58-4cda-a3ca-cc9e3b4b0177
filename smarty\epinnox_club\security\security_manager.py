"""
Security Manager
Enterprise-grade security hardening for Money Circle production deployment
"""

import hashlib
import hmac
import secrets
import time
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from aiohttp import web
import bcrypt
import jwt

logger = logging.getLogger(__name__)

class SecurityManager:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rate_limits = {}
        self.failed_attempts = {}
        self.blocked_ips = set()
        self.session_tokens = {}

        # Security configuration
        self.max_login_attempts = config.get('max_login_attempts', 5)
        self.lockout_duration = config.get('lockout_duration', 900)  # 15 minutes
        self.rate_limit_requests = config.get('rate_limit_requests', 100)
        self.rate_limit_window = config.get('rate_limit_window', 3600)  # 1 hour
        self.session_timeout = config.get('session_timeout', 3600)  # 1 hour

        # JWT configuration
        self.jwt_secret = config.get('jwt_secret', secrets.token_urlsafe(32))
        self.jwt_algorithm = 'HS256'

        logger.info("[SECURITY] Security Manager initialized with enterprise-grade protection")

    async def rate_limit_middleware(self, request: web.Request, handler):
        """Rate limiting middleware to prevent abuse."""
        client_ip = self._get_client_ip(request)

        # Skip rate limiting for localhost in development
        if self._is_localhost(client_ip):
            logger.debug(f"[SECURITY] Skipping rate limit for localhost: {client_ip}")
            return await handler(request)

        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            logger.warning(f"[SECURITY] Blocked IP attempted access: {client_ip}")
            return web.json_response(
                {'error': 'Access denied. IP temporarily blocked.'},
                status=429
            )

        # Check rate limits
        current_time = time.time()
        if client_ip not in self.rate_limits:
            self.rate_limits[client_ip] = []

        # Clean old requests
        self.rate_limits[client_ip] = [
            req_time for req_time in self.rate_limits[client_ip]
            if current_time - req_time < self.rate_limit_window
        ]

        # Check if rate limit exceeded
        if len(self.rate_limits[client_ip]) >= self.rate_limit_requests:
            logger.warning(f"[SECURITY] Rate limit exceeded for IP: {client_ip}")
            self.blocked_ips.add(client_ip)
            return web.json_response(
                {'error': 'Rate limit exceeded. Please try again later.'},
                status=429
            )

        # Add current request
        self.rate_limits[client_ip].append(current_time)

        # Continue with request
        return await handler(request)

    async def authentication_middleware(self, request: web.Request, handler):
        """Enhanced authentication middleware with security features."""
        # Skip authentication for public routes
        public_routes = ['/login', '/register', '/static', '/favicon.ico', '/api/csrf-token', '/health']
        if any(request.path.startswith(route) for route in public_routes):
            return await handler(request)

        # Check for valid session
        session_token = request.cookies.get('session_token')
        if not session_token:
            return web.Response(status=302, headers={'Location': '/login'})

        # Validate session token
        user_data = self._validate_session_token(session_token)
        if not user_data:
            # Invalid or expired session
            response = web.Response(status=302, headers={'Location': '/login'})
            response.del_cookie('session_token')
            return response

        # Add user data to request
        request['user'] = user_data

        # Log access for audit trail
        self._log_access(request, user_data)

        return await handler(request)

    async def csrf_protection_middleware(self, request: web.Request, handler):
        """Enhanced CSRF protection middleware with smart exemptions."""
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:

            # Define routes that are exempt from CSRF protection
            csrf_exempt_routes = [
                '/api/csrf-token',  # CSRF token endpoint itself
                '/ws',  # WebSocket endpoints
                '/ws/',
                '/health',  # Health check endpoints
            ]

            # Check if route is exempt
            path = request.path
            if any(path.startswith(route) for route in csrf_exempt_routes):
                return await handler(request)

            # For authenticated API requests, check if user has valid session
            if path.startswith('/api/'):
                # Check for valid session first
                user = request.get('user')
                session_id = request.cookies.get('session_id')

                if user and session_id:
                    # User is authenticated via session, allow API requests
                    logger.debug(f"[SECURITY] Allowing authenticated API request to {path}")
                    return await handler(request)

            # For form submissions and non-authenticated requests, require CSRF token
            csrf_token = request.headers.get('X-CSRF-Token')

            # If no header token, try to get from form data (for form submissions)
            if not csrf_token:
                try:
                    # Only read form data for non-JSON requests
                    content_type = request.headers.get('Content-Type', '')
                    if 'application/x-www-form-urlencoded' in content_type or 'multipart/form-data' in content_type:
                        form_data = await request.post()
                        csrf_token = form_data.get('csrf_token')
                except Exception:
                    pass  # Ignore form parsing errors

            if not csrf_token:
                logger.warning(f"[SECURITY] Missing CSRF token from {self._get_client_ip(request)} for {path}")

                # Return appropriate error based on request type
                if path.startswith('/api/'):
                    return web.json_response({'error': 'CSRF token required'}, status=403)
                else:
                    # For form submissions, redirect with error
                    return web.Response(status=302, headers={'Location': f'{path}?error=csrf_error'})

            # Validate CSRF token
            if not self._validate_csrf_token(csrf_token, request):
                logger.warning(f"[SECURITY] Invalid CSRF token from {self._get_client_ip(request)} for {path}")

                if path.startswith('/api/'):
                    return web.json_response({'error': 'Invalid CSRF token'}, status=403)
                else:
                    return web.Response(status=302, headers={'Location': f'{path}?error=csrf_error'})

        return await handler(request)

    async def security_headers_middleware(self, request: web.Request, handler):
        """Add security headers to all responses."""
        response = await handler(request)

        # Security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://fonts.gstatic.com",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }

        for header, value in security_headers.items():
            response.headers[header] = value

        return response

    def validate_login_attempt(self, username: str, client_ip: str) -> bool:
        """Validate if login attempt is allowed (not rate limited)."""
        current_time = time.time()
        key = f"{username}:{client_ip}"

        if key not in self.failed_attempts:
            return True

        attempts = self.failed_attempts[key]

        # Clean old attempts
        attempts['timestamps'] = [
            timestamp for timestamp in attempts['timestamps']
            if current_time - timestamp < self.lockout_duration
        ]

        # Check if account is locked
        if len(attempts['timestamps']) >= self.max_login_attempts:
            logger.warning(f"[SECURITY] Account locked for {username} from {client_ip}")
            return False

        return True

    def record_failed_login(self, username: str, client_ip: str):
        """Record failed login attempt."""
        current_time = time.time()
        key = f"{username}:{client_ip}"

        if key not in self.failed_attempts:
            self.failed_attempts[key] = {'timestamps': []}

        self.failed_attempts[key]['timestamps'].append(current_time)

        logger.warning(f"[SECURITY] Failed login attempt for {username} from {client_ip}")

    def clear_failed_attempts(self, username: str, client_ip: str):
        """Clear failed login attempts after successful login."""
        key = f"{username}:{client_ip}"
        if key in self.failed_attempts:
            del self.failed_attempts[key]

    def create_session_token(self, user_data: Dict[str, Any]) -> str:
        """Create secure session token."""
        payload = {
            'user_id': user_data['user_id'],
            'username': user_data['username'],
            'role': user_data['role'],
            'iat': time.time(),
            'exp': time.time() + self.session_timeout
        }

        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)

        # Store session for validation
        self.session_tokens[token] = {
            'user_data': user_data,
            'created_at': time.time(),
            'last_access': time.time()
        }

        return token

    def _validate_session_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate session token."""
        try:
            # Decode JWT token
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])

            # Check if token exists in session store
            if token not in self.session_tokens:
                return None

            session_data = self.session_tokens[token]
            current_time = time.time()

            # Check if session expired
            if current_time - session_data['last_access'] > self.session_timeout:
                del self.session_tokens[token]
                return None

            # Update last access time
            session_data['last_access'] = current_time

            return session_data['user_data']

        except jwt.ExpiredSignatureError:
            logger.warning("[SECURITY] Expired JWT token")
            return None
        except jwt.InvalidTokenError:
            logger.warning("[SECURITY] Invalid JWT token")
            return None

    def generate_csrf_token(self, session_token: str) -> str:
        """Generate CSRF token for session."""
        return hmac.new(
            self.jwt_secret.encode(),
            f"{session_token}:{time.time()}".encode(),
            hashlib.sha256
        ).hexdigest()

    def _validate_csrf_token(self, token: str, request: web.Request) -> bool:
        """Validate CSRF token."""
        # For login forms, users don't have sessions yet, so we validate the token format
        # Check if token looks like a valid HMAC-SHA256 hex string
        if not token:
            return False

        # HMAC-SHA256 produces 64 character hex strings
        if len(token) != 64:
            return False

        # Check if all characters are valid hex
        try:
            int(token, 16)  # This will raise ValueError if not valid hex
            return True
        except ValueError:
            return False

    def _get_client_ip(self, request: web.Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers (for reverse proxy setups)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()

        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip

        return request.remote

    def _is_localhost(self, ip: str) -> bool:
        """Check if IP is localhost."""
        localhost_ips = [
            '127.0.0.1',
            '::1',
            'localhost',
            '0.0.0.0'
        ]
        return ip in localhost_ips

    def _log_access(self, request: web.Request, user_data: Dict[str, Any]):
        """Log access for audit trail."""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_id': user_data['user_id'],
            'username': user_data['username'],
            'role': user_data['role'],
            'ip_address': self._get_client_ip(request),
            'method': request.method,
            'path': request.path,
            'user_agent': request.headers.get('User-Agent', 'Unknown')
        }

        # Log to security audit file
        logger.info(f"[AUDIT] {json.dumps(log_entry)}")

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        salt = bcrypt.gensalt(rounds=12)
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash."""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

    def sanitize_input(self, input_data: Any) -> Any:
        """Sanitize user input to prevent injection attacks."""
        if isinstance(input_data, str):
            # Basic XSS prevention
            dangerous_chars = ['<', '>', '"', "'", '&', 'javascript:', 'data:']
            for char in dangerous_chars:
                input_data = input_data.replace(char, '')
            return input_data.strip()
        elif isinstance(input_data, dict):
            return {key: self.sanitize_input(value) for key, value in input_data.items()}
        elif isinstance(input_data, list):
            return [self.sanitize_input(item) for item in input_data]
        else:
            return input_data

    async def cleanup_expired_sessions(self):
        """Clean up expired sessions periodically."""
        current_time = time.time()
        expired_tokens = []

        for token, session_data in self.session_tokens.items():
            if current_time - session_data['last_access'] > self.session_timeout:
                expired_tokens.append(token)

        for token in expired_tokens:
            del self.session_tokens[token]

        if expired_tokens:
            logger.info(f"[SECURITY] Cleaned up {len(expired_tokens)} expired sessions")

    def get_security_metrics(self) -> Dict[str, Any]:
        """Get security metrics for monitoring."""
        current_time = time.time()

        # Count active sessions
        active_sessions = sum(
            1 for session_data in self.session_tokens.values()
            if current_time - session_data['last_access'] < self.session_timeout
        )

        # Count blocked IPs
        blocked_ips_count = len(self.blocked_ips)

        # Count failed attempts in last hour
        recent_failed_attempts = 0
        for attempts in self.failed_attempts.values():
            recent_failed_attempts += len([
                timestamp for timestamp in attempts['timestamps']
                if current_time - timestamp < 3600
            ])

        return {
            'active_sessions': active_sessions,
            'blocked_ips': blocked_ips_count,
            'recent_failed_attempts': recent_failed_attempts,
            'total_sessions': len(self.session_tokens),
            'rate_limited_ips': len(self.rate_limits)
        }
