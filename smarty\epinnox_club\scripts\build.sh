#!/usr/bin/env bash
# Render Build Script for Money Circle Investment Club Platform

set -o errexit  # Exit on error

echo "🏗️ Building Money Circle Investment Club Platform..."

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

echo "🗄️ Setting up database..."
# Database setup will be handled by the application on startup
# since Render provides DATABASE_URL automatically

echo "📁 Creating necessary directories..."
mkdir -p data
mkdir -p logs
mkdir -p static/uploads

echo "🔧 Setting proper permissions..."
chmod 755 data
chmod 755 logs
chmod 755 static/uploads

echo "✅ Build completed successfully!"
echo "🚀 Ready to start Money Circle platform..."
