#!/usr/bin/env python3
"""
Component Testing Script for Epinnox V6
Tests individual components and end-to-end pipeline
"""

import asyncio
import logging
import sys
import time
import random
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from feeds.trade_parser import TradeParser, MarketFeatures
from storage.live_store import LiveDataStore
from models.smart_strategy import SmartStrategy
from models.llm_integration import LLMIntegration
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

@dataclass
class TestTradeData:
    """Test trade data structure."""
    symbol: str
    price: float
    quantity: float
    side: str
    timestamp: int
    trade_id: str

class ComponentTester:
    """
    Comprehensive component testing for Epinnox V6.
    Tests each component individually and the complete pipeline.
    """

    def __init__(self):
        self.config = self._load_test_config()
        self.components = {}

        logger.info("🧪 Component Tester initialized")

    def _load_test_config(self) -> Dict[str, Any]:
        """Load test configuration."""
        return {
            'symbols': {
                'enabled': ['BTC-USDT', 'ETH-USDT', 'DOGE-USDT'],
                'default': 'BTC-USDT'
            },
            'data_storage': {
                'trade_window_size': 500,
                'feature_update_interval': 2
            },
            'models': {
                'weights': {
                    'rsi': 1.2,
                    'vwap': 1.0,
                    'orderflow': 1.5,
                    'volatility': 1.1
                },
                'rsi': {
                    'period': 14,
                    'overbought': 70,
                    'oversold': 30
                },
                'vwap': {
                    'deviation_threshold': 0.02
                },
                'orderflow': {
                    'imbalance_threshold': 0.3
                },
                'volatility': {
                    'threshold_high': 0.05,
                    'threshold_low': 0.01
                }
            },
            'signals': {
                'confidence_threshold': 0.6,
                'signal_cooldown': 30,
                'thresholds': {
                    'strong_buy': 0.8,
                    'buy': 0.4,
                    'sell': -0.4,
                    'strong_sell': -0.8
                }
            },
            'llm': {
                'enabled': True,
                'api_url': 'http://localhost:1234/v1/chat/completions',
                'call_interval': 60,
                'max_tokens': 150,
                'temperature': 0.1,
                'timeout': 10,
                'prompt': {
                    'system': 'You are an expert trading analyst. Analyze market data and provide trading recommendations.'
                }
            },
            'dashboard': {
                'host': 'localhost',
                'port': 8087,  # Different port for testing
                'update_interval': 2
            },
            'data_storage': {
                'persist_state': False,
                'memory_cleanup_interval': 300
            }
        }

    def generate_test_trade_data(self, symbol: str, base_price: float = 50000) -> TestTradeData:
        """Generate realistic test trade data."""
        price_variation = random.uniform(-0.01, 0.01)  # ±1% variation
        price = base_price * (1 + price_variation)

        quantity = random.uniform(0.001, 1.0)
        side = random.choice(['buy', 'sell'])
        timestamp = int(time.time() * 1000)
        trade_id = f"test_{timestamp}_{random.randint(1000, 9999)}"

        return TestTradeData(
            symbol=symbol,
            price=price,
            quantity=quantity,
            side=side,
            timestamp=timestamp,
            trade_id=trade_id
        )

    async def test_data_store(self) -> bool:
        """Test LiveDataStore component."""
        logger.info("🧪 Testing LiveDataStore...")

        try:
            # Initialize data store
            data_store = LiveDataStore(self.config)
            self.components['data_store'] = data_store

            # Test feature storage
            test_features = MarketFeatures(
                symbol='BTC-USDT',
                timestamp=int(time.time() * 1000),
                last_price=50000.0,
                price_change_1m=0.01,
                price_velocity=0.001,
                volume_1m=100.0,
                buy_volume_ratio=0.6,
                volume_delta=20.0,
                order_flow_imbalance=0.2,
                trade_intensity=5.0,
                avg_trade_size=0.5,
                rsi=65.0,
                vwap=49800.0,
                volatility=0.02
            )

            data_store.store_features(test_features)

            # Test signal storage
            test_signal = {
                'symbol': 'BTC-USDT',
                'action': 'LONG',
                'confidence': 0.75,
                'score': 0.8,
                'reasoning': 'Test signal',
                'timestamp': time.time(),
                'pnl': 0.0
            }

            data_store.store_signal(test_signal)

            # Test data retrieval
            stored_features = data_store.get_features('BTC-USDT')
            stored_signals = data_store.get_signals('BTC-USDT')

            assert stored_features is not None, "Features not stored properly"
            assert len(stored_signals) > 0, "Signals not stored properly"

            logger.info("✅ LiveDataStore test passed")
            return True

        except Exception as e:
            logger.error(f"❌ LiveDataStore test failed: {e}")
            return False

    async def test_trade_parser(self) -> bool:
        """Test TradeParser component."""
        logger.info("🧪 Testing TradeParser...")

        try:
            # Initialize trade parser
            trade_parser = TradeParser(self.config)
            self.components['trade_parser'] = trade_parser

            # Generate test trades
            symbol = 'BTC-USDT'
            base_price = 50000.0

            for i in range(20):
                test_trade = self.generate_test_trade_data(symbol, base_price + i * 10)

                trade_data = {
                    'symbol': test_trade.symbol,
                    'price': test_trade.price,
                    'quantity': test_trade.quantity,
                    'side': test_trade.side,
                    'timestamp': test_trade.timestamp
                }

                features = await trade_parser.process_trade(trade_data)

                if features and i >= 15:  # After enough data for calculations
                    assert features.symbol == symbol, "Symbol mismatch"
                    assert features.last_price > 0, "Invalid price"
                    assert features.volume_1m >= 0, "Invalid volume"

                    logger.info(f"📊 Features: price={features.last_price:.2f}, rsi={features.rsi:.1f if features.rsi else 'N/A'}")

            logger.info("✅ TradeParser test passed")
            return True

        except Exception as e:
            logger.error(f"❌ TradeParser test failed: {e}")
            return False

    async def test_strategy_engine(self) -> bool:
        """Test SmartStrategy component."""
        logger.info("🧪 Testing SmartStrategy...")

        try:
            # Use data store from previous test
            data_store = self.components.get('data_store')
            if not data_store:
                data_store = LiveDataStore(self.config)

            # Initialize strategy engine
            strategy_engine = SmartStrategy(self.config, data_store)
            self.components['strategy_engine'] = strategy_engine

            # Create test market features
            test_features = MarketFeatures(
                symbol='BTC-USDT',
                timestamp=int(time.time() * 1000),
                last_price=50000.0,
                price_change_1m=0.01,
                price_velocity=0.001,
                volume_1m=100.0,
                buy_volume_ratio=0.6,
                volume_delta=20.0,
                order_flow_imbalance=0.4,  # Strong buy pressure
                trade_intensity=5.0,
                avg_trade_size=0.5,
                rsi=25.0,  # Oversold
                vwap=50500.0,  # Price below VWAP
                volatility=0.02
            )

            # Process features through strategy
            signal = await strategy_engine.process_features(test_features)

            if signal:
                assert signal.symbol == 'BTC-USDT', "Symbol mismatch"
                assert signal.action in ['LONG', 'SHORT', 'WAIT'], "Invalid action"
                assert 0 <= signal.confidence <= 1, "Invalid confidence"

                logger.info(f"📈 Signal: {signal.action} (confidence: {signal.confidence:.2%})")
                logger.info(f"🧠 Reasoning: {signal.reasoning}")
            else:
                logger.info("📊 No signal generated (within cooldown or low confidence)")

            logger.info("✅ SmartStrategy test passed")
            return True

        except Exception as e:
            logger.error(f"❌ SmartStrategy test failed: {e}")
            return False

    async def test_llm_integration(self) -> bool:
        """Test LLMIntegration component."""
        logger.info("🧪 Testing LLMIntegration...")

        try:
            # Use data store from previous test
            data_store = self.components.get('data_store')
            if not data_store:
                data_store = LiveDataStore(self.config)

            # Initialize LLM integration
            llm_integration = LLMIntegration(self.config, data_store)
            self.components['llm_integration'] = llm_integration

            # Create test signal and features
            from models.smart_strategy import TradingSignal

            test_signal = TradingSignal(
                symbol='BTC-USDT',
                action='LONG',
                confidence=0.75,
                score=0.8,
                reasoning='RSI oversold + strong buy pressure',
                model_contributions={'rsi': 0.8, 'orderflow': 0.6},
                timestamp=time.time(),
                price=50000.0
            )

            test_features = MarketFeatures(
                symbol='BTC-USDT',
                timestamp=int(time.time() * 1000),
                last_price=50000.0,
                price_change_1m=0.01,
                price_velocity=0.001,
                volume_1m=100.0,
                buy_volume_ratio=0.6,
                volume_delta=20.0,
                order_flow_imbalance=0.4,
                trade_intensity=5.0,
                avg_trade_size=0.5,
                rsi=25.0,
                vwap=50500.0,
                volatility=0.02
            )

            # Test LLM processing (will fail if LMStudio not running, but that's expected)
            decision = await llm_integration.process_signal(test_signal, test_features)

            if decision:
                logger.info(f"🧠 LLM Decision: {decision.action} (confidence: {decision.confidence:.2%})")
                logger.info(f"💭 LLM Reasoning: {decision.reasoning}")
            else:
                logger.info("🔄 LLM processing skipped (API not available or cooldown)")

            logger.info("✅ LLMIntegration test passed")
            return True

        except Exception as e:
            logger.error(f"❌ LLMIntegration test failed: {e}")
            return False

    async def test_dashboard(self) -> bool:
        """Test AIStrategyTunerDashboard component."""
        logger.info("🧪 Testing Dashboard...")

        try:
            # Use data store from previous test
            data_store = self.components.get('data_store')
            if not data_store:
                data_store = LiveDataStore(self.config)

            # Initialize dashboard
            dashboard = AIStrategyTunerDashboard(self.config, data_store)
            self.components['dashboard'] = dashboard

            # Test dashboard data retrieval
            dashboard_data = data_store.get_dashboard_data('BTC-USDT')

            assert 'timestamp' in dashboard_data, "Missing timestamp"
            assert 'current_symbol' in dashboard_data, "Missing current_symbol"

            logger.info(f"🌐 Dashboard data keys: {list(dashboard_data.keys())}")

            # Start dashboard server for testing
            logger.info("🌐 Starting test dashboard server...")
            await dashboard.start_server('localhost', 8087)

            logger.info("✅ Dashboard test passed")
            logger.info("🌐 Test dashboard available at: http://localhost:8087")
            return True

        except Exception as e:
            logger.error(f"❌ Dashboard test failed: {e}")
            return False

    async def test_end_to_end_pipeline(self) -> bool:
        """Test complete end-to-end pipeline."""
        logger.info("🧪 Testing End-to-End Pipeline...")

        try:
            # Get components
            data_store = self.components.get('data_store')
            trade_parser = self.components.get('trade_parser')
            strategy_engine = self.components.get('strategy_engine')

            if not all([data_store, trade_parser, strategy_engine]):
                logger.error("❌ Required components not available")
                return False

            # Simulate real-time trading pipeline
            symbol = 'BTC-USDT'
            base_price = 50000.0

            logger.info("🔄 Simulating real-time trading pipeline...")

            for i in range(10):
                # Generate trade data
                test_trade = self.generate_test_trade_data(symbol, base_price + i * 50)

                trade_data = {
                    'symbol': test_trade.symbol,
                    'price': test_trade.price,
                    'quantity': test_trade.quantity,
                    'side': test_trade.side,
                    'timestamp': test_trade.timestamp
                }

                # Process through pipeline
                features = await trade_parser.process_trade(trade_data)

                if features:
                    # Store features
                    data_store.store_features(features)

                    # Process through strategy
                    signal = await strategy_engine.process_features(features)

                    if signal:
                        logger.info(f"📈 Pipeline Signal {i+1}: {signal.action} @ {signal.price:.2f}")

                    # Small delay to simulate real-time
                    await asyncio.sleep(0.1)

            # Check final state
            final_features = data_store.get_features(symbol)
            final_signals = data_store.get_signals(symbol)

            assert final_features is not None, "No final features"
            assert len(final_signals) >= 0, "Signal storage issue"

            logger.info(f"📊 Pipeline completed: {len(final_signals)} signals generated")
            logger.info("✅ End-to-End Pipeline test passed")
            return True

        except Exception as e:
            logger.error(f"❌ End-to-End Pipeline test failed: {e}")
            return False

    async def run_all_tests(self) -> bool:
        """Run all component tests."""
        logger.info("🚀 Starting Comprehensive Component Testing")
        logger.info("=" * 60)

        tests = [
            ("Data Store", self.test_data_store),
            ("Trade Parser", self.test_trade_parser),
            ("Strategy Engine", self.test_strategy_engine),
            ("LLM Integration", self.test_llm_integration),
            ("Dashboard", self.test_dashboard),
            ("End-to-End Pipeline", self.test_end_to_end_pipeline)
        ]

        results = {}

        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running {test_name} test...")
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"{status}: {test_name}")
            except Exception as e:
                results[test_name] = False
                logger.error(f"❌ FAILED: {test_name} - {e}")

        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("🧪 TEST RESULTS SUMMARY")
        logger.info("=" * 60)

        passed = sum(results.values())
        total = len(results)

        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")

        logger.info("=" * 60)
        logger.info(f"📊 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

        if passed == total:
            logger.info("🎉 All tests passed! Epinnox V6 components are working correctly.")
        else:
            logger.warning(f"⚠️ {total-passed} test(s) failed. Check logs for details.")

        return passed == total

async def main():
    """Main testing entry point."""
    tester = ComponentTester()
    success = await tester.run_all_tests()

    if success:
        logger.info("\n🎯 Component testing completed successfully!")
        logger.info("🌐 Test dashboard running at: http://localhost:8087")
        logger.info("🔄 Keeping dashboard running for manual testing...")

        # Keep running for manual testing
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Testing stopped by user")
    else:
        logger.error("\n❌ Component testing failed!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"❌ Fatal testing error: {e}")
        sys.exit(1)
