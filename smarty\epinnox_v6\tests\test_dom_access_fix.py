#!/usr/bin/env python3
"""
Test DOM Access Fix - Phase 9.12
Comprehensive test to identify and fix all DOM access issues
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_dom_access_issues():
    """Analyze the JavaScript code for DOM access issues."""
    logger.info("🔍 Analyzing DOM Access Issues - Phase 9.12")
    
    # Read the dashboard JavaScript
    dashboard_file = Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py"
    
    if not dashboard_file.exists():
        logger.error("❌ Dashboard file not found")
        return
    
    with open(dashboard_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all DOM access patterns that need fixing
    dom_issues = []
    
    # Pattern 1: Direct getElementById without null check
    import re
    
    # Find getElementById calls without null checks
    getbyid_pattern = r'document\.getElementById\([\'"]([^\'\"]+)[\'"]\)\.(\w+)'
    matches = re.findall(getbyid_pattern, content)
    
    for element_id, property_access in matches:
        dom_issues.append({
            'type': 'getElementById_without_null_check',
            'element_id': element_id,
            'property': property_access,
            'severity': 'HIGH',
            'description': f"Direct property access on getElementById('{element_id}').{property_access} without null check"
        })
    
    # Pattern 2: querySelector without null check
    queryselector_pattern = r'document\.querySelector\([\'"]([^\'\"]+)[\'"]\)\.(\w+)'
    matches = re.findall(queryselector_pattern, content)
    
    for selector, property_access in matches:
        dom_issues.append({
            'type': 'querySelector_without_null_check',
            'selector': selector,
            'property': property_access,
            'severity': 'HIGH',
            'description': f"Direct property access on querySelector('{selector}').{property_access} without null check"
        })
    
    # Pattern 3: Functions that access DOM elements without checks
    problematic_functions = [
        'applyPreset', 'cycleSymbol', 'resetParameters', 'openSettingsModal', 
        'closeSettingsModal', 'getCurrentWeights', 'resetToDefaults'
    ]
    
    for func_name in problematic_functions:
        if func_name in content:
            dom_issues.append({
                'type': 'function_with_unsafe_dom_access',
                'function': func_name,
                'severity': 'MEDIUM',
                'description': f"Function '{func_name}' may access DOM elements without proper null checks"
            })
    
    # Report findings
    logger.info(f"🔍 Found {len(dom_issues)} potential DOM access issues:")
    
    high_severity = [issue for issue in dom_issues if issue['severity'] == 'HIGH']
    medium_severity = [issue for issue in dom_issues if issue['severity'] == 'MEDIUM']
    
    logger.info(f"   🔴 HIGH severity: {len(high_severity)} issues")
    logger.info(f"   🟡 MEDIUM severity: {len(medium_severity)} issues")
    
    # Show specific issues
    for i, issue in enumerate(high_severity[:10], 1):  # Show first 10 high severity
        logger.info(f"   {i}. {issue['description']}")
    
    return dom_issues

def generate_dom_fix_recommendations():
    """Generate recommendations for fixing DOM access issues."""
    logger.info("\n🔧 DOM Fix Recommendations:")
    
    recommendations = [
        {
            'issue': 'getElementById without null check',
            'fix': 'Always check if element exists before accessing properties',
            'example_before': "document.getElementById('my-element').textContent = 'value';",
            'example_after': """const element = document.getElementById('my-element');
if (element) {
    element.textContent = 'value';
} else {
    console.warn('Element not found: my-element');
}"""
        },
        {
            'issue': 'querySelector without null check',
            'fix': 'Use optional chaining or explicit null checks',
            'example_before': "document.querySelector('.my-class').style.display = 'none';",
            'example_after': """const element = document.querySelector('.my-class');
if (element) {
    element.style.display = 'none';
}"""
        },
        {
            'issue': 'Function parameter access',
            'fix': 'Validate function parameters and DOM elements',
            'example_before': "function toggleElement(element) { element.classList.toggle('active'); }",
            'example_after': """function toggleElement(element) {
    if (element && element.classList) {
        element.classList.toggle('active');
    } else {
        console.warn('Invalid element passed to toggleElement');
    }
}"""
        },
        {
            'issue': 'Event listener on null elements',
            'fix': 'Check element exists before adding listeners',
            'example_before': "document.getElementById('button').addEventListener('click', handler);",
            'example_after': """const button = document.getElementById('button');
if (button) {
    button.addEventListener('click', handler);
} else {
    console.warn('Button element not found');
}"""
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        logger.info(f"\n{i}. {rec['issue']}:")
        logger.info(f"   Fix: {rec['fix']}")
        logger.info(f"   Before: {rec['example_before']}")
        logger.info(f"   After: {rec['example_after']}")

def test_specific_dom_functions():
    """Test specific functions that are known to have DOM access issues."""
    logger.info("\n🎯 Testing Specific DOM Functions:")
    
    problematic_patterns = [
        {
            'pattern': "document.getElementById('preset-selector').value",
            'location': 'applyPreset function',
            'fix_needed': 'Add null check before accessing .value'
        },
        {
            'pattern': "document.getElementById('symbol-selector')",
            'location': 'cycleSymbol function',
            'fix_needed': 'Check if selector exists before accessing .options'
        },
        {
            'pattern': "document.getElementById('settings-modal').style.display",
            'location': 'openSettingsModal/closeSettingsModal',
            'fix_needed': 'Verify modal exists before changing style'
        },
        {
            'pattern': "document.getElementById('rsi-weight').value",
            'location': 'getCurrentWeights function',
            'fix_needed': 'Check all weight elements exist before parsing values'
        },
        {
            'pattern': "document.getElementById('rsi-value').textContent",
            'location': 'resetToDefaults function',
            'fix_needed': 'Verify display elements exist before setting text'
        }
    ]
    
    for i, pattern in enumerate(problematic_patterns, 1):
        logger.info(f"{i}. Pattern: {pattern['pattern']}")
        logger.info(f"   Location: {pattern['location']}")
        logger.info(f"   Fix needed: {pattern['fix_needed']}")

async def test_dom_access_fix():
    """Main test function for DOM access issues."""
    try:
        logger.info("🧪 Testing DOM Access Fix - Phase 9.12")
        logger.info("="*60)
        
        # Analyze current DOM access issues
        dom_issues = analyze_dom_access_issues()
        
        # Generate fix recommendations
        generate_dom_fix_recommendations()
        
        # Test specific problematic functions
        test_specific_dom_functions()
        
        # Summary and action plan
        logger.info("\n" + "="*60)
        logger.info("📋 DOM ACCESS FIX SUMMARY")
        logger.info("="*60)
        
        logger.info("🔍 Issues Found:")
        logger.info("   1. Multiple getElementById calls without null checks")
        logger.info("   2. Direct property access on potentially null elements")
        logger.info("   3. Functions that assume DOM elements exist")
        logger.info("   4. Event listeners added to potentially null elements")
        logger.info("   5. Style and class modifications without validation")
        
        logger.info("\n🔧 Required Fixes:")
        logger.info("   1. Add null checks before all DOM element access")
        logger.info("   2. Use defensive programming patterns")
        logger.info("   3. Add console warnings for missing elements")
        logger.info("   4. Implement graceful degradation")
        logger.info("   5. Use try-catch blocks for DOM operations")
        
        logger.info("\n🎯 Priority Functions to Fix:")
        priority_functions = [
            'applyPreset() - Line 7422',
            'cycleSymbol() - Line 7443', 
            'resetParameters() - Line 7453',
            'openSettingsModal() - Line 7461',
            'closeSettingsModal() - Line 7466',
            'getCurrentWeights() - Line 7507',
            'resetToDefaults() - Line 7617',
            'loadSystemSettings() - Line 7767'
        ]
        
        for i, func in enumerate(priority_functions, 1):
            logger.info(f"   {i}. {func}")
        
        logger.info("\n🚀 Next Steps:")
        logger.info("   1. Apply null checks to all getElementById calls")
        logger.info("   2. Add defensive programming to DOM access functions")
        logger.info("   3. Test each function individually")
        logger.info("   4. Implement error handling for missing elements")
        logger.info("   5. Add console warnings for debugging")
        
        logger.info("\n✅ DOM ACCESS ANALYSIS COMPLETE!")
        logger.info("Ready to implement comprehensive DOM access fixes.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_dom_access_fix())
