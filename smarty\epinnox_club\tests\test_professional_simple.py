#!/usr/bin/env python3
"""
Simple Professional Trading Platform Test
Tests the new professional trading features using requests library.
"""

import requests
import json
import time
from datetime import datetime

class SimpleTradingTester:
    def __init__(self, base_url="http://localhost:8086"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def run_comprehensive_test(self):
        """Run comprehensive test of professional trading platform."""
        print("🚀 PROFESSIONAL TRADING PLATFORM TEST SUITE")
        print("=" * 60)
        
        # Test 1: Platform Health Check
        self.test_platform_health()
        
        # Test 2: Authentication
        self.test_authentication()
        
        # Test 3: Professional UI Loading
        self.test_professional_ui()
        
        # Test 4: Market Data Integration
        self.test_market_data()
        
        # Test 5: HTX API Integration
        self.test_htx_integration()
        
        # Test 6: TradingView Integration
        self.test_tradingview_integration()
        
        print("\n" + "=" * 60)
        print("✅ PROFESSIONAL TRADING PLATFORM TEST COMPLETE")
        
    def test_platform_health(self):
        """Test platform health and availability."""
        print("\n📊 Testing Platform Health...")
        
        try:
            resp = self.session.get(f"{self.base_url}/health", timeout=10)
            if resp.status_code == 200:
                health_data = resp.json()
                print(f"✅ Platform Status: {health_data.get('status', 'unknown')}")
                print(f"✅ Database: {health_data.get('database', 'unknown')}")
                print(f"✅ Market Data: {health_data.get('market_data', 'unknown')}")
                print(f"✅ Environment: {health_data.get('environment', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {resp.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
            
    def test_authentication(self):
        """Test authentication system."""
        print("\n🔐 Testing Authentication...")
        
        try:
            # Get login page
            resp = self.session.get(f"{self.base_url}/login", timeout=10)
            if resp.status_code != 200:
                print(f"❌ Login page failed: {resp.status_code}")
                return False
                
            # Login with epinnox credentials
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            resp = self.session.post(f"{self.base_url}/login", data=login_data, timeout=10)
            if resp.status_code in [200, 302]:  # Success or redirect
                print("✅ Authentication successful")
                print("✅ Session established")
                return True
            else:
                print(f"❌ Authentication failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
            
    def test_professional_ui(self):
        """Test professional trading UI loading."""
        print("\n🎨 Testing Professional Trading UI...")
        
        try:
            resp = self.session.get(f"{self.base_url}/live-trading", timeout=10)
            if resp.status_code == 200:
                content = resp.text
                
                # Check for professional UI elements
                ui_elements = {
                    'trading-platform': 'Trading Platform Layout',
                    'chart-container': 'Chart Container',
                    'orderbook-container': 'Order Book',
                    'trading-panel': 'Trading Panel',
                    'positions-container': 'Positions Container',
                    'tradingview_chart': 'TradingView Chart',
                    'market-info-bar': 'Market Info Bar',
                    'automation-panel': 'Automation Panel'
                }
                
                found_elements = []
                missing_elements = []
                
                for element, description in ui_elements.items():
                    if element in content:
                        found_elements.append(description)
                    else:
                        missing_elements.append(description)
                        
                print(f"✅ Found {len(found_elements)}/8 professional UI elements:")
                for element in found_elements:
                    print(f"   ✓ {element}")
                    
                if missing_elements:
                    print(f"⚠️ Missing elements:")
                    for element in missing_elements:
                        print(f"   ✗ {element}")
                        
                # Check for CSS and JavaScript
                if 'unified_header.css' in content:
                    print("✅ Header CSS loaded")
                if 'header_navigation.js' in content:
                    print("✅ Navigation JavaScript loaded")
                if 'TradingView.widget' in content:
                    print("✅ TradingView widget configured")
                    
                return len(found_elements) >= 6  # Pass if most elements found
            else:
                print(f"❌ UI loading failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ UI test error: {e}")
            return False
            
    def test_market_data(self):
        """Test market data integration."""
        print("\n📈 Testing Market Data Integration...")
        
        try:
            resp = self.session.get(f"{self.base_url}/api/market-data/current", timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                print(f"✅ Market data API accessible")
                print(f"✅ Data sources: {len(data)} symbols/sources")
                
                # Check data structure
                if isinstance(data, dict):
                    print("✅ Market data structure valid")
                    
                    # Look for any price data
                    has_price_data = False
                    for key, value in data.items():
                        if isinstance(value, dict) and ('price' in str(value).lower() or 'usdt' in str(value).lower()):
                            has_price_data = True
                            break
                            
                    if has_price_data:
                        print("✅ Price data available")
                    else:
                        print("⚠️ No price data found")
                        
                return True
            else:
                print(f"❌ Market data failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Market data error: {e}")
            return False
            
    def test_htx_integration(self):
        """Test HTX API integration."""
        print("\n⚡ Testing HTX API Integration...")
        
        try:
            resp = self.session.get(f"{self.base_url}/api/live-trading/state", timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                print("✅ HTX trading state API accessible")
                
                # Check for key trading data
                if 'current_price' in data:
                    price = data['current_price']
                    print(f"✅ Current price available: ${price:.6f}")
                else:
                    print("⚠️ Current price not available")
                    
                if 'account_balance' in data:
                    print("✅ Account balance data structure present")
                else:
                    print("⚠️ Account balance not available")
                    
                if 'position' in data:
                    print("✅ Position data structure present")
                else:
                    print("✅ No open position (expected)")
                    
                # Check for HTX-specific data
                if 'exchange' in data or 'htx' in str(data).lower():
                    print("✅ HTX-specific data detected")
                    
                return True
            else:
                print(f"❌ HTX integration failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ HTX integration error: {e}")
            return False
            
    def test_tradingview_integration(self):
        """Test TradingView chart integration."""
        print("\n📊 Testing TradingView Integration...")
        
        try:
            resp = self.session.get(f"{self.base_url}/live-trading", timeout=10)
            if resp.status_code == 200:
                content = resp.text
                
                # Check for TradingView integration
                tradingview_checks = {
                    'tradingview.com/tv.js': 'TradingView Library',
                    'TradingView.widget': 'Widget Configuration',
                    'tradingview_chart': 'Chart Container',
                    'BINANCE:DOGEUSDT': 'DOGE/USDT Symbol',
                    'theme": "dark"': 'Dark Theme',
                    'interval": "1"': 'Chart Interval'
                }
                
                found_features = []
                for check, description in tradingview_checks.items():
                    if check in content:
                        found_features.append(description)
                        
                print(f"✅ TradingView integration: {len(found_features)}/6 features found")
                for feature in found_features:
                    print(f"   ✓ {feature}")
                    
                if len(found_features) >= 4:
                    print("✅ TradingView chart fully integrated")
                    return True
                elif len(found_features) >= 2:
                    print("⚠️ TradingView partially integrated")
                    return True
                else:
                    print("❌ TradingView integration incomplete")
                    return False
            else:
                print(f"❌ TradingView test failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ TradingView test error: {e}")
            return False

def main():
    """Run the professional trading platform test suite."""
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = SimpleTradingTester()
    tester.run_comprehensive_test()
    
    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
