#!/usr/bin/env python3
"""
🔧 Test Account Integration Fix
Verify that the account integration fixes work correctly
"""

import asyncio
import logging
import os
import yaml
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_account_fix():
    """Test that the account integration fixes work."""
    
    logger.info("🔧 TESTING ACCOUNT INTEGRATION FIXES")
    logger.info("=" * 50)
    
    try:
        # Load configuration
        config_path = Path("../config.yaml")
        if not config_path.exists():
            config_path = Path("config.yaml")
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"✅ Configuration loaded from {config_path}")
        
        # Load .env file
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            logger.info("✅ .env file loaded")
        
        # Test 1: Data Storage Config Fix
        logger.info("\n🔧 TEST 1: Data Storage Configuration")
        logger.info("-" * 40)
        
        from storage.live_store import LiveDataStore
        
        try:
            data_store = LiveDataStore(config)
            logger.info("✅ LiveDataStore created successfully")
        except Exception as e:
            logger.error(f"❌ LiveDataStore creation failed: {e}")
            return False
        
        # Test 2: Execution Controller Account Tracker
        logger.info("\n🎯 TEST 2: Execution Controller Account Tracker")
        logger.info("-" * 40)
        
        from execution.execution_controller import ExecutionController
        
        execution_controller = ExecutionController(config)
        
        # Check if account tracker exists
        if hasattr(execution_controller, 'account_tracker') and execution_controller.account_tracker:
            logger.info("✅ Execution controller has account tracker")
            
            # Test the initialization method
            await execution_controller._initialize_account_tracker()
            
            # Check if snapshot was created
            snapshot = execution_controller.account_tracker.get_current_snapshot()
            if snapshot:
                logger.info(f"✅ Account tracker has snapshot with balance: ${snapshot.total_balance:.2f}")
            else:
                logger.error("❌ Account tracker still has no snapshot after initialization")
                return False
        else:
            logger.error("❌ Execution controller missing account tracker")
            return False
        
        # Test 3: Dashboard Integration
        logger.info("\n🌐 TEST 3: Dashboard Integration")
        logger.info("-" * 40)
        
        from ui.ai_strategy_tuner import AIStrategyTunerDashboard
        
        # Create dashboard with execution controller
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
        
        # Test account summary API method directly
        class MockRequest:
            pass
        
        mock_request = MockRequest()
        
        try:
            # This simulates what happens when the dashboard calls /api/account/summary
            response = await dashboard.api_get_account_summary(mock_request)
            
            if hasattr(response, 'body'):
                import json
                response_data = json.loads(response.body.decode())
                
                logger.info("✅ Dashboard API response:")
                logger.info(f"   Success: {response_data.get('success')}")
                logger.info(f"   Total Balance: ${response_data.get('total_balance', 0):.2f}")
                logger.info(f"   Data Source: {response_data.get('data_source', 'unknown')}")
                logger.info(f"   Connected: {response_data.get('connected', False)}")
                
                # Check if it's using real data now
                if response_data.get('data_source') == 'live_htx':
                    logger.info("✅ SUCCESS: Dashboard is now using LIVE HTX DATA!")
                    
                    # Check if balance is correct
                    real_balance = response_data.get('total_balance', 0)
                    if real_balance > 10.0:  # Should be around $12
                        logger.info(f"✅ SUCCESS: Real balance displayed: ${real_balance:.2f}")
                    else:
                        logger.warning(f"⚠️ Balance seems low: ${real_balance:.2f}")
                        
                elif response_data.get('data_source') == 'mock':
                    logger.error("❌ STILL BROKEN: Dashboard is still returning MOCK DATA")
                    return False
                elif response_data.get('data_source') == 'fallback':
                    logger.error("❌ STILL BROKEN: Dashboard is using FALLBACK DATA")
                    return False
                else:
                    logger.warning(f"⚠️ Unknown data source: {response_data.get('data_source')}")
                
            else:
                logger.error("❌ Dashboard API returned invalid response")
                return False
                
        except Exception as e:
            logger.error(f"❌ Dashboard API test failed: {e}")
            return False
        
        # Test 4: Real vs Mock Balance Comparison
        logger.info("\n💰 TEST 4: Real vs Mock Balance Comparison")
        logger.info("-" * 40)
        
        # Get real balance from HTX client
        htx_client = execution_controller.account_tracker.htx_client
        if htx_client and htx_client.is_connected:
            real_balance_data = await htx_client.get_account_balance()
            if real_balance_data:
                real_balance = real_balance_data.get('USDT', {}).get('total', 0)
                dashboard_balance = response_data.get('total_balance', 0)
                
                logger.info(f"📊 Real HTX Balance: ${real_balance:.2f}")
                logger.info(f"📊 Dashboard Balance: ${dashboard_balance:.2f}")
                
                if abs(real_balance - dashboard_balance) < 0.01:
                    logger.info("✅ SUCCESS: Balances match perfectly!")
                else:
                    logger.warning(f"⚠️ Balance mismatch: ${abs(real_balance - dashboard_balance):.2f} difference")
        
        logger.info("\n🎉 ACCOUNT INTEGRATION FIX TESTING COMPLETED")
        logger.info("=" * 50)
        logger.info("✅ All fixes are working correctly!")
        logger.info("🎯 Dashboard should now show real account balance")
        
        # Cleanup
        if hasattr(htx_client, 'disconnect'):
            await htx_client.disconnect()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during account fix testing: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_account_fix())
    if success:
        print("\n✅ ACCOUNT INTEGRATION FIXES: WORKING")
        print("🎯 Dashboard should now display real account balance")
    else:
        print("\n❌ ACCOUNT INTEGRATION FIXES: STILL BROKEN")
        exit(1)
