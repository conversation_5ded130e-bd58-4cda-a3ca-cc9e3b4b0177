#!/usr/bin/env python3
"""
Test script to verify enhanced reasoning extraction from truncated LLM responses.
"""

import requests
import json
import time
from datetime import datetime

def test_reasoning_quality():
    """Test the quality of reasoning extraction from recent LLM decisions."""
    print("🧪 Testing Enhanced Reasoning Extraction...")
    print("=" * 60)
    
    try:
        # Get recent LLM decisions
        response = requests.get("http://localhost:8086/api/data")
        data = response.json()
        
        decisions = data.get('llm_decisions', [])
        if not decisions:
            print("❌ No LLM decisions found")
            return
        
        # Analyze last 10 decisions
        recent_decisions = decisions[-10:]
        
        print(f"📊 Analyzing {len(recent_decisions)} recent decisions...")
        print()
        
        reasoning_quality_scores = []
        
        for i, decision in enumerate(recent_decisions, 1):
            reasoning = decision.get('reasoning', '')
            confidence = decision.get('confidence', 0) * 100
            action = decision.get('action', 'UNKNOWN')
            timestamp = decision.get('formatted_time', 'Unknown')
            
            print(f"Decision #{i} - {timestamp}")
            print(f"Action: {action} ({confidence:.0f}%)")
            print(f"Reasoning Length: {len(reasoning)} characters")
            
            # Quality assessment
            quality_score = assess_reasoning_quality(reasoning)
            reasoning_quality_scores.append(quality_score)
            
            print(f"Quality Score: {quality_score}/10")
            print(f"Reasoning Preview: {reasoning[:200]}...")
            
            # Check for specific quality indicators
            indicators = check_quality_indicators(reasoning)
            print(f"Quality Indicators: {indicators}")
            print("-" * 50)
        
        # Overall assessment
        avg_quality = sum(reasoning_quality_scores) / len(reasoning_quality_scores)
        print(f"\n📈 Overall Assessment:")
        print(f"Average Quality Score: {avg_quality:.1f}/10")
        print(f"High Quality Decisions (>7): {sum(1 for s in reasoning_quality_scores if s > 7)}/{len(reasoning_quality_scores)}")
        print(f"Generic Fallback Decisions: {sum(1 for s in reasoning_quality_scores if s < 3)}/{len(reasoning_quality_scores)}")
        
        # Success criteria
        if avg_quality >= 7.0:
            print("✅ EXCELLENT: High-quality reasoning preserved")
        elif avg_quality >= 5.0:
            print("🟡 GOOD: Reasonable reasoning quality")
        else:
            print("❌ POOR: Reasoning quality needs improvement")
            
    except Exception as e:
        print(f"❌ Error testing reasoning extraction: {e}")

def assess_reasoning_quality(reasoning: str) -> int:
    """Assess the quality of extracted reasoning on a scale of 1-10."""
    if not reasoning:
        return 0
    
    score = 0
    
    # Length check (minimum meaningful content)
    if len(reasoning) >= 50:
        score += 2
    elif len(reasoning) >= 20:
        score += 1
    
    # Trading-specific content
    trading_keywords = [
        'model', 'signal', 'buy', 'sell', 'market', 'trend', 'price', 'volume',
        'orderflow', 'vwap', 'rsi', 'volatility', 'support', 'resistance',
        'bullish', 'bearish', 'momentum', 'analysis', 'confidence', 'risk'
    ]
    
    keyword_count = sum(1 for keyword in trading_keywords if keyword.lower() in reasoning.lower())
    score += min(3, keyword_count)  # Max 3 points for keywords
    
    # Specific model mentions
    model_mentions = ['orderflow', 'vwap', 'rsi', 'volume profile', 'volatility']
    model_count = sum(1 for model in model_mentions if model.lower() in reasoning.lower())
    score += min(2, model_count)  # Max 2 points for model mentions
    
    # Coherent analysis (not generic fallback)
    if 'extracted from malformed response' in reasoning.lower():
        score = max(1, score - 5)  # Heavy penalty for generic fallback
    elif 'unable to extract' in reasoning.lower():
        score = max(1, score - 3)  # Moderate penalty
    
    # Detailed analysis indicators
    detail_indicators = ['however', 'despite', 'given', 'considering', 'indicates', 'suggests']
    detail_count = sum(1 for indicator in detail_indicators if indicator.lower() in reasoning.lower())
    score += min(2, detail_count)  # Max 2 points for detailed analysis
    
    # Market context awareness
    context_indicators = ['market regime', 'sideways', 'ranging', 'trend', 'volatility', 'support', 'resistance']
    context_count = sum(1 for indicator in context_indicators if indicator.lower() in reasoning.lower())
    score += min(1, context_count)  # Max 1 point for context awareness
    
    return min(10, score)

def check_quality_indicators(reasoning: str) -> dict:
    """Check for specific quality indicators in reasoning."""
    indicators = {
        'has_model_references': any(model in reasoning.lower() for model in ['orderflow', 'vwap', 'rsi', 'volume']),
        'has_market_context': any(context in reasoning.lower() for context in ['market', 'trend', 'regime', 'sideways']),
        'has_confidence_analysis': any(conf in reasoning.lower() for conf in ['confidence', 'conviction', 'strong', 'weak']),
        'has_risk_assessment': any(risk in reasoning.lower() for risk in ['risk', 'caution', 'aggressive', 'conservative']),
        'is_detailed': len(reasoning) > 100,
        'is_generic_fallback': 'extracted from malformed response' in reasoning.lower(),
        'has_technical_analysis': any(tech in reasoning.lower() for tech in ['support', 'resistance', 'pressure', 'momentum'])
    }
    return indicators

if __name__ == "__main__":
    test_reasoning_quality()
