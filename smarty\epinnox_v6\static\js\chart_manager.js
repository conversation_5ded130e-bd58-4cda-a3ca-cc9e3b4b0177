/**
 * Chart Manager - Real-time price charts with signal overlays
 * Handles Chart.js integration for Bitcoin price data and trading signals
 */

class ChartManager {
    constructor() {
        this.priceChart = null;
        this.pnlChart = null;
        this.currentSymbol = 'BTC-USDT';
        this.priceData = [];
        this.signalData = [];
        this.isInitialized = false;

        console.log('📊 Chart Manager initialized');
    }

    /**
     * Initialize charts when DOM is ready
     */
    async initialize() {
        try {
            if (this.isInitialized) return;

            // Wait for Chart.js to be available
            if (typeof Chart === 'undefined') {
                console.log('⏳ Waiting for Chart.js to load...');
                setTimeout(() => this.initialize(), 100);
                return;
            }

            await this.initializePriceChart();
            await this.initializePnLChart();

            this.isInitialized = true;
            console.log('✅ Charts initialized successfully');

        } catch (error) {
            console.error('❌ Error initializing charts:', error);
        }
    }

    /**
     * Initialize the main price chart with signal overlays
     */
    async initializePriceChart() {
        const canvas = document.getElementById('signal-chart');
        if (!canvas) {
            console.warn('⚠️ Price chart canvas not found (looking for signal-chart)');
            return;
        }

        const ctx = canvas.getContext('2d');

        this.priceChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Price',
                        data: [],
                        borderColor: '#00d4ff',
                        backgroundColor: 'rgba(0, 212, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    },
                    {
                        label: 'LONG Signals',
                        data: [],
                        backgroundColor: '#00ff88',
                        borderColor: '#00ff88',
                        pointRadius: 8,
                        pointHoverRadius: 10,
                        showLine: false,
                        pointStyle: 'triangle'
                    },
                    {
                        label: 'SHORT Signals',
                        data: [],
                        backgroundColor: '#ff4757',
                        borderColor: '#ff4757',
                        pointRadius: 8,
                        pointHoverRadius: 10,
                        showLine: false,
                        pointStyle: 'rectRot'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    title: {
                        display: true,
                        text: `${this.currentSymbol} Price Chart`,
                        color: '#ffffff',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        labels: {
                            color: '#ffffff',
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#00d4ff',
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const timestamp = context[0].parsed.x;
                                return new Date(timestamp).toLocaleString();
                            },
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return `Price: $${context.parsed.y.toFixed(2)}`;
                                } else {
                                    const signal = context.raw;
                                    return [
                                        `${signal.direction} Signal`,
                                        `Price: $${signal.y.toFixed(2)}`,
                                        `Confidence: ${signal.confidence}%`,
                                        `Status: ${signal.status.toUpperCase()}`,
                                        `P&L: $${signal.pnl.toFixed(2)}`
                                    ];
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff',
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                },
                animation: {
                    duration: 0 // Disable animations for real-time updates
                }
            }
        });

        console.log('📈 Price chart initialized');
    }

    /**
     * Initialize P&L tracking chart
     */
    async initializePnLChart() {
        const canvas = document.getElementById('pnlChart');
        if (!canvas) {
            console.warn('⚠️ P&L chart canvas not found - skipping P&L chart initialization');
            return;
        }

        const ctx = canvas.getContext('2d');

        this.pnlChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Realized P&L',
                        data: [],
                        borderColor: '#00ff88',
                        backgroundColor: 'rgba(0, 255, 136, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    },
                    {
                        label: 'Unrealized P&L',
                        data: [],
                        borderColor: '#ffa502',
                        backgroundColor: 'rgba(255, 165, 2, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'P&L Performance',
                        color: '#ffffff',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#ffffff',
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });

        console.log('💰 P&L chart initialized');
    }

    /**
     * Update price chart with new data
     */
    updatePriceChart(priceData, signalData) {
        if (!this.priceChart) return;

        try {
            // Update price data
            this.priceChart.data.datasets[0].data = priceData;

            // Separate signals by direction
            const longSignals = signalData.filter(s => s.direction === 'LONG');
            const shortSignals = signalData.filter(s => s.direction === 'SHORT');

            this.priceChart.data.datasets[1].data = longSignals;
            this.priceChart.data.datasets[2].data = shortSignals;

            this.priceChart.update('none'); // No animation for real-time updates

        } catch (error) {
            console.error('❌ Error updating price chart:', error);
        }
    }

    /**
     * Update P&L chart with new data
     */
    updatePnLChart(pnlData) {
        if (!this.pnlChart) return;

        try {
            // Separate realized and unrealized P&L
            const realizedData = pnlData.map(d => ({
                x: d.x,
                y: d.y
            }));

            const unrealizedData = pnlData.map(d => ({
                x: d.x,
                y: d.y + (d.unrealized || 0)
            }));

            this.pnlChart.data.datasets[0].data = realizedData;
            this.pnlChart.data.datasets[1].data = unrealizedData;

            this.pnlChart.update('none');

        } catch (error) {
            console.error('❌ Error updating P&L chart:', error);
        }
    }

    /**
     * Add new price point to chart
     */
    addPricePoint(timestamp, price) {
        if (!this.priceChart) return;

        const dataPoint = {
            x: timestamp,
            y: price
        };

        this.priceChart.data.datasets[0].data.push(dataPoint);

        // Keep only last 100 points for performance
        if (this.priceChart.data.datasets[0].data.length > 100) {
            this.priceChart.data.datasets[0].data.shift();
        }

        this.priceChart.update('none');
    }

    /**
     * Add new signal to chart
     */
    addSignal(signal) {
        if (!this.priceChart) return;

        const datasetIndex = signal.direction === 'LONG' ? 1 : 2;

        const signalPoint = {
            x: signal.x,
            y: signal.y,
            direction: signal.direction,
            confidence: signal.confidence,
            status: signal.status,
            pnl: signal.pnl,
            signal_id: signal.signal_id
        };

        this.priceChart.data.datasets[datasetIndex].data.push(signalPoint);
        this.priceChart.update('none');

        console.log(`📊 Signal added to chart: ${signal.direction} @ $${signal.y.toFixed(2)}`);
    }

    /**
     * Update chart symbol
     */
    updateSymbol(symbol) {
        this.currentSymbol = symbol;

        if (this.priceChart) {
            this.priceChart.options.plugins.title.text = `${symbol} Price Chart`;
            this.priceChart.update();
        }

        console.log(`📊 Chart symbol updated to: ${symbol}`);
    }

    /**
     * Clear all chart data
     */
    clearCharts() {
        if (this.priceChart) {
            this.priceChart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            this.priceChart.update();
        }

        if (this.pnlChart) {
            this.pnlChart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            this.pnlChart.update();
        }

        console.log('🧹 Charts cleared');
    }

    /**
     * Resize charts (call when container size changes)
     */
    resize() {
        if (this.priceChart) {
            this.priceChart.resize();
        }
        if (this.pnlChart) {
            this.pnlChart.resize();
        }
    }

    /**
     * Destroy charts
     */
    destroy() {
        if (this.priceChart) {
            this.priceChart.destroy();
            this.priceChart = null;
        }
        if (this.pnlChart) {
            this.pnlChart.destroy();
            this.pnlChart = null;
        }

        this.isInitialized = false;
        console.log('🗑️ Charts destroyed');
    }
}

// Global chart manager instance
window.chartManager = new ChartManager();

// DISABLED: Chart manager conflicts with main chart initialization
// Initialize when DOM is ready
// document.addEventListener('DOMContentLoaded', () => {
//     window.chartManager.initialize();
// });

// Handle window resize
window.addEventListener('resize', () => {
    if (window.chartManager && window.chartManager.isInitialized) {
        window.chartManager.resize();
    }
});
