#!/usr/bin/env python3
"""
Strategy Performance Tracker
Maintains rolling logs of win rates, divergence counts, regime alignment, and execution quality
"""

import logging
import time
import json
import os
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    WIN_RATE = "win_rate"
    AVG_RETURN = "avg_return"
    SHARPE_RATIO = "sharpe_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    PROFIT_FACTOR = "profit_factor"

@dataclass
class TradeResult:
    """Individual trade result for tracking."""
    trade_id: str
    symbol: str
    action: str
    entry_price: float
    exit_price: float
    size: float
    pnl: float
    duration_minutes: float
    model_contributions: Dict[str, float]
    mta_alignment_score: float
    signal_confidence: float
    execution_quality: float
    timestamp: float

@dataclass
class ModelPerformance:
    """Performance metrics for individual models."""
    model_name: str
    total_signals: int
    winning_signals: int
    win_rate: float
    total_pnl: float
    avg_return: float
    reliability_score: float
    last_updated: float

@dataclass
class StrategyHealth:
    """Overall strategy health assessment."""
    overall_win_rate: float
    total_trades: int
    total_pnl: float
    avg_trade_duration: float
    model_consensus_rate: float
    mta_alignment_rate: float
    execution_quality_avg: float
    risk_adjusted_return: float
    health_score: float  # 0-1
    recommendations: List[str]
    timestamp: float

class StrategyTracker:
    """
    Strategy Performance Tracker

    Tracks and analyzes trading strategy performance across multiple dimensions:
    - Individual model performance
    - Multi-timeframe alignment effectiveness
    - Signal-LLM divergence tracking
    - Execution quality monitoring
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.tracker_config = config.get('strategy_tracker', {})

        # Storage configuration
        self.max_trade_history = self.tracker_config.get('max_trade_history', 1000)
        self.performance_window = self.tracker_config.get('performance_window', 100)  # Last N trades
        self.save_interval = self.tracker_config.get('save_interval', 300)  # 5 minutes

        # Data storage
        self.trade_history: deque = deque(maxlen=self.max_trade_history)
        self.model_performance: Dict[str, ModelPerformance] = {}
        self.divergence_log: deque = deque(maxlen=500)
        self.regime_alignment_log: deque = deque(maxlen=200)

        # Performance tracking
        self.performance_snapshots: deque = deque(maxlen=100)
        self.last_health_check = 0
        self.health_check_interval = 3600  # 1 hour

        # Statistics
        self.stats = {
            'total_trades_tracked': 0,
            'total_signals_tracked': 0,
            'divergences_detected': 0,
            'regime_alignments_tracked': 0,
            'last_save_time': 0
        }

        # Load persisted data
        self._load_persisted_data()

        logger.info("📊 Strategy Tracker initialized")

    def record_trade_result(self, trade_result: TradeResult):
        """Record a completed trade result."""
        try:
            # Add to trade history
            self.trade_history.append(trade_result)
            self.stats['total_trades_tracked'] += 1

            # Update model performance
            self._update_model_performance(trade_result)

            # Log trade
            recent_wr = self._get_recent_win_rate()
            logger.info(f"📊 Trade recorded: {trade_result.symbol} {trade_result.action} "
                       f"PnL: ${trade_result.pnl:.2f} (WR: {recent_wr:.1%})")

            # Trigger health check if needed
            if time.time() - self.last_health_check > self.health_check_interval:
                self._perform_health_check()

        except Exception as e:
            logger.error(f"❌ Error recording trade result: {e}")

    def record_signal_divergence(self, signal_data: Dict[str, Any],
                                llm_decision: Dict[str, Any]):
        """Record divergence between signal and LLM decision."""
        try:
            signal_action = signal_data.get('action', 'WAIT')
            llm_action = llm_decision.get('action', 'WAIT')

            # Check for divergence
            if signal_action != llm_action and signal_action != 'WAIT' and llm_action != 'WAIT':
                divergence_record = {
                    'timestamp': time.time(),
                    'symbol': signal_data.get('symbol', 'UNKNOWN'),
                    'signal_action': signal_action,
                    'signal_confidence': signal_data.get('confidence', 0.0),
                    'llm_action': llm_action,
                    'llm_confidence': llm_decision.get('confidence', 0.0),
                    'divergence_type': f"{signal_action}_vs_{llm_action}",
                    'price': signal_data.get('price', 0.0)
                }

                self.divergence_log.append(divergence_record)
                self.stats['divergences_detected'] += 1

                logger.warning(f"🔄 Signal-LLM divergence detected: "
                             f"Signal={signal_action} vs LLM={llm_action}")

        except Exception as e:
            logger.error(f"❌ Error recording signal divergence: {e}")

    def record_regime_alignment(self, signal_data: Dict[str, Any],
                              mta_analysis: Optional[Any]):
        """Record regime alignment effectiveness."""
        try:
            if not mta_analysis:
                return

            signal_action = signal_data.get('action', 'WAIT')
            alignment_score = getattr(mta_analysis, 'alignment_score', 0.0)
            dominant_trend = getattr(mta_analysis, 'dominant_trend', 'neutral')

            # Determine if signal aligns with dominant trend
            aligned = False
            if signal_action in ['LONG', 'BUY'] and dominant_trend == 'bullish':
                aligned = True
            elif signal_action in ['SHORT', 'SELL'] and dominant_trend == 'bearish':
                aligned = True
            elif signal_action == 'WAIT' and dominant_trend == 'neutral':
                aligned = True

            alignment_record = {
                'timestamp': time.time(),
                'symbol': signal_data.get('symbol', 'UNKNOWN'),
                'signal_action': signal_action,
                'dominant_trend': dominant_trend,
                'alignment_score': alignment_score,
                'aligned': aligned,
                'confidence': signal_data.get('confidence', 0.0)
            }

            self.regime_alignment_log.append(alignment_record)
            self.stats['regime_alignments_tracked'] += 1

        except Exception as e:
            logger.error(f"❌ Error recording regime alignment: {e}")

    def _update_model_performance(self, trade_result: TradeResult):
        """Update individual model performance metrics."""
        try:
            for model_name, contribution in trade_result.model_contributions.items():
                if model_name not in self.model_performance:
                    self.model_performance[model_name] = ModelPerformance(
                        model_name=model_name,
                        total_signals=0,
                        winning_signals=0,
                        win_rate=0.0,
                        total_pnl=0.0,
                        avg_return=0.0,
                        reliability_score=0.5,
                        last_updated=time.time()
                    )

                model_perf = self.model_performance[model_name]

                # Update metrics
                model_perf.total_signals += 1
                if trade_result.pnl > 0:
                    model_perf.winning_signals += 1

                model_perf.win_rate = model_perf.winning_signals / model_perf.total_signals
                model_perf.total_pnl += trade_result.pnl * abs(contribution)  # Weight by contribution
                model_perf.avg_return = model_perf.total_pnl / model_perf.total_signals

                # Update reliability score based on recent performance
                recent_trades = [t for t in list(self.trade_history)[-20:]
                               if model_name in t.model_contributions]
                if len(recent_trades) >= 5:
                    recent_wins = sum(1 for t in recent_trades if t.pnl > 0)
                    recent_win_rate = recent_wins / len(recent_trades)
                    model_perf.reliability_score = recent_win_rate

                model_perf.last_updated = time.time()

        except Exception as e:
            logger.error(f"❌ Error updating model performance: {e}")

    def _perform_health_check(self):
        """Perform comprehensive strategy health assessment."""
        try:
            self.last_health_check = time.time()

            if len(self.trade_history) < 10:
                return  # Need minimum trades for meaningful analysis

            # Calculate health metrics
            recent_trades = list(self.trade_history)[-self.performance_window:]

            # Overall performance
            total_trades = len(recent_trades)
            winning_trades = sum(1 for t in recent_trades if t.pnl > 0)
            overall_win_rate = winning_trades / total_trades if total_trades > 0 else 0
            total_pnl = sum(t.pnl for t in recent_trades)
            avg_trade_duration = np.mean([t.duration_minutes for t in recent_trades])

            # Model consensus rate
            consensus_trades = sum(1 for t in recent_trades
                                 if len([c for c in t.model_contributions.values() if abs(c) > 0.1]) >= 2)
            model_consensus_rate = consensus_trades / total_trades if total_trades > 0 else 0

            # MTA alignment rate
            aligned_trades = sum(1 for t in recent_trades if t.mta_alignment_score > 0.6)
            mta_alignment_rate = aligned_trades / total_trades if total_trades > 0 else 0

            # Execution quality
            execution_quality_avg = np.mean([t.execution_quality for t in recent_trades])

            # Risk-adjusted return (simplified Sharpe-like ratio)
            returns = [t.pnl / (t.entry_price * t.size) for t in recent_trades if t.entry_price > 0 and t.size > 0]
            if returns:
                avg_return = np.mean(returns)
                return_std = np.std(returns) if len(returns) > 1 else 0.01
                risk_adjusted_return = avg_return / return_std if return_std > 0 else 0
            else:
                risk_adjusted_return = 0

            # Calculate health score
            health_score = self._calculate_health_score(
                overall_win_rate, model_consensus_rate, mta_alignment_rate,
                execution_quality_avg, risk_adjusted_return
            )

            # Generate recommendations
            recommendations = self._generate_recommendations(
                overall_win_rate, model_consensus_rate, mta_alignment_rate,
                execution_quality_avg, recent_trades
            )

            # Create health assessment
            health = StrategyHealth(
                overall_win_rate=overall_win_rate,
                total_trades=total_trades,
                total_pnl=total_pnl,
                avg_trade_duration=avg_trade_duration,
                model_consensus_rate=model_consensus_rate,
                mta_alignment_rate=mta_alignment_rate,
                execution_quality_avg=execution_quality_avg,
                risk_adjusted_return=risk_adjusted_return,
                health_score=health_score,
                recommendations=recommendations,
                timestamp=time.time()
            )

            # Store health snapshot
            self.performance_snapshots.append(health)

            logger.info(f"🏥 Strategy health check: Score={health_score:.2f}, "
                       f"WR={overall_win_rate:.1%}, PnL=${total_pnl:.2f}")

            if health_score < 0.4:
                logger.warning(f"⚠️ Strategy health below threshold: {recommendations}")

        except Exception as e:
            logger.error(f"❌ Error performing health check: {e}")

    def _calculate_health_score(self, win_rate: float, consensus_rate: float,
                              alignment_rate: float, execution_quality: float,
                              risk_adjusted_return: float) -> float:
        """Calculate overall strategy health score (0-1)."""
        try:
            # Weighted combination of health factors
            weights = {
                'win_rate': 0.30,
                'consensus': 0.20,
                'alignment': 0.20,
                'execution': 0.15,
                'risk_adjusted': 0.15
            }

            # Normalize risk-adjusted return to 0-1 scale
            normalized_risk_return = max(0, min(1, (risk_adjusted_return + 2) / 4))

            health_score = (
                win_rate * weights['win_rate'] +
                consensus_rate * weights['consensus'] +
                alignment_rate * weights['alignment'] +
                execution_quality * weights['execution'] +
                normalized_risk_return * weights['risk_adjusted']
            )

            return max(0.0, min(1.0, health_score))

        except Exception as e:
            logger.error(f"Error calculating health score: {e}")
            return 0.5

    def _generate_recommendations(self, win_rate: float, consensus_rate: float,
                                alignment_rate: float, execution_quality: float,
                                recent_trades: List[TradeResult]) -> List[str]:
        """Generate strategy improvement recommendations."""
        recommendations = []

        try:
            # Win rate recommendations
            if win_rate < 0.45:
                recommendations.append("Low win rate - consider tightening signal filters")
            elif win_rate > 0.75:
                recommendations.append("High win rate - consider increasing position sizes")

            # Model consensus recommendations
            if consensus_rate < 0.4:
                recommendations.append("Low model consensus - review model weights")

            # MTA alignment recommendations
            if alignment_rate < 0.5:
                recommendations.append("Poor timeframe alignment - strengthen MTA integration")

            # Execution quality recommendations
            if execution_quality < 0.7:
                recommendations.append("Poor execution quality - review order logic")

            # Trade duration analysis
            if recent_trades:
                avg_duration = np.mean([t.duration_minutes for t in recent_trades])
                if avg_duration > 240:  # 4 hours
                    recommendations.append("Long trade duration - consider tighter profit targets")
                elif avg_duration < 15:  # 15 minutes
                    recommendations.append("Very short trades - consider wider profit targets")

            # Model-specific recommendations
            for model_name, perf in self.model_performance.items():
                if perf.total_signals >= 10:
                    if perf.win_rate < 0.4:
                        recommendations.append(f"Poor {model_name} performance - reduce weight")
                    elif perf.win_rate > 0.7:
                        recommendations.append(f"Strong {model_name} performance - increase weight")

            return recommendations[:5]  # Limit to top 5 recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]

    def _load_persisted_data(self):
        """Load persisted tracking data."""
        try:
            data_file = "data/autonomy/strategy_tracker.json"
            if os.path.exists(data_file):
                with open(data_file, 'r') as f:
                    data = json.load(f)

                # Load model performance
                if 'model_performance' in data:
                    for model_name, perf_data in data['model_performance'].items():
                        self.model_performance[model_name] = ModelPerformance(**perf_data)

                # Load stats
                if 'stats' in data:
                    self.stats.update(data['stats'])

                logger.info("📊 Strategy tracker data loaded from persistence")

        except Exception as e:
            logger.error(f"Error loading persisted data: {e}")

    def _save_persisted_data(self):
        """Save tracking data to persistence."""
        try:
            data_file = "data/autonomy/strategy_tracker.json"
            os.makedirs(os.path.dirname(data_file), exist_ok=True)

            # Prepare data for saving
            save_data = {
                'model_performance': {
                    name: asdict(perf) for name, perf in self.model_performance.items()
                },
                'stats': self.stats,
                'last_save': time.time()
            }

            with open(data_file, 'w') as f:
                json.dump(save_data, f, indent=2, default=str)

            self.stats['last_save_time'] = time.time()
            logger.debug("📊 Strategy tracker data saved to persistence")

        except Exception as e:
            logger.error(f"Error saving persisted data: {e}")

    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get summary of model performance."""
        try:
            summary = {}
            for model_name, perf in self.model_performance.items():
                summary[model_name] = {
                    'win_rate': perf.win_rate,
                    'total_signals': perf.total_signals,
                    'avg_return': perf.avg_return,
                    'reliability_score': perf.reliability_score,
                    'total_pnl': perf.total_pnl
                }
            return summary
        except Exception as e:
            logger.error(f"Error getting model performance summary: {e}")
            return {}

    def get_divergence_analysis(self) -> Dict[str, Any]:
        """Get analysis of signal-LLM divergences."""
        try:
            if not self.divergence_log:
                return {'total_divergences': 0, 'divergence_rate': 0.0}

            recent_divergences = [d for d in self.divergence_log
                                if time.time() - d['timestamp'] < 86400]  # Last 24 hours

            # Analyze divergence types
            divergence_types = {}
            for div in recent_divergences:
                div_type = div['divergence_type']
                divergence_types[div_type] = divergence_types.get(div_type, 0) + 1

            total_signals = self.stats['total_signals_tracked']
            divergence_rate = len(recent_divergences) / max(1, total_signals) * 100

            return {
                'total_divergences': len(recent_divergences),
                'divergence_rate': divergence_rate,
                'divergence_types': divergence_types,
                'most_common_divergence': max(divergence_types, key=divergence_types.get) if divergence_types else None
            }

        except Exception as e:
            logger.error(f"Error getting divergence analysis: {e}")
            return {'total_divergences': 0, 'divergence_rate': 0.0}

    def get_latest_health_assessment(self) -> Optional[StrategyHealth]:
        """Get the latest strategy health assessment."""
        try:
            if self.performance_snapshots:
                return self.performance_snapshots[-1]
            return None
        except Exception as e:
            logger.error(f"Error getting latest health assessment: {e}")
            return None

    def get_performance_trends(self, days: int = 7) -> Dict[str, List[float]]:
        """Get performance trends over specified days."""
        try:
            cutoff_time = time.time() - (days * 86400)
            recent_snapshots = [s for s in self.performance_snapshots
                              if s.timestamp > cutoff_time]

            if not recent_snapshots:
                return {}

            trends = {
                'timestamps': [s.timestamp for s in recent_snapshots],
                'win_rates': [s.overall_win_rate for s in recent_snapshots],
                'health_scores': [s.health_score for s in recent_snapshots],
                'total_pnl': [s.total_pnl for s in recent_snapshots],
                'alignment_rates': [s.mta_alignment_rate for s in recent_snapshots]
            }

            return trends

        except Exception as e:
            logger.error(f"Error getting performance trends: {e}")
            return {}

    def get_tracker_stats(self) -> Dict[str, Any]:
        """Get comprehensive tracker statistics."""
        try:
            recent_health = self.get_latest_health_assessment()
            divergence_analysis = self.get_divergence_analysis()
            model_summary = self.get_model_performance_summary()

            return {
                'basic_stats': self.stats,
                'recent_health': asdict(recent_health) if recent_health else None,
                'divergence_analysis': divergence_analysis,
                'model_performance': model_summary,
                'total_trade_history': len(self.trade_history),
                'health_snapshots': len(self.performance_snapshots)
            }

        except Exception as e:
            logger.error(f"Error getting tracker stats: {e}")
            return self.stats

    async def start_background_tasks(self):
        """Start background maintenance tasks."""
        import asyncio
        asyncio.create_task(self._periodic_save())
        logger.info("📊 Strategy tracker background tasks started")

    async def _periodic_save(self):
        """Periodic data saving task."""
        import asyncio
        while True:
            try:
                await asyncio.sleep(self.save_interval)
                self._save_persisted_data()
            except Exception as e:
                logger.error(f"Error in periodic save: {e}")

    def shutdown(self):
        """Shutdown and save final state."""
        self._save_persisted_data()
        logger.info("📊 Strategy Tracker shutdown complete")
