#!/usr/bin/env python3
"""
Money Circle Platform Critical Issues Fix
Comprehensive fix for database connections, WebSocket issues, and Windows compatibility
"""

import asyncio
import logging
import sys
import os
import platform
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/system_recovery.log')
    ]
)

logger = logging.getLogger(__name__)


class SystemRecoveryManager:
    """Comprehensive system recovery and fix manager."""

    def __init__(self):
        self.fixes_applied = []
        self.errors_encountered = []

    async def run_comprehensive_fix(self):
        """Run all critical system fixes."""
        logger.info("🚀 Starting Money Circle Platform Critical Issues Fix")
        logger.info("=" * 60)

        # 1. Initialize Windows-specific fixes
        await self._fix_windows_event_loop()

        # 2. Fix database schema issues
        await self._fix_database_schema()

        # 3. Test and fix database connections
        await self._fix_database_connections()

        # 4. Test WebSocket connections
        await self._test_websocket_connections()

        # 5. Test CoinGecko integration
        await self._test_coingecko_integration()

        # 6. Setup health monitoring
        await self._setup_health_monitoring()

        # 7. Generate recovery report
        self._generate_recovery_report()

        logger.info("✅ System recovery completed")
        return len(self.errors_encountered) == 0

    async def _fix_windows_event_loop(self):
        """Fix Windows event loop issues."""
        logger.info("🔧 [1/6] Fixing Windows event loop issues...")

        try:
            if platform.system() == 'Windows':
                from utils.windows_event_loop_fix import initialize_windows_fixes
                success = initialize_windows_fixes()

                if success:
                    self.fixes_applied.append("Windows SelectorEventLoop configured")
                    logger.info("✅ Windows event loop fixes applied")
                else:
                    self.errors_encountered.append("Failed to configure Windows event loop")
                    logger.error("❌ Windows event loop fix failed")
            else:
                logger.info("ℹ️ Not on Windows, skipping Windows-specific fixes")

        except Exception as e:
            error_msg = f"Windows event loop fix error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def _fix_database_schema(self):
        """Fix database schema issues."""
        logger.info("🔧 [2/6] Fixing database schema issues...")

        try:
            from database.schema_fixer import fix_database_schema

            db_path = "data/money_circle.db"
            success = fix_database_schema(db_path)

            if success:
                self.fixes_applied.append("Database schema fixed")
                logger.info("✅ Database schema fixes applied")
            else:
                self.errors_encountered.append("Database schema fix failed")
                logger.error("❌ Database schema fix failed")

        except Exception as e:
            error_msg = f"Database schema fix error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def _fix_database_connections(self):
        """Test and fix database connection issues."""
        logger.info("🔧 [3/6] Testing database connections...")

        try:
            from database.connection_manager import get_connection_manager

            db_path = "data/money_circle.db"
            conn_manager = get_connection_manager(db_path)

            # Test sync connection
            with conn_manager.get_connection() as conn:
                result = conn.execute("SELECT 1").fetchone()
                if result:
                    logger.info("✅ Synchronous database connection working")
                    self.fixes_applied.append("Database sync connection verified")

            # Test async connection
            async with conn_manager.get_async_connection() as conn:
                cursor = await conn.execute("SELECT 1")
                result = await cursor.fetchone()
                if result:
                    logger.info("✅ Asynchronous database connection working")
                    self.fixes_applied.append("Database async connection verified")

            # Test health check
            if conn_manager.health_check():
                logger.info("✅ Database health check passed")
                self.fixes_applied.append("Database health check passed")
            else:
                self.errors_encountered.append("Database health check failed")

        except Exception as e:
            error_msg = f"Database connection test error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def _test_websocket_connections(self):
        """Test WebSocket connections."""
        logger.info("🔧 [4/6] Testing WebSocket connections...")

        try:
            from market_data.websocket_connection_manager import HTXWebSocketManager

            # Test HTX WebSocket with short duration
            symbols = ['BTC/USDT']
            manager = HTXWebSocketManager(symbols)

            # Set up test message handler
            message_count = 0

            async def test_handler(data):
                nonlocal message_count
                message_count += 1
                if message_count <= 3:  # Log first few messages
                    logger.info(f"📡 Received HTX data: {data.get('ch', 'unknown')}")

            manager.on_message = test_handler

            # Test connection for 15 seconds
            await manager.start_htx_connection()
            await asyncio.sleep(15)

            stats = manager.get_stats()
            await manager.stop()

            if stats['messages_received'] > 0:
                logger.info(f"✅ HTX WebSocket working ({stats['messages_received']} messages)")
                self.fixes_applied.append(f"HTX WebSocket verified ({stats['messages_received']} messages)")
            else:
                self.errors_encountered.append("HTX WebSocket not receiving data")

        except Exception as e:
            error_msg = f"WebSocket test error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def _test_coingecko_integration(self):
        """Test CoinGecko integration."""
        logger.info("🔧 [5/6] Testing CoinGecko integration...")

        try:
            from utils.windows_event_loop_fix import CoinGeckoClientFixed

            async with CoinGeckoClientFixed() as client:
                data = await client.get_simple_price(
                    ids="bitcoin,ethereum",
                    vs_currencies="usd",
                    include_24hr_change=True
                )

                if data and len(data) > 0:
                    logger.info(f"✅ CoinGecko integration working ({len(data)} coins)")
                    for coin_id, coin_data in data.items():
                        price = coin_data.get('usd', 0)
                        logger.info(f"📊 {coin_id}: ${price:,.2f}")
                    self.fixes_applied.append(f"CoinGecko integration verified ({len(data)} coins)")
                else:
                    self.errors_encountered.append("CoinGecko integration not returning data")

        except Exception as e:
            error_msg = f"CoinGecko test error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def _setup_health_monitoring(self):
        """Setup system health monitoring."""
        logger.info("🔧 [6/6] Setting up health monitoring...")

        try:
            from monitoring.system_health_monitor import get_health_monitor, HealthCheck
            from database.connection_manager import get_connection_manager

            monitor = get_health_monitor()

            # Database health check
            def check_database():
                try:
                    conn_manager = get_connection_manager("data/money_circle.db")
                    return conn_manager.health_check()
                except:
                    return False

            def recover_database():
                try:
                    conn_manager = get_connection_manager("data/money_circle.db")
                    conn_manager.close_all_connections()
                    return conn_manager.health_check()
                except:
                    return False

            monitor.add_health_check(HealthCheck(
                name="database_connection",
                check_function=check_database,
                recovery_function=recover_database,
                interval=60,
                max_failures=3,
                critical=True
            ))

            # Test the monitor briefly
            await monitor.start()
            await asyncio.sleep(5)

            report = monitor.get_health_report()
            if report['overall_status'] == 'healthy':
                logger.info("✅ Health monitoring system operational")
                self.fixes_applied.append("Health monitoring system configured")
            else:
                self.errors_encountered.append("Health monitoring system issues detected")

            await monitor.stop()

        except Exception as e:
            error_msg = f"Health monitoring setup error: {e}"
            self.errors_encountered.append(error_msg)
            logger.error(f"❌ {error_msg}")

    def _generate_recovery_report(self):
        """Generate comprehensive recovery report."""
        logger.info("📋 Generating recovery report...")
        logger.info("=" * 60)

        logger.info(f"✅ Fixes Applied ({len(self.fixes_applied)}):")
        for fix in self.fixes_applied:
            logger.info(f"   • {fix}")

        if self.errors_encountered:
            logger.info(f"❌ Errors Encountered ({len(self.errors_encountered)}):")
            for error in self.errors_encountered:
                logger.info(f"   • {error}")
        else:
            logger.info("🎉 No errors encountered!")

        # Overall status
        if len(self.errors_encountered) == 0:
            logger.info("🎯 OVERALL STATUS: ALL CRITICAL ISSUES RESOLVED")
        elif len(self.errors_encountered) < len(self.fixes_applied):
            logger.info("⚠️ OVERALL STATUS: MOST ISSUES RESOLVED, SOME REMAIN")
        else:
            logger.info("🚨 OVERALL STATUS: CRITICAL ISSUES REMAIN")

        logger.info("=" * 60)


async def main():
    """Main recovery function."""
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)

    recovery_manager = SystemRecoveryManager()
    success = await recovery_manager.run_comprehensive_fix()

    if success:
        print("\n🎉 SUCCESS: All critical issues have been resolved!")
        print("The Money Circle platform should now be stable and operational.")
        return 0
    else:
        print("\n⚠️ PARTIAL SUCCESS: Some issues remain.")
        print("Check the logs for details on remaining issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
