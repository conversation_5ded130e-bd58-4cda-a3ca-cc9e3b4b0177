#!/usr/bin/env python3
"""
Autonomy Loop - Main Controller
Phase 7: Continuously runs autonomous analysis, tuning, and adaptation
"""

import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

from .autonomy_tracker import AutonomyTracker
from .model_tuner import ModelTuner, TuningEvent
from .final_decision_enhancer import FinalDecisionEnhancer, EnhancedDecision

logger = logging.getLogger(__name__)

@dataclass
class AutonomyStatus:
    """Current autonomy system status."""
    enabled: bool
    last_analysis_time: float
    last_tuning_time: float
    performance_score: float
    adaptation_needed: bool
    critical_issues: List[str]
    tuning_events_24h: int
    confidence_drift: float
    system_health: str

class AutonomyLoop:
    """
    Main Autonomy Loop controller that orchestrates:
    - Continuous performance analysis (every 10 min)
    - Self-review and tuning (every 50 trades or 6 hours)
    - Auto-adjustment of model weights & thresholds (every 24 hours)
    - Human oversight dashboard with override capabilities
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.autonomy_config = config.get('autonomy_loop', {})

        # Initialize components
        self.autonomy_tracker = AutonomyTracker(config)
        self.model_tuner = ModelTuner(config)
        self.final_decision_enhancer = FinalDecisionEnhancer(config)

        # Timing configuration
        self.analysis_interval = self.autonomy_config.get('analysis_interval', 600)  # 10 minutes
        self.review_interval = self.autonomy_config.get('review_interval', 21600)  # 6 hours
        self.tuning_interval = self.autonomy_config.get('tuning_interval', 86400)  # 24 hours
        self.trade_review_threshold = self.autonomy_config.get('trade_review_threshold', 50)

        # Control flags
        self.enabled = self.autonomy_config.get('enabled', True)
        self.auto_tuning_enabled = self.autonomy_config.get('auto_tuning_enabled', True)
        self.human_override_active = False
        self.frozen_state = False

        # Timing tracking
        self.last_analysis_time = 0
        self.last_review_time = 0
        self.last_tuning_time = 0
        self.trade_count_since_review = 0

        # Performance tracking
        self.autonomy_metrics_history = []
        self.tuning_effectiveness_scores = []

        # Background task
        self.autonomy_task = None

        logger.info(f"Autonomy Loop initialized (enabled: {self.enabled})")

    async def start_autonomy_loop(self):
        """Start the autonomous operation loop."""
        try:
            if not self.enabled:
                logger.info("Autonomy Loop disabled in configuration")
                return

            if self.autonomy_task and not self.autonomy_task.done():
                logger.warning("Autonomy Loop already running")
                return

            self.autonomy_task = asyncio.create_task(self._autonomy_loop_worker())
            logger.info("🤖 Autonomy Loop started - system is now autonomous")

        except Exception as e:
            logger.error(f"Error starting autonomy loop: {e}")

    async def stop_autonomy_loop(self):
        """Stop the autonomous operation loop."""
        try:
            if self.autonomy_task:
                self.autonomy_task.cancel()
                try:
                    await self.autonomy_task
                except asyncio.CancelledError:
                    pass
                self.autonomy_task = None

            logger.info("🛑 Autonomy Loop stopped")

        except Exception as e:
            logger.error(f"Error stopping autonomy loop: {e}")

    async def _autonomy_loop_worker(self):
        """Main autonomy loop worker."""
        try:
            while True:
                current_time = time.time()

                # Performance analysis (every 10 minutes)
                if current_time - self.last_analysis_time >= self.analysis_interval:
                    await self._perform_analysis()
                    self.last_analysis_time = current_time

                # Self-review (every 6 hours or 50 trades)
                if (current_time - self.last_review_time >= self.review_interval or
                    self.trade_count_since_review >= self.trade_review_threshold):
                    await self._perform_self_review()
                    self.last_review_time = current_time
                    self.trade_count_since_review = 0

                # Auto-tuning (every 24 hours)
                if (current_time - self.last_tuning_time >= self.tuning_interval and
                    self.auto_tuning_enabled and not self.frozen_state):
                    await self._perform_auto_tuning()
                    self.last_tuning_time = current_time

                # Sleep for a short interval
                await asyncio.sleep(60)  # Check every minute

        except asyncio.CancelledError:
            logger.info("Autonomy loop cancelled")
        except Exception as e:
            logger.error(f"Error in autonomy loop: {e}")
            await asyncio.sleep(300)  # Wait 5 minutes before retrying

    async def _perform_analysis(self):
        """Perform continuous performance analysis."""
        try:
            logger.debug("🔍 Performing autonomy analysis...")

            # Get current autonomy metrics
            metrics = self.autonomy_tracker.get_autonomy_metrics()

            # Store metrics history
            self.autonomy_metrics_history.append({
                'timestamp': time.time(),
                'metrics': asdict(metrics)
            })

            # Keep only recent history
            if len(self.autonomy_metrics_history) > 100:
                self.autonomy_metrics_history = self.autonomy_metrics_history[-100:]

            # Log critical issues
            if metrics.critical_issues:
                logger.warning(f"🚨 Critical issues detected: {metrics.critical_issues}")

            # Check if immediate intervention is needed
            if metrics.overall_win_rate < 0.3:
                logger.critical("🚨 CRITICAL: Win rate below 30% - consider manual intervention")
                await self._trigger_emergency_review()

        except Exception as e:
            logger.error(f"Error performing analysis: {e}")

    async def _perform_self_review(self):
        """Perform comprehensive self-review."""
        try:
            logger.info("🧠 Performing self-review...")

            # Get comprehensive metrics
            metrics = self.autonomy_tracker.get_autonomy_metrics()
            tuning_summary = self.model_tuner.get_tuning_summary()
            enhancement_summary = self.final_decision_enhancer.get_enhancement_summary()

            # Generate tuning recommendations
            recommendations = self.autonomy_tracker.get_tuning_recommendations()

            # Log review summary
            logger.info(f"📊 Self-review complete:")
            logger.info(f"   Win rate: {metrics.overall_win_rate:.2f}")
            logger.info(f"   Performance trend: {metrics.recent_trend}")
            logger.info(f"   Adaptation needed: {metrics.adaptation_needed}")
            logger.info(f"   Recommendations: {len(recommendations)}")

            # Store review results
            review_data = {
                'timestamp': time.time(),
                'metrics': asdict(metrics),
                'tuning_summary': tuning_summary,
                'enhancement_summary': enhancement_summary,
                'recommendations': recommendations
            }

            # Apply urgent recommendations immediately
            urgent_recommendations = [r for r in recommendations if r.get('urgency') == 'high']
            if urgent_recommendations and self.auto_tuning_enabled:
                logger.info(f"🚨 Applying {len(urgent_recommendations)} urgent recommendations")
                await self._apply_recommendations(urgent_recommendations)

        except Exception as e:
            logger.error(f"Error performing self-review: {e}")

    async def _perform_auto_tuning(self):
        """Perform automatic model tuning."""
        try:
            logger.info("🔧 Performing auto-tuning...")

            # Get tuning recommendations
            recommendations = self.autonomy_tracker.get_tuning_recommendations()

            if not recommendations:
                logger.info("📊 No tuning recommendations - system performing optimally")
                return

            # Apply recommendations
            tuning_events = await self._apply_recommendations(recommendations)

            if tuning_events:
                logger.info(f"✅ Applied {len(tuning_events)} tuning adjustments")

                # Calculate effectiveness score
                effectiveness_score = self._calculate_tuning_effectiveness(tuning_events)
                self.tuning_effectiveness_scores.append(effectiveness_score)

                # Keep only recent scores
                if len(self.tuning_effectiveness_scores) > 20:
                    self.tuning_effectiveness_scores = self.tuning_effectiveness_scores[-20:]

        except Exception as e:
            logger.error(f"Error performing auto-tuning: {e}")

    async def _apply_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[TuningEvent]:
        """Apply tuning recommendations."""
        try:
            if self.frozen_state:
                logger.info("🧊 Tuning frozen - skipping recommendations")
                return []

            # Apply through model tuner
            tuning_events = self.model_tuner.apply_tuning_recommendations(recommendations, 'auto')

            # Log applied changes
            for event in tuning_events:
                logger.info(f"🔧 Tuned {event.parameter}: {event.old_value:.3f} → {event.new_value:.3f} ({event.reason})")

            return tuning_events

        except Exception as e:
            logger.error(f"Error applying recommendations: {e}")
            return []

    async def _trigger_emergency_review(self):
        """Trigger emergency review for critical performance issues."""
        try:
            logger.critical("🚨 EMERGENCY REVIEW TRIGGERED")

            # Freeze auto-tuning temporarily
            self.frozen_state = True

            # Get emergency recommendations
            metrics = self.autonomy_tracker.get_autonomy_metrics()
            emergency_recommendations = self.model_tuner.suggest_manual_adjustments(metrics.critical_issues)

            # Log emergency state
            logger.critical(f"🚨 Emergency recommendations: {len(emergency_recommendations)}")
            for rec in emergency_recommendations:
                logger.critical(f"   - {rec.get('type')}: {rec.get('reason')}")

            # Auto-unfreeze after 1 hour
            await asyncio.sleep(3600)
            self.frozen_state = False
            logger.info("🔓 Emergency freeze lifted - resuming normal operation")

        except Exception as e:
            logger.error(f"Error in emergency review: {e}")

    def _calculate_tuning_effectiveness(self, tuning_events: List[TuningEvent]) -> float:
        """Calculate effectiveness score for tuning events."""
        try:
            if not tuning_events:
                return 0.5

            # Simple effectiveness calculation based on impact scores
            total_impact = sum(event.impact_score for event in tuning_events)
            avg_probability = sum(event.success_probability for event in tuning_events) / len(tuning_events)

            # Combine impact and probability
            effectiveness = (total_impact * avg_probability) / len(tuning_events)

            return min(1.0, max(0.0, effectiveness))

        except Exception as e:
            logger.error(f"Error calculating tuning effectiveness: {e}")
            return 0.5

    # Human Override and Control Methods

    def enable_autonomy(self):
        """Enable autonomous operation."""
        self.enabled = True
        self.human_override_active = False
        logger.info("🤖 Autonomy enabled")

    def disable_autonomy(self):
        """Disable autonomous operation."""
        self.enabled = False
        self.human_override_active = True
        logger.info("👤 Autonomy disabled - manual control active")

    def freeze_current_state(self):
        """Freeze current strategy state (no auto-tuning)."""
        self.frozen_state = True
        logger.info("🧊 Strategy state frozen - no auto-tuning")

    def unfreeze_state(self):
        """Unfreeze strategy state (resume auto-tuning)."""
        self.frozen_state = False
        logger.info("🔓 Strategy state unfrozen - auto-tuning resumed")

    def get_autonomy_status(self) -> AutonomyStatus:
        """Get current autonomy system status."""
        try:
            # Get latest metrics
            metrics = self.autonomy_tracker.get_autonomy_metrics()
            tuning_summary = self.model_tuner.get_tuning_summary()

            # Calculate performance score
            performance_score = (metrics.overall_win_rate +
                               (1.0 if metrics.recent_trend == 'improving' else 0.5)) / 2

            # Calculate confidence drift
            confidence_drift = 0.0
            if len(self.autonomy_metrics_history) >= 2:
                recent_metrics = self.autonomy_metrics_history[-1]['metrics']
                older_metrics = self.autonomy_metrics_history[-2]['metrics']
                confidence_drift = recent_metrics.get('confidence_calibration', 0.5) - older_metrics.get('confidence_calibration', 0.5)

            # Determine system health
            if metrics.critical_issues:
                system_health = 'CRITICAL'
            elif performance_score < 0.3:
                system_health = 'POOR'
            elif performance_score < 0.6:
                system_health = 'FAIR'
            elif performance_score < 0.8:
                system_health = 'GOOD'
            else:
                system_health = 'EXCELLENT'

            return AutonomyStatus(
                enabled=self.enabled,
                last_analysis_time=self.last_analysis_time,
                last_tuning_time=self.last_tuning_time,
                performance_score=performance_score,
                adaptation_needed=metrics.adaptation_needed,
                critical_issues=metrics.critical_issues,
                tuning_events_24h=tuning_summary.get('recent_tuning_count', 0),
                confidence_drift=confidence_drift,
                system_health=system_health
            )

        except Exception as e:
            logger.error(f"Error getting autonomy status: {e}")
            return AutonomyStatus(
                enabled=False,
                last_analysis_time=0,
                last_tuning_time=0,
                performance_score=0.0,
                adaptation_needed=True,
                critical_issues=['Status calculation error'],
                tuning_events_24h=0,
                confidence_drift=0.0,
                system_health='ERROR'
            )

    async def enhance_llm_decision(self, llm_decision: Dict[str, Any],
                                 model_outputs: Dict[str, Any],
                                 market_data: Dict[str, Any]) -> EnhancedDecision:
        """Enhance LLM decision with final decision enhancer."""
        try:
            enhanced_decision = self.final_decision_enhancer.enhance_decision(
                llm_decision, model_outputs, market_data
            )

            logger.debug(f"LLM decision enhanced: {enhanced_decision.final_decision_enhanced}")

            return enhanced_decision

        except Exception as e:
            logger.error(f"Error enhancing LLM decision: {e}")
            return self.final_decision_enhancer._create_fallback_decision(llm_decision)
