#!/usr/bin/env python3
"""
Test script for Phase 7 Autonomy Loop
Tests the complete autonomous operation system
"""

import asyncio
import logging
import yaml
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import autonomy components
from autonomy.autonomy_loop import Autono<PERSON>Loop
from autonomy.autonomy_tracker import AutonomyTracker
from autonomy.model_tuner import ModelTuner
from autonomy.final_decision_enhancer import FinalDecisionEnhancer

async def test_autonomy_loop():
    """Test the complete autonomy loop system."""
    try:
        logger.info("🚀 Testing Phase 7 Autonomy Loop")
        
        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info("✅ Configuration loaded")
        
        # Initialize autonomy loop
        autonomy_loop = AutonomyLoop(config)
        logger.info("✅ Autonomy Loop initialized")
        
        # Test autonomy status
        logger.info("\n🧪 Test 1: Autonomy Status")
        status = autonomy_loop.get_autonomy_status()
        logger.info(f"✅ Autonomy enabled: {status.enabled}")
        logger.info(f"📊 Performance score: {status.performance_score:.2f}")
        logger.info(f"🏥 System health: {status.system_health}")
        
        # Test decision enhancement
        logger.info("\n🧪 Test 2: Decision Enhancement")
        test_llm_decision = {
            'symbol': 'BTC-USDT',
            'final_decision': 'LONG',
            'confidence': 85,
            'reasoning': 'Strong bullish signals from RSI oversold conditions, VWAP support, and positive momentum indicators.',
            'risk_assessment': 'MEDIUM',
            'market_regime': 'trending_up',
            'timestamp': time.time()
        }
        
        test_model_outputs = {
            'RSI': {'signal': 'BUY', 'confidence': 0.8, 'signal_strength': 0.7},
            'VWAP': {'signal': 'BUY', 'confidence': 0.9, 'signal_strength': 0.8},
            'ORDERFLOW': {'signal': 'NEUTRAL', 'confidence': 0.6, 'signal_strength': 0.5},
            'VOLATILITY': {'signal': 'BUY', 'confidence': 0.7, 'signal_strength': 0.6}
        }
        
        test_market_data = {
            'symbol': 'BTC-USDT',
            'last_price': 45000.0,
            'volume_24h': 1500000000,
            'volatility': 0.012,
            'market_regime': 'trending_up'
        }
        
        enhanced_decision = await autonomy_loop.enhance_llm_decision(
            test_llm_decision, test_model_outputs, test_market_data
        )
        
        logger.info(f"✅ Enhanced decision: {enhanced_decision.final_decision_enhanced}")
        logger.info(f"💬 Commentary: {enhanced_decision.final_commentary}")
        logger.info(f"⭐ Conviction: {'⭐' * enhanced_decision.conviction_score}")
        logger.info(f"🎯 Risk: {enhanced_decision.risk_assessment.value}")
        logger.info(f"🤝 Consensus: {enhanced_decision.model_consensus.value}")
        
        # Test autonomy tracker
        logger.info("\n🧪 Test 3: Autonomy Tracker")
        autonomy_tracker = AutonomyTracker(config)
        
        # Record test performance snapshot
        test_trade_data = {
            'pnl': 150.0,
            'volatility': 0.012,
            'regime_duration': 2.5,
            'regime_outcome': 'trending_up',
            'outcome': 'win'
        }
        
        snapshot_id = autonomy_tracker.record_performance_snapshot(
            'BTC-USDT', test_trade_data, test_model_outputs
        )
        logger.info(f"✅ Performance snapshot recorded: {snapshot_id}")
        
        # Get autonomy metrics
        metrics = autonomy_tracker.get_autonomy_metrics()
        logger.info(f"📊 Overall win rate: {metrics.overall_win_rate:.2f}")
        logger.info(f"📈 Recent trend: {metrics.recent_trend}")
        logger.info(f"🔧 Adaptation needed: {metrics.adaptation_needed}")
        
        # Test model tuner
        logger.info("\n🧪 Test 4: Model Tuner")
        model_tuner = ModelTuner(config)
        
        # Get current configuration
        current_config = model_tuner.get_current_configuration()
        logger.info(f"✅ Current model weights: {current_config['model_weights']}")
        logger.info(f"🎯 Current thresholds: {current_config['trading_thresholds']}")
        
        # Test tuning recommendations
        recommendations = autonomy_tracker.get_tuning_recommendations()
        logger.info(f"💡 Tuning recommendations: {len(recommendations)}")
        
        if recommendations:
            # Apply first recommendation as test
            test_rec = recommendations[0]
            logger.info(f"🔧 Testing recommendation: {test_rec}")
            
            tuning_events = model_tuner.apply_tuning_recommendations([test_rec], 'test')
            if tuning_events:
                logger.info(f"✅ Applied tuning: {tuning_events[0].parameter} "
                          f"{tuning_events[0].old_value:.3f} → {tuning_events[0].new_value:.3f}")
        
        # Test final decision enhancer
        logger.info("\n🧪 Test 5: Final Decision Enhancer")
        enhancer = FinalDecisionEnhancer(config)
        
        enhancement_summary = enhancer.get_enhancement_summary()
        logger.info(f"✅ Enhancement summary: {enhancement_summary}")
        
        logger.info("\n🎉 Phase 7 Autonomy Loop test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

async def test_autonomy_integration():
    """Test autonomy integration with main system components."""
    try:
        logger.info("\n🔧 Testing Autonomy Integration")
        
        # Load config
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Test autonomy loop startup
        logger.info("\n🧪 Testing Autonomy Loop Startup")
        autonomy_loop = AutonomyLoop(config)
        
        # Start autonomy loop
        await autonomy_loop.start_autonomy_loop()
        logger.info("✅ Autonomy loop started successfully")
        
        # Wait a moment for initialization
        await asyncio.sleep(2)
        
        # Test control methods
        logger.info("\n🧪 Testing Control Methods")
        
        # Test freeze/unfreeze
        autonomy_loop.freeze_current_state()
        logger.info("✅ State frozen")
        
        autonomy_loop.unfreeze_state()
        logger.info("✅ State unfrozen")
        
        # Test disable/enable
        autonomy_loop.disable_autonomy()
        logger.info("✅ Autonomy disabled")
        
        autonomy_loop.enable_autonomy()
        logger.info("✅ Autonomy enabled")
        
        # Get comprehensive status
        status = autonomy_loop.get_autonomy_status()
        logger.info(f"✅ Final status - Enabled: {status.enabled}, Health: {status.system_health}")
        
        # Stop autonomy loop
        await autonomy_loop.stop_autonomy_loop()
        logger.info("✅ Autonomy loop stopped successfully")
        
        logger.info("\n🎉 Autonomy integration test completed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Phase 7 Autonomy Loop Tests")
    
    # Ensure data directory exists
    Path('data/autonomy').mkdir(parents=True, exist_ok=True)
    
    # Run tests
    success1 = await test_autonomy_loop()
    success2 = await test_autonomy_integration()
    
    if success1 and success2:
        logger.info("\n🎉 All tests passed! Phase 7 Autonomy Loop is ready.")
        logger.info("\n🤖 The system is now capable of:")
        logger.info("   ✅ Autonomous performance analysis")
        logger.info("   ✅ Self-tuning model parameters")
        logger.info("   ✅ Enhanced decision-making with meta-awareness")
        logger.info("   ✅ Continuous learning and adaptation")
        logger.info("   ✅ Human oversight with override capabilities")
    else:
        logger.error("\n❌ Some tests failed. Check the logs for details.")

if __name__ == "__main__":
    asyncio.run(main())
