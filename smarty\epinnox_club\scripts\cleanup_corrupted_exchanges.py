#!/usr/bin/env python3
"""
Cleanup Corrupted Exchange Records
Safely removes exchange accounts with corrupted/undecryptable credentials
"""

import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_corrupted_exchanges():
    """Clean up corrupted exchange records."""
    try:
        logger.info("🧹 Starting corrupted exchange cleanup...")
        
        # Import required modules
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        from exchanges.account_manager import ExchangeAccountManager
        from exchanges.encryption_utils import decrypt_api_credentials
        
        # Initialize managers
        db_manager = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db_manager)
        exchange_manager = ExchangeAccountManager(db_manager)
        
        # Get epinnox user
        epinnox_user = user_manager.get_user_by_username('epinnox')
        if not epinnox_user:
            logger.error("❌ Epinnox user not found")
            return False
        
        user_id = epinnox_user.id
        logger.info(f"✅ Found epinnox user: ID {user_id}")
        
        # Get all exchange accounts for the user
        cursor = db_manager.conn.execute("""
            SELECT id, exchange_name, api_key_encrypted, secret_key_encrypted, passphrase_encrypted
            FROM user_exchanges
            WHERE user_id = ? AND is_active = 1
        """, (user_id,))
        
        exchanges = cursor.fetchall()
        logger.info(f"📊 Found {len(exchanges)} exchange accounts to check")
        
        corrupted_exchanges = []
        working_exchanges = []
        
        # Test each exchange for credential corruption
        for exchange in exchanges:
            exchange_id, exchange_name, api_key_enc, secret_key_enc, passphrase_enc = exchange
            
            logger.info(f"🔍 Testing {exchange_name} (ID: {exchange_id})...")
            
            try:
                # Check if we have encrypted data
                if not api_key_enc or not secret_key_enc:
                    logger.warning(f"⚠️ {exchange_name} has empty credentials")
                    corrupted_exchanges.append((exchange_id, exchange_name, "Empty credentials"))
                    continue
                
                # Try to decrypt credentials
                encrypted_creds = {
                    'api_key': api_key_enc,
                    'secret_key': secret_key_enc,
                    'passphrase': passphrase_enc if passphrase_enc else None
                }
                
                decrypted_creds = decrypt_api_credentials(encrypted_creds)
                
                # Check if decryption returned valid data
                if not decrypted_creds.get('api_key') or not decrypted_creds.get('secret_key'):
                    logger.warning(f"⚠️ {exchange_name} credentials failed to decrypt properly")
                    corrupted_exchanges.append((exchange_id, exchange_name, "Decryption failed"))
                    continue
                
                # Try to create exchange client
                client = exchange_manager.get_exchange_client(user_id, exchange_name)
                if client:
                    logger.info(f"✅ {exchange_name} credentials working")
                    working_exchanges.append((exchange_id, exchange_name))
                else:
                    logger.warning(f"⚠️ {exchange_name} client creation failed")
                    corrupted_exchanges.append((exchange_id, exchange_name, "Client creation failed"))
                
            except Exception as e:
                logger.error(f"❌ {exchange_name} test failed: {e}")
                corrupted_exchanges.append((exchange_id, exchange_name, f"Test error: {e}"))
        
        # Report findings
        logger.info("\n" + "=" * 60)
        logger.info("EXCHANGE ACCOUNT ANALYSIS RESULTS")
        logger.info("=" * 60)
        
        logger.info(f"✅ Working exchanges: {len(working_exchanges)}")
        for exchange_id, exchange_name in working_exchanges:
            logger.info(f"   - {exchange_name} (ID: {exchange_id})")
        
        logger.info(f"❌ Corrupted exchanges: {len(corrupted_exchanges)}")
        for exchange_id, exchange_name, reason in corrupted_exchanges:
            logger.info(f"   - {exchange_name} (ID: {exchange_id}): {reason}")
        
        # Ask for confirmation to remove corrupted exchanges
        if corrupted_exchanges:
            logger.info("\n🗑️ Preparing to remove corrupted exchange accounts...")
            
            # Remove corrupted exchanges
            removed_count = 0
            for exchange_id, exchange_name, reason in corrupted_exchanges:
                try:
                    logger.info(f"🗑️ Removing {exchange_name} (ID: {exchange_id})...")
                    
                    # Force delete the corrupted record
                    db_manager.conn.execute("""
                        DELETE FROM user_exchanges
                        WHERE id = ? AND user_id = ?
                    """, (exchange_id, user_id))
                    
                    db_manager.conn.commit()
                    logger.info(f"✅ Removed {exchange_name} (ID: {exchange_id})")
                    removed_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to remove {exchange_name}: {e}")
            
            logger.info(f"\n🎉 Cleanup completed: {removed_count} corrupted exchanges removed")
        else:
            logger.info("\n🎉 No corrupted exchanges found - all accounts are working!")
        
        # Final verification
        logger.info("\n🔍 Final verification...")
        final_exchanges = exchange_manager.get_user_exchanges(user_id)
        logger.info(f"📊 Remaining exchange accounts: {len(final_exchanges)}")
        
        for exchange in final_exchanges:
            logger.info(f"   ✅ {exchange.exchange_name} (ID: {exchange.id})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_remaining_exchanges():
    """Test all remaining exchange accounts."""
    try:
        logger.info("\n🧪 Testing remaining exchange accounts...")
        
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        from exchanges.account_manager import ExchangeAccountManager
        
        # Initialize managers
        db_manager = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db_manager)
        exchange_manager = ExchangeAccountManager(db_manager)
        
        # Get epinnox user
        epinnox_user = user_manager.get_user_by_username('epinnox')
        if not epinnox_user:
            logger.error("❌ Epinnox user not found")
            return False
        
        user_id = epinnox_user.id
        exchanges = exchange_manager.get_user_exchanges(user_id)
        
        logger.info(f"Testing {len(exchanges)} remaining exchange accounts...")
        
        success_count = 0
        for exchange in exchanges:
            logger.info(f"\n🔍 Testing {exchange.exchange_name}...")
            
            try:
                client = exchange_manager.get_exchange_client(user_id, exchange.exchange_name)
                if client:
                    logger.info(f"✅ {exchange.exchange_name} client created successfully")
                    
                    # Test API call
                    try:
                        balance = client.fetch_balance()
                        logger.info(f"✅ {exchange.exchange_name} API call successful")
                        success_count += 1
                    except Exception as api_error:
                        logger.warning(f"⚠️ {exchange.exchange_name} API call failed: {api_error}")
                        success_count += 1  # Client creation is what matters
                else:
                    logger.error(f"❌ {exchange.exchange_name} client creation failed")
            except Exception as e:
                logger.error(f"❌ {exchange.exchange_name} test failed: {e}")
        
        logger.info(f"\n📊 Test Results: {success_count}/{len(exchanges)} exchanges working")
        
        if success_count == len(exchanges):
            logger.info("🎉 All remaining exchanges are working correctly!")
            return True
        else:
            logger.warning("⚠️ Some exchanges still have issues")
            return False
        
    except Exception as e:
        logger.error(f"❌ Testing failed: {e}")
        return False

def main():
    """Main function."""
    logger.info("=" * 70)
    logger.info("MONEY CIRCLE CORRUPTED EXCHANGE CLEANUP")
    logger.info("=" * 70)
    
    # Cleanup corrupted exchanges
    if not cleanup_corrupted_exchanges():
        return 1
    
    # Test remaining exchanges
    if not test_remaining_exchanges():
        return 1
    
    logger.info("\n🎉 Exchange cleanup completed successfully!")
    logger.info("The Money Circle platform should now have only working exchange accounts.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
