#!/usr/bin/env python3
"""
Fix authentication issues by updating password hashes to use bcrypt.
"""

import sqlite3
import bcrypt
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def hash_password_bcrypt(password):
    """Hash password using bcrypt."""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def fix_user_passwords():
    """Fix all user passwords to use bcrypt hashing."""
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # Get all users
        users = conn.execute("SELECT id, username FROM users").fetchall()
        
        # Hash the standard password
        correct_hash = hash_password_bcrypt("securepass123")
        
        # Update all users with the correct bcrypt hash
        for user in users:
            conn.execute("""
                UPDATE users 
                SET hashed_password = ?, agreement_accepted = 1, email_verified = 1
                WHERE id = ?
            """, (correct_hash, user['id']))
            
            logger.info(f"Updated password for user: {user['username']}")
        
        conn.commit()
        conn.close()
        
        logger.info("✅ All user passwords updated with bcrypt hashing")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing passwords: {e}")
        return False

def verify_authentication():
    """Verify that authentication works correctly."""
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # Test epinnox user
        epinnox = conn.execute("SELECT * FROM users WHERE username = 'epinnox'").fetchone()
        if epinnox:
            # Test password verification
            test_password = "securepass123"
            stored_hash = epinnox['hashed_password']
            
            # Verify with bcrypt
            is_valid = bcrypt.checkpw(test_password.encode('utf-8'), stored_hash.encode('utf-8'))
            
            logger.info(f"epinnox user found: {epinnox['username']}")
            logger.info(f"Password verification: {is_valid}")
            
            if is_valid:
                logger.info("✅ Authentication should work now!")
            else:
                logger.error("❌ Authentication still failing")
        
        # Test demo user
        demo_user = conn.execute("SELECT * FROM users WHERE username = 'trader_alex'").fetchone()
        if demo_user:
            # Test password verification
            test_password = "securepass123"
            stored_hash = demo_user['hashed_password']
            
            # Verify with bcrypt
            is_valid = bcrypt.checkpw(test_password.encode('utf-8'), stored_hash.encode('utf-8'))
            
            logger.info(f"Demo user found: {demo_user['username']}")
            logger.info(f"Password verification: {is_valid}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error verifying authentication: {e}")
        return False

def main():
    """Main function to fix authentication."""
    print("🔧 FIXING MONEY CIRCLE AUTHENTICATION")
    print("=" * 50)
    
    # Step 1: Fix user passwords
    print("Step 1: Updating user passwords with bcrypt...")
    if not fix_user_passwords():
        print("❌ Failed to fix passwords")
        return 1
    
    # Step 2: Verify authentication
    print("\nStep 2: Verifying authentication...")
    if not verify_authentication():
        print("❌ Authentication verification failed")
        return 1
    
    print("\n✅ AUTHENTICATION FIXED!")
    print("=" * 50)
    print("All users now use bcrypt password hashing")
    print("Login credentials:")
    print("  Username: epinnox (or any demo user)")
    print("  Password: securepass123")
    print("\nTry logging in at: http://localhost:8084/login")
    
    return 0

if __name__ == "__main__":
    exit(main())
