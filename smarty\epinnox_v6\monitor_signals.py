#!/usr/bin/env python3
"""
Signal Monitor - Check if signals are being generated and stored
"""

import asyncio
import json
import time
from pathlib import Path
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(str(Path(__file__).parent))

from storage.live_store import LiveDataStore
import yaml

async def monitor_signals():
    """Monitor signals in the data store."""
    
    # Load configuration
    config_path = Path(__file__).parent / "config" / "strategy.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Initialize data store
    data_store = LiveDataStore(config)
    
    print("🔍 Signal Monitor Started")
    print("=" * 50)
    
    last_signal_count = 0
    
    while True:
        try:
            # Get signals for DOGE/USDT:USDT
            signals = data_store.get_signals('DOGE/USDT:USDT', limit=10)
            signal_count = len(signals)
            
            # Get all signals from timeline
            all_signals = data_store.get_signals(None, limit=20)
            total_count = len(all_signals)
            
            # Get dashboard data
            dashboard_data = data_store.get_dashboard_data('DOGE/USDT:USDT')
            
            print(f"\n⏰ {time.strftime('%H:%M:%S')}")
            print(f"📊 DOGE/USDT:USDT Signals: {signal_count}")
            print(f"📈 Total Signals: {total_count}")
            print(f"🎯 Dashboard Signals: {len(dashboard_data.get('recent_signals', []))}")
            print(f"🧠 Dashboard LLM Decisions: {len(dashboard_data.get('llm_decisions', []))}")
            
            # Show new signals
            if signal_count > last_signal_count:
                print(f"🆕 NEW SIGNALS DETECTED! (+{signal_count - last_signal_count})")
                for i, signal in enumerate(signals[-3:]):  # Show last 3 signals
                    timestamp = signal.get('timestamp', 0)
                    if isinstance(timestamp, (int, float)):
                        time_str = time.strftime('%H:%M:%S', time.localtime(timestamp/1000))
                    else:
                        time_str = str(timestamp)
                    
                    print(f"   📈 {time_str}: {signal.get('action', 'UNKNOWN')} "
                          f"(confidence: {signal.get('confidence', 0):.2%})")
                
                last_signal_count = signal_count
            
            # Show latest signal details
            if signals:
                latest = signals[-1]
                print(f"🔥 Latest Signal: {latest.get('action', 'UNKNOWN')} "
                      f"@ ${latest.get('price', 0):.4f} "
                      f"(confidence: {latest.get('confidence', 0):.2%})")
            
            await asyncio.sleep(3)  # Check every 3 seconds
            
        except KeyboardInterrupt:
            print("\n👋 Signal monitor stopped")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(monitor_signals())
