#!/usr/bin/env python3
"""
Fix database schema inconsistencies
"""

import sqlite3
import bcrypt
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_database():
    """Fix database schema inconsistencies."""
    db_path = 'data/money_circle.db'
    
    try:
        # Ensure data directory exists
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== FIXING DATABASE SCHEMA ===")
        
        # Drop and recreate user_sessions table with correct schema
        print("Fixing user_sessions table...")
        cursor.execute("DROP TABLE IF EXISTS user_sessions")
        cursor.execute("""
            CREATE TABLE user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_id TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        """)
        
        # Drop and recreate notifications table with correct schema
        print("Fixing notifications table...")
        cursor.execute("DROP TABLE IF EXISTS notifications")
        cursor.execute("""
            CREATE TABLE notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                data TEXT,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)")
        
        # Ensure epinnox user exists
        cursor.execute("SELECT id, username FROM users WHERE username = 'epinnox'")
        user = cursor.fetchone()
        if not user:
            print("Creating epinnox admin user...")
            hashed_password = bcrypt.hashpw('securepass123'.encode('utf-8'), bcrypt.gensalt())
            cursor.execute("""
                INSERT INTO users (username, email, hashed_password, role, email_verified, agreement_accepted, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, ('epinnox', '<EMAIL>', hashed_password, 'admin', True, True, True))
            print("✅ Epinnox admin user created!")
        else:
            print(f"✅ Epinnox user already exists (ID: {user[0]})")
        
        conn.commit()
        conn.close()
        
        print("✅ Database schema fixed successfully!")
        
    except Exception as e:
        print(f"❌ Database fix failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_database()
