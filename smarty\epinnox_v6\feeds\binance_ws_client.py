#!/usr/bin/env python3
"""
Binance WebSocket Client for Real-Time Market Data (Backup for HTX)
Epinnox V6 - Standalone AI Strategy Tuner
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Callable, Optional, Any
import websockets
from datetime import datetime

logger = logging.getLogger(__name__)

class BinanceWebSocketClient:
    """
    Real-time Binance WebSocket client for market data streaming.
    Serves as backup when HTX is unavailable.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        # Use correct Binance WebSocket base URL
        self.ws_url = "wss://stream.binance.com:9443/ws"
        self.symbols = config['symbols']['enabled']

        # Connection management
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_interval = 5

        # Data handlers
        self.trade_handler: Optional[Callable] = None
        self.depth_handler: Optional[Callable] = None
        self.error_handler: Optional[Callable] = None

        # Statistics
        self.stats = {
            'messages_received': 0,
            'trades_processed': 0,
            'depth_updates': 0,
            'connection_time': None,
            'last_message_time': None
        }

        logger.info("Binance WebSocket Client initialized")

    def set_trade_handler(self, handler: Callable[[Dict], None]):
        """Set handler for trade data."""
        self.trade_handler = handler
        logger.info("Trade handler registered")

    def set_depth_handler(self, handler: Callable[[Dict], None]):
        """Set handler for order book depth data."""
        self.depth_handler = handler
        logger.info("Depth handler registered")

    def set_error_handler(self, handler: Callable[[Exception], None]):
        """Set handler for connection errors."""
        self.error_handler = handler
        logger.info("Error handler registered")

    async def connect(self) -> bool:
        """
        Establish WebSocket connection to Binance.
        Returns True if successful, False otherwise.
        """
        try:
            logger.info(f"Connecting to Binance WebSocket: {self.ws_url}")

            # Use individual stream connections instead of combined streams
            # Connect to DOGE/USDT specifically (our primary trading symbol)
            binance_symbol = 'dogeusdt'  # Hardcode DOGE/USDT for now

            # Connect to individual stream (more reliable)
            stream_url = f"wss://stream.binance.com:9443/ws/{binance_symbol}@trade"
            logger.info(f"🔗 Connecting to Binance stream: {stream_url}")

            self.websocket = await websockets.connect(
                stream_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )

            self.is_connected = True
            self.reconnect_attempts = 0
            self.stats['connection_time'] = datetime.now()

            logger.info("✅ Connected to Binance WebSocket successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to connect to Binance WebSocket: {e}")
            self.is_connected = False
            if self.error_handler:
                try:
                    await self.error_handler(e)
                except Exception:
                    pass
            return False

    async def listen(self):
        """
        Main listening loop for WebSocket messages.
        Handles incoming data and routes to appropriate handlers.
        """
        if not self.is_connected or not self.websocket:
            logger.error("WebSocket not connected")
            return

        logger.info("🎧 Starting Binance WebSocket message listener")

        try:
            async for message in self.websocket:
                await self._process_message(message)

        except websockets.exceptions.ConnectionClosed:
            logger.warning("Binance WebSocket connection closed")
            self.is_connected = False
            await self._handle_disconnect()

        except Exception as e:
            logger.error(f"Error in Binance WebSocket listener: {e}")
            self.is_connected = False
            if self.error_handler:
                try:
                    await self.error_handler(e)
                except Exception:
                    pass

    async def _process_message(self, raw_message):
        """Process incoming WebSocket message."""
        try:
            data = json.loads(raw_message)
            self.stats['messages_received'] += 1
            self.stats['last_message_time'] = datetime.now()

            # Handle individual stream format (direct trade data)
            if 'e' in data and data['e'] == 'trade':
                # Direct trade event
                await self._handle_trade_data(data, None)
            elif 'stream' in data and 'data' in data:
                # Combined stream format (fallback)
                stream = data['stream']
                payload = data['data']

                if '@trade' in stream:
                    await self._handle_trade_data(payload, stream)
                elif '@depth' in stream:
                    await self._handle_depth_data(payload, stream)

        except Exception as e:
            logger.error(f"Error processing Binance message: {e}")

    async def _handle_trade_data(self, data: Dict, stream: str = None):
        """Handle trade data from Binance."""
        if not self.trade_handler:
            return

        try:
            # Handle both individual stream and combined stream formats
            if stream:
                # Combined stream format
                symbol_part = stream.split('@')[0]
                symbol = self._normalize_symbol(symbol_part)
            else:
                # Individual stream format - extract from data
                symbol = self._normalize_symbol(data.get('s', ''))

            trade_data = {
                'symbol': symbol,
                'price': float(data['p']),
                'quantity': float(data['q']),
                'side': 'buy' if data['m'] == False else 'sell',  # m=true means buyer is market maker
                'timestamp': data['T'],  # Trade time
                'trade_id': str(data['t'])
            }

            await self.trade_handler(trade_data)
            self.stats['trades_processed'] += 1

        except Exception as e:
            logger.error(f"Error handling Binance trade data: {e}")

    async def _handle_depth_data(self, data: Dict, stream: str):
        """Handle order book depth data from Binance."""
        if not self.depth_handler:
            return

        try:
            # Extract symbol from stream
            symbol_part = stream.split('@')[0]
            symbol = self._normalize_symbol(symbol_part)

            depth_data = {
                'symbol': symbol,
                'bids': [[float(bid[0]), float(bid[1])] for bid in data.get('bids', [])],
                'asks': [[float(ask[0]), float(ask[1])] for ask in data.get('asks', [])],
                'timestamp': data.get('lastUpdateId', int(time.time() * 1000))
            }

            await self.depth_handler(depth_data)
            self.stats['depth_updates'] += 1

        except Exception as e:
            logger.error(f"Error handling Binance depth data: {e}")

    def _normalize_symbol(self, binance_symbol: str) -> str:
        """Convert Binance symbol format to CCXT standard format."""
        # btcusdt -> DOGE/USDT:USDT (for futures compatibility)
        symbol_map = {
            'btcusdt': 'BTC/USDT:USDT',
            'ethusdt': 'ETH/USDT:USDT',
            'dogeusdt': 'DOGE/USDT:USDT',
            'solusdt': 'SOL/USDT:USDT',
            'adausdt': 'ADA/USDT:USDT'
        }

        # Handle both formats: DOGEUSDT and dogeusdt
        normalized = binance_symbol.lower()
        if normalized in symbol_map:
            return symbol_map[normalized]

        # Fallback: try to parse manually
        if 'usdt' in normalized:
            base = normalized.replace('usdt', '').upper()
            return f"{base}/USDT:USDT"

        return binance_symbol.upper()

    async def _handle_disconnect(self):
        """Handle WebSocket disconnection and attempt reconnection."""
        self.is_connected = False

        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.warning(f"🔄 Attempting Binance reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")

            await asyncio.sleep(self.reconnect_interval)

            if await self.connect():
                # Restart listening
                asyncio.create_task(self.listen())
            else:
                await self._handle_disconnect()
        else:
            logger.error("❌ Max Binance reconnection attempts reached. Giving up.")
            if self.error_handler:
                try:
                    await self.error_handler(Exception("Max reconnection attempts reached"))
                except Exception:
                    pass

    async def disconnect(self):
        """Gracefully disconnect from WebSocket."""
        if self.websocket and self.is_connected:
            logger.info("🔌 Disconnecting from Binance WebSocket")
            await self.websocket.close()
            self.is_connected = False

    def get_stats(self) -> Dict[str, Any]:
        """Get connection and processing statistics."""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'subscribed_symbols': len(self.symbols),
            'exchange': 'Binance'
        }
