"""
System Health Monitor for Epinnox V6 AI Strategy Tuner
Comprehensive monitoring and optimization utilities.
"""

import asyncio
import logging
import time
import psutil
import gc
from typing import Dict, Any, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics."""
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    active_connections: int
    signal_generation_rate: float
    error_rate: float
    uptime_seconds: float
    timestamp: float

class SystemMonitor:
    """
    Comprehensive system health monitoring and optimization.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.start_time = time.time()
        self.metrics_history: List[SystemMetrics] = []
        self.error_count = 0
        self.signal_count = 0
        self.last_signal_time = 0
        self.connection_count = 0
        
        # Performance thresholds
        self.cpu_threshold = 80.0  # %
        self.memory_threshold = 80.0  # %
        self.error_rate_threshold = 0.1  # 10%
        
        logger.info("System Monitor initialized")
    
    def record_signal(self):
        """Record a signal generation event."""
        self.signal_count += 1
        self.last_signal_time = time.time()
    
    def record_error(self):
        """Record an error event."""
        self.error_count += 1
    
    def record_connection(self, delta: int = 1):
        """Record connection change."""
        self.connection_count += delta
        if self.connection_count < 0:
            self.connection_count = 0
    
    def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        try:
            # CPU and memory usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Calculate signal generation rate (signals per minute)
            current_time = time.time()
            uptime = current_time - self.start_time
            signal_rate = (self.signal_count / max(uptime / 60, 1)) if uptime > 0 else 0
            
            # Calculate error rate
            total_events = self.signal_count + self.error_count
            error_rate = (self.error_count / max(total_events, 1)) if total_events > 0 else 0
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                active_connections=self.connection_count,
                signal_generation_rate=signal_rate,
                error_rate=error_rate,
                uptime_seconds=uptime,
                timestamp=current_time
            )
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return SystemMetrics(0, 0, 0, 0, 0, 0, 0, time.time())
    
    def check_system_health(self) -> Dict[str, Any]:
        """Comprehensive system health check."""
        metrics = self.get_current_metrics()
        
        # Store metrics history (keep last 100 entries)
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > 100:
            self.metrics_history.pop(0)
        
        # Health assessment
        health_status = "HEALTHY"
        warnings = []
        
        # Check CPU usage
        if metrics.cpu_percent > self.cpu_threshold:
            health_status = "WARNING"
            warnings.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        # Check memory usage
        if metrics.memory_percent > self.memory_threshold:
            health_status = "WARNING"
            warnings.append(f"High memory usage: {metrics.memory_percent:.1f}%")
        
        # Check error rate
        if metrics.error_rate > self.error_rate_threshold:
            health_status = "WARNING"
            warnings.append(f"High error rate: {metrics.error_rate:.1%}")
        
        # Check signal generation
        if metrics.signal_generation_rate < 0.1 and metrics.uptime_seconds > 300:  # Less than 0.1 signals/min after 5 minutes
            health_status = "WARNING"
            warnings.append("Low signal generation rate")
        
        return {
            'status': health_status,
            'metrics': {
                'cpu_percent': metrics.cpu_percent,
                'memory_percent': metrics.memory_percent,
                'memory_mb': metrics.memory_mb,
                'active_connections': metrics.active_connections,
                'signal_rate_per_minute': metrics.signal_generation_rate,
                'error_rate': metrics.error_rate,
                'uptime_hours': metrics.uptime_seconds / 3600
            },
            'warnings': warnings,
            'timestamp': metrics.timestamp
        }
    
    def optimize_performance(self):
        """Perform automatic performance optimizations."""
        try:
            # Force garbage collection
            collected = gc.collect()
            if collected > 0:
                logger.info(f"Garbage collection freed {collected} objects")
            
            # Check memory usage and warn if high
            metrics = self.get_current_metrics()
            if metrics.memory_percent > 70:
                logger.warning(f"Memory usage high: {metrics.memory_percent:.1f}%")
                
        except Exception as e:
            logger.error(f"Error during performance optimization: {e}")
    
    async def start_monitoring(self, interval: int = 60):
        """Start background monitoring task."""
        logger.info(f"Starting system monitoring (interval: {interval}s)")
        
        while True:
            try:
                await asyncio.sleep(interval)
                
                # Get health status
                health = self.check_system_health()
                
                # Log health status
                if health['status'] == 'HEALTHY':
                    logger.info(f"System health: {health['status']} - "
                              f"CPU: {health['metrics']['cpu_percent']:.1f}%, "
                              f"Memory: {health['metrics']['memory_percent']:.1f}%, "
                              f"Signals/min: {health['metrics']['signal_rate_per_minute']:.1f}")
                else:
                    logger.warning(f"System health: {health['status']} - Warnings: {', '.join(health['warnings'])}")
                
                # Perform optimizations if needed
                if health['metrics']['memory_percent'] > 70:
                    self.optimize_performance()
                    
            except Exception as e:
                logger.error(f"Error in system monitoring: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        if not self.metrics_history:
            return {'error': 'No metrics available'}
        
        # Calculate averages from recent metrics
        recent_metrics = self.metrics_history[-10:] if len(self.metrics_history) >= 10 else self.metrics_history
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_signal_rate = sum(m.signal_generation_rate for m in recent_metrics) / len(recent_metrics)
        
        current_metrics = self.get_current_metrics()
        
        return {
            'current': {
                'cpu_percent': current_metrics.cpu_percent,
                'memory_percent': current_metrics.memory_percent,
                'memory_mb': current_metrics.memory_mb,
                'active_connections': current_metrics.active_connections,
                'uptime_hours': current_metrics.uptime_seconds / 3600
            },
            'averages': {
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory,
                'signal_rate_per_minute': avg_signal_rate
            },
            'totals': {
                'signals_generated': self.signal_count,
                'errors_recorded': self.error_count,
                'error_rate': current_metrics.error_rate
            },
            'timestamp': time.time()
        }
