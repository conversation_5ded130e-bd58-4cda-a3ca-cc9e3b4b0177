#!/usr/bin/env python3
"""
Test script to verify Bybit market data processing fix.
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from market_data.advanced_market_data_manager import AdvancedMarketDataManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BybitTestManager:
    """Test manager for Bybit market data processing."""

    def __init__(self):
        self.test_symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        self.received_data = {}
        self.error_count = 0
        self.success_count = 0

    async def test_bybit_processing(self):
        """Test Bybit data processing with mock data."""
        print("🧪 TESTING BYBIT MARKET DATA PROCESSING")
        print("=" * 60)

        # Initialize market data manager
        config = {
            'symbols': self.test_symbols,
            'update_interval': 1.0,
            'max_trade_history': 100
        }
        manager = AdvancedMarketDataManager(config)

        # Test 1: Spot ticker data (no bid1Price/ask1Price)
        print("\n1️⃣ Testing Spot ticker data processing...")
        spot_data = {
            "topic": "tickers.BTCUSDT",
            "ts": 1673853746003,
            "type": "snapshot",
            "cs": 2588407389,
            "data": {
                "symbol": "BTCUSDT",
                "lastPrice": "21109.77",
                "highPrice24h": "21426.99",
                "lowPrice24h": "20575",
                "prevPrice24h": "20704.93",
                "volume24h": "6780.866843",
                "turnover24h": "141946527.22907118",
                "price24hPcnt": "0.0196",
                "usdIndexPrice": "21120.2400136"
            }
        }

        try:
            await manager._process_bybit_data(spot_data)
            print("✅ Spot data processed successfully")
            self.success_count += 1

            # Check if data was stored correctly
            if 'BTC/USDT' in manager.market_ticks:
                tick = manager.market_ticks['BTC/USDT']
                print(f"   📊 Price: ${tick.price:.2f}")
                print(f"   📊 Bid: ${tick.bid:.2f} (estimated)")
                print(f"   📊 Ask: ${tick.ask:.2f} (estimated)")
                print(f"   📊 Volume 24h: {tick.volume_24h:.2f}")
                print(f"   📊 Change 24h: {tick.change_24h_percent:.2f}%")
            else:
                print("❌ Data not stored in market_ticks")
                self.error_count += 1

        except Exception as e:
            print(f"❌ Error processing spot data: {e}")
            self.error_count += 1

        # Test 2: Linear/Inverse ticker data (with bid1Price/ask1Price)
        print("\n2️⃣ Testing Linear/Inverse ticker data processing...")
        linear_data = {
            "topic": "tickers.BTCUSDT",
            "type": "snapshot",
            "data": {
                "symbol": "BTCUSDT",
                "tickDirection": "PlusTick",
                "price24hPcnt": "0.017103",
                "lastPrice": "17216.00",
                "prevPrice24h": "16926.50",
                "highPrice24h": "17281.50",
                "lowPrice24h": "16915.00",
                "prevPrice1h": "17238.00",
                "markPrice": "17217.33",
                "indexPrice": "17227.36",
                "openInterest": "68744.761",
                "openInterestValue": "1183601235.91",
                "turnover24h": "1570383121.943499",
                "volume24h": "91705.276",
                "nextFundingTime": "1673280000000",
                "fundingRate": "-0.000212",
                "bid1Price": "17215.50",
                "bid1Size": "84.489",
                "ask1Price": "17216.00",
                "ask1Size": "83.020"
            },
            "cs": 24987956059,
            "ts": 1673272861686
        }

        try:
            await manager._process_bybit_data(linear_data)
            print("✅ Linear/Inverse data processed successfully")
            self.success_count += 1

            # Check if data was stored correctly
            if 'BTC/USDT' in manager.market_ticks:
                tick = manager.market_ticks['BTC/USDT']
                print(f"   📊 Price: ${tick.price:.2f}")
                print(f"   📊 Bid: ${tick.bid:.2f} (from bid1Price)")
                print(f"   📊 Ask: ${tick.ask:.2f} (from ask1Price)")
                print(f"   📊 Volume 24h: {tick.volume_24h:.2f}")
                print(f"   📊 Change 24h: {tick.change_24h_percent:.2f}%")
            else:
                print("❌ Data not stored in market_ticks")
                self.error_count += 1

        except Exception as e:
            print(f"❌ Error processing linear data: {e}")
            self.error_count += 1

        # Test 3: Invalid data handling
        print("\n3️⃣ Testing invalid data handling...")
        invalid_data = {
            "topic": "tickers.BTCUSDT",
            "type": "snapshot",
            "data": {
                "symbol": "BTCUSDT",
                # Missing lastPrice - should be handled gracefully
                "volume24h": "1000",
                "price24hPcnt": "0.01"
            }
        }

        try:
            await manager._process_bybit_data(invalid_data)
            print("✅ Invalid data handled gracefully (no crash)")
            self.success_count += 1
        except Exception as e:
            print(f"❌ Error handling invalid data: {e}")
            self.error_count += 1

        # Test 4: Empty/malformed data
        print("\n4️⃣ Testing empty data handling...")
        empty_data = {}

        try:
            await manager._process_bybit_data(empty_data)
            print("✅ Empty data handled gracefully")
            self.success_count += 1
        except Exception as e:
            print(f"❌ Error handling empty data: {e}")
            self.error_count += 1

        # Test 5: Non-USDT pairs (should be ignored)
        print("\n5️⃣ Testing non-USDT pair filtering...")
        non_usdt_data = {
            "topic": "tickers.BTCETH",
            "type": "snapshot",
            "data": {
                "symbol": "BTCETH",
                "lastPrice": "15.5",
                "volume24h": "1000",
                "price24hPcnt": "0.01"
            }
        }

        try:
            await manager._process_bybit_data(non_usdt_data)
            print("✅ Non-USDT pair filtered correctly")
            self.success_count += 1
        except Exception as e:
            print(f"❌ Error filtering non-USDT pair: {e}")
            self.error_count += 1

        # Print test results
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS")
        print("=" * 60)
        print(f"✅ Successful tests: {self.success_count}")
        print(f"❌ Failed tests: {self.error_count}")
        print(f"🎯 Success rate: {(self.success_count / (self.success_count + self.error_count)) * 100:.1f}%")

        if self.error_count == 0:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Bybit market data processing is working correctly")
            print("✅ No more 'bid1Price' KeyError should occur")
            print("✅ Proper fallback mechanisms are in place")
            return True
        else:
            print(f"\n⚠️ {self.error_count} tests failed")
            print("❌ Some issues remain in Bybit processing")
            return False

async def test_live_connection():
    """Test live connection to Bybit WebSocket (optional)."""
    print("\n🌐 TESTING LIVE BYBIT CONNECTION")
    print("=" * 60)

    try:
        import websockets

        url = "wss://stream.bybit.com/v5/public/spot"
        print(f"🔄 Connecting to {url}...")

        async with websockets.connect(url, ping_interval=20, ping_timeout=10) as websocket:
            print("✅ Connected to Bybit WebSocket successfully")

            # Subscribe to BTC ticker
            subscribe_msg = {
                "op": "subscribe",
                "args": ["tickers.BTCUSDT"]
            }
            await websocket.send(json.dumps(subscribe_msg))
            print("📡 Subscription message sent")

            # Wait for a few messages
            message_count = 0
            async for message in websocket:
                try:
                    data = json.loads(message)
                    print(f"📨 Received message {message_count + 1}: {data.get('op', data.get('topic', 'data'))}")

                    if data.get('topic') == 'tickers.BTCUSDT':
                        tick_data = data.get('data', {})
                        print(f"   💰 BTC Price: ${tick_data.get('lastPrice', 'N/A')}")
                        print(f"   📊 Available fields: {list(tick_data.keys())}")
                        break

                    message_count += 1
                    if message_count >= 5:  # Limit test messages
                        break

                except json.JSONDecodeError:
                    print("❌ Invalid JSON received")
                except Exception as e:
                    print(f"❌ Error processing message: {e}")

            print("✅ Live connection test completed")
            return True

    except Exception as e:
        print(f"❌ Live connection test failed: {e}")
        print("ℹ️ This is normal if there are network restrictions")
        return False

async def main():
    """Main test function."""
    print("🔧 BYBIT MARKET DATA FIX VERIFICATION")
    print("=" * 60)
    print("Testing the fix for 'bid1Price' KeyError in Bybit processing")
    print()

    # Run processing tests
    test_manager = BybitTestManager()
    processing_success = await test_manager.test_bybit_processing()

    # Optionally test live connection
    print("\n" + "=" * 60)
    user_input = input("🌐 Test live Bybit connection? (y/N): ").strip().lower()

    if user_input == 'y':
        live_success = await test_live_connection()
    else:
        print("⏭️ Skipping live connection test")
        live_success = True

    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL VERIFICATION SUMMARY")
    print("=" * 60)

    if processing_success and live_success:
        print("🎉 BYBIT FIX VERIFICATION SUCCESSFUL!")
        print("✅ All processing tests passed")
        print("✅ Error handling is robust")
        print("✅ Fallback mechanisms work correctly")
        print("\n🚀 The Smart Trader system should no longer experience")
        print("   'bid1Price' KeyError messages from Bybit!")
        return 0
    else:
        print("⚠️ VERIFICATION INCOMPLETE")
        if not processing_success:
            print("❌ Processing tests failed")
        if not live_success:
            print("❌ Live connection test failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
