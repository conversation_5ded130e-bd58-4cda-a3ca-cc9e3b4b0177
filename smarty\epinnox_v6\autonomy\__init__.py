#!/usr/bin/env python3
"""
Phase 7: Epinnox Autonomy Loop
Self-Tuning. Self-Evolving. Always Trading.

This module implements the complete autonomy loop that allows the strategy to:
- Evaluate its own long-term performance
- Adjust model weights or thresholds automatically
- Recalibrate based on market regime or trade clusters
- Run with optional human override, but no dependence
"""

from .autonomy_tracker import AutonomyTracker
from .model_tuner import ModelTuner
from .autonomy_loop import <PERSON>no<PERSON><PERSON><PERSON>
from .final_decision_enhancer import FinalDecisionEnhancer
from .market_intelligence import AutonomousMarketIntelligence
from .autonomous_trader import AutonomousTrader

__all__ = [
    'AutonomyTracker',
    'ModelTuner',
    'AutonomyLoop',
    'FinalDecisionEnhancer',
    'AutonomousMarketIntelligence',
    'AutonomousTrader'
]

__version__ = "7.0.0"
__author__ = "Epinnox Autonomous Trading System"
