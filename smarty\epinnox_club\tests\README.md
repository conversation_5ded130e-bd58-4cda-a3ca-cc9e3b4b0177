# Tests Directory

This directory contains all test scripts, validation tools, and debugging utilities for the Money Circle platform.

## 🧪 Test Categories

### Core Application Tests
- `test_app_startup.py` - Application startup testing
- `test_platform.py` - Platform functionality tests
- `test_platform_final.py` - Final platform validation
- `comprehensive_test.py` - Comprehensive system testing

### Authentication & Security Tests
- `test_auth_simple.py` - Basic authentication tests
- `test_auth_improvements.py` - Enhanced authentication testing
- `test_authentication_fixes.py` - Authentication fix validation
- `test_security_fixes.py` - Security system tests
- `test_login.py` - Login functionality tests
- `test_login_simple.py` - Simple login tests

### CSRF & Security Tests
- `test_csrf_fix.py` - CSRF protection tests
- `test_csrf_fixes.py` - CSRF fix validation
- `test_csrf_simple.py` - Simple CSRF tests
- `test_csrf_validation.py` - CSRF validation tests
- `simple_csrf_test.py` - Basic CSRF testing

### Database Tests
- `test_database_fix.py` - Database fix validation
- `test_database_and_template_fixes.py` - Database and template tests
- `check_db.py` - Database health checks
- `check_db_schema.py` - Database schema validation
- `check_table_structure.py` - Table structure verification
- `check_users.py` - User data validation

### UI & Frontend Tests
- `test_browser_compatibility.py` - Cross-browser testing
- `test_responsive_design.py` - Responsive design validation
- `test_css_layout_fix.py` - CSS layout tests
- `test_design_consistency.py` - Design consistency checks
- `test_dropdown_positioning.py` - UI component tests
- `test_templates.py` - Template rendering tests

### Performance Tests
- `test_performance_optimization.py` - Performance optimization tests
- `test_performance_after_optimization.py` - Post-optimization validation

### Trading & Exchange Tests
- `test_live_trading_integration.py` - Live trading tests
- `test_complete_live_trading.py` - Complete trading system tests
- `test_professional_trading.py` - Professional trading features
- `test_exchange_management.py` - Exchange integration tests
- `test_bybit_fix.py` - Bybit exchange tests
- `test_htx_websocket_fix.py` - HTX websocket tests
- `test_coingecko_fix.py` - CoinGecko API tests

### Feature Tests
- `test_enhanced_club_features.py` - Club feature tests
- `test_enhanced_features_simple.py` - Simple feature tests
- `test_member_directory.py` - Member directory tests
- `test_money_circle_dashboard.py` - Dashboard tests

### Debug & Diagnostic Tools
- `debug_auth.py` - Authentication debugging
- `debug_auth_fix.py` - Authentication fix debugging
- `debug_fernet_issue.py` - Encryption debugging
- `debug_webapp_creation.py` - Web app creation debugging
- `diagnose_startup.py` - Startup diagnostics

### Verification Scripts
- `verify_demo_success.py` - Demo environment verification
- `verify_demo_users.py` - Demo user verification
- `verify_deployment.py` - Deployment verification
- `verify_smart_trader_fix.py` - Smart trader verification
- `final_verification.py` - Final system verification

## 🚀 Running Tests

### Quick Test Suite
```bash
# Run basic functionality tests
python tests/simple_test.py
python tests/test_app_startup.py

# Run authentication tests
python tests/test_auth_simple.py
python tests/test_login_simple.py
```

### Comprehensive Testing
```bash
# Run full test suite
python tests/comprehensive_test.py

# Run platform tests
python tests/test_platform_final.py

# Run final verification
python tests/final_verification.py
```

### Specific Feature Testing
```bash
# Test trading features
python tests/test_live_trading_integration.py
python tests/test_professional_trading.py

# Test UI components
python tests/test_responsive_design.py
python tests/test_browser_compatibility.py

# Test performance
python tests/test_performance_optimization.py
```

### Database Testing
```bash
# Check database health
python tests/check_db.py
python tests/check_db_schema.py

# Verify user data
python tests/check_users.py
```

### Debug & Troubleshooting
```bash
# Debug authentication issues
python tests/debug_auth.py

# Diagnose startup problems
python tests/diagnose_startup.py

# Debug encryption issues
python tests/debug_fernet_issue.py
```

## 📊 Test Results

Tests generate logs and reports in the `../logs/` directory. Check these files for detailed test results and error information.

## ⚠️ Important Notes

- Run tests in development environment first
- Some tests require specific environment setup
- Database tests may modify test data
- Performance tests should be run on production-like hardware

## 🔗 Related Directories

- `../scripts/` - Utility and setup scripts
- `../logs/` - Test logs and reports
- `../docs/` - Testing documentation
