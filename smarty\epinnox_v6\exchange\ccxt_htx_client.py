#!/usr/bin/env python3
"""
CCXT HTX Client for USDT-M Futures Trading
Real implementation using CCXT library for HTX futures
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List
import ccxt.async_support as ccxt

logger = logging.getLogger(__name__)

class CCXTHTXClient:
    """
    CCXT-based HTX USDT-M Futures client for live trading.
    Uses ccxt.huobi with defaultType='swap' for futures trading.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # API Configuration
        self.api_key = os.getenv('HTX_API_KEY', '')
        self.api_secret = os.getenv('HTX_API_SECRET', '')
        self.passphrase = os.getenv('HTX_PASSPHRASE', '')

        # Trading Configuration
        trading_symbol_env = os.getenv('TRADING_SYMBOL', 'DOGE-USDT')
        # Convert to CCXT format: DOGE-USDT -> DOGE/USDT:USDT for futures
        if '-' in trading_symbol_env:
            base, quote = trading_symbol_env.split('-')
            self.trading_symbol = f"{base}/{quote}:{quote}"  # DOGE/USDT:USDT
        else:
            self.trading_symbol = trading_symbol_env
        self.leverage = float(os.getenv('LEVERAGE', '20'))

        # Initialize CCXT client
        self.client = None
        self.is_connected = False

        # Check credentials
        if not self.api_key or not self.api_secret:
            logger.warning("⚠️ HTX API credentials not configured - live trading disabled")
            self.live_trading_enabled = False
        else:
            self.live_trading_enabled = True
            logger.info(f"🔑 CCXT HTX client initialized for {self.trading_symbol}")

    def create_client(self):
        """Create CCXT HTX client for USDT-M futures."""
        try:
            if not self.live_trading_enabled:
                return None

            self.client = ccxt.huobi({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'enableRateLimit': True,
                'sandbox': False,  # Use live trading
                'options': {
                    'defaultType': 'swap',  # CRITICAL: 'swap' = USDT-M futures
                    'hedgeMode': True,      # 🔧 FIX: Enable hedge mode explicitly
                },
                'headers': {
                    'User-Agent': 'Epinnox-V6-Trading-Bot/1.0'
                }
            })

            logger.info("✅ CCXT HTX futures client created")
            return self.client

        except Exception as e:
            logger.error(f"Error creating CCXT HTX client: {e}")
            return None

    async def connect(self):
        """Connect and test the CCXT client."""
        try:
            if not self.client:
                self.client = self.create_client()

            if not self.client:
                return False

            # Test connection by fetching markets
            markets = await self.client.load_markets()

            # Check if our trading symbol exists
            if self.trading_symbol not in markets:
                logger.error(f"Trading symbol {self.trading_symbol} not found in HTX markets")
                return False

            self.is_connected = True
            logger.info(f"✅ Connected to HTX futures - {len(markets)} markets loaded")
            return True

        except Exception as e:
            logger.error(f"Error connecting to HTX: {e}")
            self.is_connected = False
            return False

    async def disconnect(self):
        """Disconnect from HTX with proper cleanup."""
        try:
            if self.client:
                # Properly close CCXT client
                await self.client.close()
                self.client = None
            self.is_connected = False
            logger.info("🔌 Disconnected from HTX")

        except Exception as e:
            logger.error(f"Error disconnecting from HTX: {e}")

    async def __aenter__(self):
        """Async context manager entry."""
        if not self.is_connected:
            await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        await self.disconnect()

    async def get_account_balance(self) -> Optional[Dict[str, Any]]:
        """Get account balance for USDT-M futures."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Fetch balance for futures (swap) trading
            balance = await self.client.fetch_balance({'type': 'swap'})

            logger.debug(f"📊 Account balance retrieved: {balance.get('USDT', {})}")
            return balance

        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return None

    async def get_positions(self, symbol: str = None) -> Optional[List[Dict[str, Any]]]:
        """Get open positions with detailed debugging."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Get positions for specific symbol or all symbols
            symbols = [symbol] if symbol else None
            positions = await self.client.fetch_positions(symbols)

            # Filter out zero positions - check multiple fields
            open_positions = []
            for pos in positions:
                size = float(pos.get('size', 0))
                contracts = float(pos.get('contracts', 0))
                amount = float(pos.get('amount', 0))

                # Position is open if any of these fields > 0
                if size > 0 or contracts > 0 or amount > 0:
                    open_positions.append(pos)

            if open_positions:
                logger.info(f"📊 Found {len(open_positions)} open position(s)")
                # Log summary of open positions
                for pos in open_positions:
                    symbol_name = pos.get('symbol', 'unknown')
                    side = pos.get('side', 'unknown')
                    contracts = pos.get('contracts', 0)
                    pnl = pos.get('unrealizedPnl', 0)
                    logger.info(f"  📈 {symbol_name} {side} {contracts} contracts | PnL: ${pnl:.3f}")
            else:
                logger.debug(f"📊 No open positions found ({len(positions)} total checked)")
            return open_positions

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return None

    async def place_market_order(self, symbol: str, side: str, amount: float, offset: str = 'open') -> Optional[Dict[str, Any]]:
        """🔧 ENHANCED: Place a market order with proper HTX hedge mode support and position management."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected - cannot place order")
                return None

            # Convert side to CCXT format
            ccxt_side = 'buy' if side.upper() in ['LONG', 'BUY'] else 'sell'

            logger.info(f"🚀 Placing market order: {ccxt_side} {amount} {symbol} (offset: {offset})")

            # 🔧 ENHANCED: HTX Hedge Mode Parameters with dynamic offset
            # For hedge mode, we need to specify position side and offset explicitly
            if offset == 'close':
                # For closing positions, determine position side from existing positions
                positions = await self.get_positions()
                position_side = None

                if positions:
                    for pos in positions:
                        if pos.get('symbol') == symbol and pos.get('contracts', 0) > 0:
                            position_side = pos.get('side')
                            break

                if not position_side:
                    logger.warning(f"⚠️ No open position found for {symbol} to close")
                    return None

                params = {
                    'offset': 'close',          # 'close' for closing existing positions
                    'lever_rate': 20,           # Leverage setting
                    'direction': ccxt_side,     # Explicit direction for hedge mode
                    'contract_type': 'swap',    # Perpetual futures
                    'position_side': position_side  # Use existing position side
                }
            else:
                # For opening new positions
                params = {
                    'offset': 'open',           # 'open' for new positions
                    'lever_rate': 20,           # Leverage setting
                    'direction': ccxt_side,     # Explicit direction for hedge mode
                    'contract_type': 'swap',    # Perpetual futures
                    'position_side': 'long' if ccxt_side == 'buy' else 'short'  # Hedge mode position side
                }

            logger.info(f"🔧 HTX Hedge Mode params: {params}")

            # Use the generic create_order method with explicit type and params
            order = await self.client.create_order(
                symbol=symbol,
                type='market',
                side=ccxt_side,
                amount=amount,
                price=None,  # No price for market orders
                params=params
            )

            logger.info(f"✅ Order placed: {order.get('id')} - {order.get('status')}")
            return order

        except Exception as e:
            logger.error(f"Error placing market order: {e}")
            return None

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return False

            result = await self.client.cancel_order(order_id, symbol)
            logger.info(f"✅ Order cancelled: {order_id}")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False

    async def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current ticker for symbol."""
        try:
            if not self.is_connected or not self.client:
                return None

            ticker = await self.client.fetch_ticker(symbol)
            return ticker

        except Exception as e:
            logger.error(f"Error getting ticker: {e}")
            return None

    async def get_historical_trades(self, symbol: str = None, limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """🔧 NEW: Get historical trades/orders for futures positions."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Use the trading symbol if none specified
            if not symbol:
                symbol = self.trading_symbol

            logger.info(f"📊 Fetching historical trades for {symbol} (limit: {limit})")

            # Fetch recent orders/trades
            orders = await self.client.fetch_orders(symbol, limit=limit)

            # Process and format the orders
            formatted_trades = []
            for order in orders:
                if order.get('status') in ['closed', 'filled']:
                    trade = {
                        'id': order.get('id'),
                        'symbol': order.get('symbol'),
                        'side': order.get('side'),
                        'amount': order.get('amount', 0),
                        'price': order.get('price', 0),
                        'cost': order.get('cost', 0),
                        'fee': order.get('fee', {}),
                        'timestamp': order.get('timestamp'),
                        'datetime': order.get('datetime'),
                        'status': order.get('status'),
                        'type': order.get('type'),
                        'filled': order.get('filled', 0),
                        'remaining': order.get('remaining', 0)
                    }
                    formatted_trades.append(trade)

            logger.info(f"✅ Retrieved {len(formatted_trades)} historical trades")
            return formatted_trades

        except Exception as e:
            logger.error(f"Error getting historical trades: {e}")
            return None

    async def get_position_history(self, symbol: str = None, limit: int = 30) -> Optional[List[Dict[str, Any]]]:
        """🔧 NEW: Get historical position data and calculate P&L."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Use the trading symbol if none specified
            if not symbol:
                symbol = self.trading_symbol

            logger.info(f"📊 Fetching position history for {symbol} (limit: {limit})")

            # Get historical trades to reconstruct position history
            trades = await self.get_historical_trades(symbol, limit * 2)  # Get more trades to build positions

            if not trades:
                return []

            # Process trades to create position history
            position_history = []
            current_position = {'size': 0, 'side': None, 'entry_price': 0, 'trades': []}

            for trade in sorted(trades, key=lambda x: x.get('timestamp', 0)):
                trade_side = trade.get('side')
                trade_amount = float(trade.get('filled', 0))
                trade_price = float(trade.get('price', 0))

                if trade_side == 'buy':
                    if current_position['side'] == 'short':
                        # Closing short position
                        if trade_amount >= abs(current_position['size']):
                            # Position fully closed
                            exit_price = trade_price
                            pnl = (current_position['entry_price'] - exit_price) * abs(current_position['size'])

                            position_history.append({
                                'symbol': symbol,
                                'side': 'short',
                                'size': abs(current_position['size']),
                                'entry_price': current_position['entry_price'],
                                'exit_price': exit_price,
                                'pnl': pnl,
                                'pnl_pct': (pnl / (current_position['entry_price'] * abs(current_position['size']))) * 100,
                                'entry_time': current_position.get('entry_time'),
                                'exit_time': trade.get('datetime'),
                                'duration': None,  # Calculate if needed
                                'trades': current_position['trades'] + [trade]
                            })

                            # Reset position
                            remaining = trade_amount - abs(current_position['size'])
                            if remaining > 0:
                                current_position = {
                                    'size': remaining,
                                    'side': 'long',
                                    'entry_price': trade_price,
                                    'entry_time': trade.get('datetime'),
                                    'trades': [trade]
                                }
                            else:
                                current_position = {'size': 0, 'side': None, 'entry_price': 0, 'trades': []}
                        else:
                            # Partial close
                            current_position['size'] += trade_amount  # Reducing short position
                            current_position['trades'].append(trade)
                    else:
                        # Opening or adding to long position
                        if current_position['size'] == 0:
                            current_position = {
                                'size': trade_amount,
                                'side': 'long',
                                'entry_price': trade_price,
                                'entry_time': trade.get('datetime'),
                                'trades': [trade]
                            }
                        else:
                            # Adding to position - calculate average price
                            total_cost = (current_position['entry_price'] * current_position['size']) + (trade_price * trade_amount)
                            total_size = current_position['size'] + trade_amount
                            current_position['entry_price'] = total_cost / total_size
                            current_position['size'] = total_size
                            current_position['trades'].append(trade)

                elif trade_side == 'sell':
                    if current_position['side'] == 'long':
                        # Closing long position
                        if trade_amount >= current_position['size']:
                            # Position fully closed
                            exit_price = trade_price
                            pnl = (exit_price - current_position['entry_price']) * current_position['size']

                            position_history.append({
                                'symbol': symbol,
                                'side': 'long',
                                'size': current_position['size'],
                                'entry_price': current_position['entry_price'],
                                'exit_price': exit_price,
                                'pnl': pnl,
                                'pnl_pct': (pnl / (current_position['entry_price'] * current_position['size'])) * 100,
                                'entry_time': current_position.get('entry_time'),
                                'exit_time': trade.get('datetime'),
                                'duration': None,  # Calculate if needed
                                'trades': current_position['trades'] + [trade]
                            })

                            # Reset position
                            remaining = trade_amount - current_position['size']
                            if remaining > 0:
                                current_position = {
                                    'size': remaining,
                                    'side': 'short',
                                    'entry_price': trade_price,
                                    'entry_time': trade.get('datetime'),
                                    'trades': [trade]
                                }
                            else:
                                current_position = {'size': 0, 'side': None, 'entry_price': 0, 'trades': []}
                        else:
                            # Partial close
                            current_position['size'] -= trade_amount
                            current_position['trades'].append(trade)
                    else:
                        # Opening or adding to short position
                        if current_position['size'] == 0:
                            current_position = {
                                'size': trade_amount,
                                'side': 'short',
                                'entry_price': trade_price,
                                'entry_time': trade.get('datetime'),
                                'trades': [trade]
                            }
                        else:
                            # Adding to position - calculate average price
                            total_cost = (current_position['entry_price'] * current_position['size']) + (trade_price * trade_amount)
                            total_size = current_position['size'] + trade_amount
                            current_position['entry_price'] = total_cost / total_size
                            current_position['size'] = total_size
                            current_position['trades'].append(trade)

            logger.info(f"✅ Processed {len(position_history)} historical positions")
            return position_history

        except Exception as e:
            logger.error(f"Error getting position history: {e}")
            return None

    async def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', limit: int = 100) -> Optional[List]:
        """🧠 CRITICAL: Fetch OHLCV data for Multi-Timeframe Analysis."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected - cannot fetch OHLCV")
                return None

            # Fetch OHLCV data using CCXT
            ohlcv = await self.client.fetch_ohlcv(symbol, timeframe, limit=limit)

            if ohlcv:
                logger.debug(f"📊 Fetched {len(ohlcv)} {timeframe} candles for {symbol}")
                return ohlcv
            else:
                logger.warning(f"⚠️ No OHLCV data returned for {symbol} {timeframe}")
                return None

        except Exception as e:
            logger.error(f"❌ Error fetching OHLCV for {symbol} {timeframe}: {e}")
            return None

    async def set_leverage(self, symbol: str, leverage: float) -> bool:
        """Set leverage for symbol."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return False

            # Set leverage for the symbol
            result = await self.client.set_leverage(leverage, symbol)
            logger.info(f"✅ Leverage set to {leverage}x for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error setting leverage: {e}")
            return False

    def get_trading_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        return {
            'live_trading_enabled': self.live_trading_enabled,
            'is_connected': self.is_connected,
            'api_configured': bool(self.api_key and self.api_secret),
            'trading_symbol': self.trading_symbol,
            'leverage': self.leverage,
            'client_type': 'ccxt.huobi',
            'market_type': 'swap'  # USDT-M futures
        }

def create_htx_client(config: Dict[str, Any] = None) -> CCXTHTXClient:
    """Factory function to create HTX CCXT client."""
    config = config or {}
    return CCXTHTXClient(config)

async def test_htx_connection() -> bool:
    """Test HTX connection and basic functionality."""
    try:
        logger.info("🧪 Testing HTX CCXT connection...")

        # Create client
        client = create_htx_client()

        # Connect
        connected = await client.connect()
        if not connected:
            logger.error("❌ Failed to connect to HTX")
            return False

        # Test account balance
        balance = await client.get_account_balance()
        if balance:
            usdt_balance = balance.get('USDT', {})
            logger.info(f"✅ Account balance: {usdt_balance.get('total', 0)} USDT")

        # Test positions
        positions = await client.get_positions()
        if positions is not None:
            logger.info(f"✅ Positions: {len(positions)} open positions")

        # Test ticker
        ticker = await client.get_ticker('DOGE/USDT:USDT')
        if ticker:
            logger.info(f"✅ Ticker: DOGE/USDT price = ${ticker.get('last', 0):.4f}")

        # Disconnect
        await client.disconnect()

        logger.info("✅ HTX CCXT connection test completed successfully")
        return True

    except Exception as e:
        logger.error(f"❌ HTX connection test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the client
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_htx_connection())
