#!/usr/bin/env python3
"""
Multi-Symbol Trading Orchestrator - Phase 5
Central orchestrator that coordinates all components of the autonomous
multi-symbol futures trading system for seamless operation.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .market_scanner import MultiSymbolMarketScanner
from .symbol_selector import DynamicSymbolSelector
from .multi_symbol_trader import MultiSymbolAutonomousTrader
from .enhanced_decision_engine import EnhancedDecisionEngine

logger = logging.getLogger(__name__)

@dataclass
class SystemStatus:
    """Overall system status."""
    running: bool
    components_active: Dict[str, bool]
    performance_metrics: Dict[str, float]
    active_symbols: List[str]
    total_positions: int
    portfolio_value: float
    daily_pnl: float
    system_health: str  # 'excellent', 'good', 'warning', 'critical'
    last_updated: float

class MultiSymbolTradingOrchestrator:
    """
    Central orchestrator for the autonomous multi-symbol futures trading system.
    Coordinates all components and ensures seamless operation.
    """

    def __init__(self, config: Dict[str, Any], exchange_client=None, execution_controller=None):
        self.config = config
        self.exchange_client = exchange_client
        self.execution_controller = execution_controller
        
        # Initialize all components
        self.market_scanner = MultiSymbolMarketScanner(config, exchange_client)
        self.symbol_selector = DynamicSymbolSelector(config, self.market_scanner)
        self.multi_symbol_trader = MultiSymbolAutonomousTrader(
            config, self.market_scanner, self.symbol_selector, execution_controller
        )
        self.decision_engine = EnhancedDecisionEngine(
            config, self.market_scanner, self.symbol_selector, self.multi_symbol_trader
        )
        
        # Orchestrator configuration
        self.startup_sequence_delay = config.get('orchestrator', {}).get('startup_delay', 10)
        self.health_check_interval = config.get('orchestrator', {}).get('health_check_interval', 60)
        self.performance_report_interval = config.get('orchestrator', {}).get('report_interval', 300)
        
        # System state
        self.system_status = SystemStatus(
            running=False,
            components_active={
                'market_scanner': False,
                'symbol_selector': False,
                'multi_symbol_trader': False,
                'decision_engine': False
            },
            performance_metrics={},
            active_symbols=[],
            total_positions=0,
            portfolio_value=0.0,
            daily_pnl=0.0,
            system_health='good',
            last_updated=time.time()
        )
        
        # Performance tracking
        self.startup_time = 0
        self.total_runtime = 0
        self.component_errors = {
            'market_scanner': 0,
            'symbol_selector': 0,
            'multi_symbol_trader': 0,
            'decision_engine': 0
        }
        
        # Tasks
        self.orchestrator_task = None
        self.health_check_task = None
        self.performance_report_task = None
        
        logger.info(f"🎼 Multi-Symbol Trading Orchestrator initialized")
        logger.info(f"   🔧 Components: Market Scanner, Symbol Selector, Multi-Symbol Trader, Decision Engine")
        logger.info(f"   ⏱️ Health check interval: {self.health_check_interval}s")

    async def start_system(self):
        """Start the complete multi-symbol trading system."""
        if self.system_status.running:
            logger.warning("System is already running")
            return
        
        logger.info("🚀 Starting Multi-Symbol Autonomous Trading System...")
        self.startup_time = time.time()
        
        try:
            # Start components in sequence
            await self._start_components_sequence()
            
            # Start orchestrator tasks
            await self._start_orchestrator_tasks()
            
            # Update system status
            self.system_status.running = True
            self.system_status.last_updated = time.time()
            
            logger.info("✅ Multi-Symbol Autonomous Trading System started successfully!")
            await self._log_system_status()
            
        except Exception as e:
            logger.error(f"❌ Failed to start system: {e}")
            await self.stop_system()
            raise

    async def stop_system(self):
        """Stop the complete multi-symbol trading system."""
        if not self.system_status.running:
            logger.warning("System is not running")
            return
        
        logger.info("🛑 Stopping Multi-Symbol Autonomous Trading System...")
        
        try:
            # Stop orchestrator tasks
            await self._stop_orchestrator_tasks()
            
            # Stop components in reverse order
            await self._stop_components_sequence()
            
            # Update system status
            self.system_status.running = False
            self.system_status.components_active = {k: False for k in self.system_status.components_active}
            self.system_status.last_updated = time.time()
            
            # Calculate total runtime
            if self.startup_time > 0:
                self.total_runtime += time.time() - self.startup_time
            
            logger.info("✅ Multi-Symbol Autonomous Trading System stopped successfully!")
            
        except Exception as e:
            logger.error(f"❌ Error stopping system: {e}")

    async def _start_components_sequence(self):
        """Start all components in the correct sequence."""
        logger.info("📊 Starting Market Scanner...")
        await self.market_scanner.start_scanning()
        self.system_status.components_active['market_scanner'] = True
        await asyncio.sleep(self.startup_sequence_delay)  # Allow scanner to gather initial data
        
        logger.info("🎯 Starting Symbol Selector...")
        await self.symbol_selector.start_selection()
        self.system_status.components_active['symbol_selector'] = True
        await asyncio.sleep(5)  # Allow selector to analyze initial data
        
        logger.info("🤖 Starting Multi-Symbol Trader...")
        await self.multi_symbol_trader.start_trading()
        self.system_status.components_active['multi_symbol_trader'] = True
        await asyncio.sleep(5)
        
        logger.info("🧠 Starting Enhanced Decision Engine...")
        await self.decision_engine.start_engine()
        self.system_status.components_active['decision_engine'] = True
        
        logger.info("⚡ All components started successfully!")

    async def _stop_components_sequence(self):
        """Stop all components in reverse order."""
        logger.info("🧠 Stopping Enhanced Decision Engine...")
        await self.decision_engine.stop_engine()
        self.system_status.components_active['decision_engine'] = False
        
        logger.info("🤖 Stopping Multi-Symbol Trader...")
        await self.multi_symbol_trader.stop_trading()
        self.system_status.components_active['multi_symbol_trader'] = False
        
        logger.info("🎯 Stopping Symbol Selector...")
        await self.symbol_selector.stop_selection()
        self.system_status.components_active['symbol_selector'] = False
        
        logger.info("📊 Stopping Market Scanner...")
        await self.market_scanner.stop_scanning()
        self.system_status.components_active['market_scanner'] = False
        
        logger.info("⚡ All components stopped successfully!")

    async def _start_orchestrator_tasks(self):
        """Start orchestrator background tasks."""
        self.orchestrator_task = asyncio.create_task(self._orchestrator_loop())
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        self.performance_report_task = asyncio.create_task(self._performance_report_loop())

    async def _stop_orchestrator_tasks(self):
        """Stop orchestrator background tasks."""
        tasks = [self.orchestrator_task, self.health_check_task, self.performance_report_task]
        
        for task in tasks:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    async def _orchestrator_loop(self):
        """Main orchestrator loop for coordination."""
        while self.system_status.running:
            try:
                # Update system status
                await self._update_system_status()
                
                # Coordinate components if needed
                await self._coordinate_components()
                
                # Check for system optimization opportunities
                await self._optimize_system_performance()
                
                await asyncio.sleep(30)  # Orchestrator cycle
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in orchestrator loop: {e}")
                await asyncio.sleep(10)

    async def _health_check_loop(self):
        """Health check loop for all components."""
        while self.system_status.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in health check loop: {e}")
                await asyncio.sleep(30)

    async def _performance_report_loop(self):
        """Performance reporting loop."""
        while self.system_status.running:
            try:
                await self._generate_performance_report()
                await asyncio.sleep(self.performance_report_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in performance report loop: {e}")
                await asyncio.sleep(60)

    async def _update_system_status(self):
        """Update overall system status."""
        try:
            # Get portfolio status
            portfolio_status = self.multi_symbol_trader.get_portfolio_status()
            
            # Update system status
            self.system_status.active_symbols = portfolio_status.get('active_symbols', [])
            self.system_status.total_positions = portfolio_status['portfolio_metrics']['total_positions']
            self.system_status.portfolio_value = portfolio_status['portfolio_metrics']['total_capital']
            self.system_status.daily_pnl = portfolio_status['daily_stats']['total_pnl']
            
            # Update performance metrics
            scanner_stats = self.market_scanner.get_scan_stats()
            selector_stats = self.symbol_selector.get_selection_stats()
            trader_stats = portfolio_status
            engine_stats = self.decision_engine.get_engine_status()
            
            self.system_status.performance_metrics = {
                'scanner_symbols': scanner_stats.get('symbols_scanned', 0),
                'scanner_opportunities': scanner_stats.get('opportunities_found', 0),
                'selector_switches': selector_stats.get('symbol_switches', 0),
                'trader_positions': trader_stats['portfolio_metrics']['total_positions'],
                'trader_pnl': trader_stats['daily_stats']['total_pnl'],
                'engine_signals': engine_stats.get('recent_signals_count', 0)
            }
            
            # Determine system health
            self.system_status.system_health = await self._assess_system_health()
            self.system_status.last_updated = time.time()
            
        except Exception as e:
            logger.error(f"❌ Error updating system status: {e}")

    async def _coordinate_components(self):
        """Coordinate components for optimal performance."""
        try:
            # Check if symbol selector needs to update based on new scanner data
            scanner_stats = self.market_scanner.get_scan_stats()
            if scanner_stats.get('opportunities_found', 0) > 10:
                # High opportunity environment - increase selection frequency
                pass  # Could adjust selector parameters
            
            # Check if trader needs risk adjustment
            portfolio_status = self.multi_symbol_trader.get_portfolio_status()
            risk_utilization = portfolio_status['portfolio_metrics']['risk_utilization']
            
            if risk_utilization > 0.8:  # High risk utilization
                logger.warning(f"⚠️ High risk utilization: {risk_utilization:.1%}")
                # Could trigger risk reduction
            
        except Exception as e:
            logger.error(f"❌ Error coordinating components: {e}")

    async def _optimize_system_performance(self):
        """Optimize system performance based on current conditions."""
        try:
            # Analyze component performance
            performance_metrics = self.system_status.performance_metrics
            
            # Optimize based on market conditions
            engine_status = self.decision_engine.get_engine_status()
            current_regime = engine_status.get('current_regime')
            
            if current_regime:
                regime_type = current_regime.get('type', 'unknown')
                
                # Adjust system parameters based on regime
                if regime_type == 'volatile':
                    # In volatile markets, increase decision frequency
                    pass
                elif regime_type == 'trending':
                    # In trending markets, hold positions longer
                    pass
            
        except Exception as e:
            logger.error(f"❌ Error optimizing system performance: {e}")

    async def _perform_health_checks(self):
        """Perform health checks on all components."""
        try:
            health_issues = []
            
            # Check market scanner
            scanner_stats = self.market_scanner.get_scan_stats()
            if scanner_stats.get('last_scan_duration', 0) > 30:  # Slow scans
                health_issues.append("Market scanner running slowly")
            
            # Check symbol selector
            current_symbol = self.symbol_selector.get_current_symbol()
            if not current_symbol:
                health_issues.append("Symbol selector has no active symbol")
            
            # Check trader
            portfolio_status = self.multi_symbol_trader.get_portfolio_status()
            if not portfolio_status.get('enabled', False):
                health_issues.append("Multi-symbol trader is disabled")
            
            # Check decision engine
            engine_status = self.decision_engine.get_engine_status()
            if not engine_status.get('running', False):
                health_issues.append("Decision engine is not running")
            
            # Update system health
            if len(health_issues) == 0:
                self.system_status.system_health = 'excellent'
            elif len(health_issues) <= 2:
                self.system_status.system_health = 'good'
            elif len(health_issues) <= 4:
                self.system_status.system_health = 'warning'
            else:
                self.system_status.system_health = 'critical'
            
            if health_issues:
                logger.warning(f"⚠️ Health check issues: {', '.join(health_issues)}")
            
        except Exception as e:
            logger.error(f"❌ Error performing health checks: {e}")
            self.system_status.system_health = 'critical'

    async def _assess_system_health(self) -> str:
        """Assess overall system health."""
        try:
            # Check component status
            active_components = sum(self.system_status.components_active.values())
            total_components = len(self.system_status.components_active)
            
            if active_components == total_components:
                component_health = 'excellent'
            elif active_components >= total_components * 0.75:
                component_health = 'good'
            elif active_components >= total_components * 0.5:
                component_health = 'warning'
            else:
                component_health = 'critical'
            
            # Check performance metrics
            performance_health = 'good'  # Simplified assessment
            
            # Check error rates
            total_errors = sum(self.component_errors.values())
            if total_errors > 10:
                error_health = 'warning'
            elif total_errors > 20:
                error_health = 'critical'
            else:
                error_health = 'good'
            
            # Overall health assessment
            health_scores = {'excellent': 4, 'good': 3, 'warning': 2, 'critical': 1}
            avg_score = (health_scores[component_health] + health_scores[performance_health] + health_scores[error_health]) / 3
            
            if avg_score >= 3.5:
                return 'excellent'
            elif avg_score >= 2.5:
                return 'good'
            elif avg_score >= 1.5:
                return 'warning'
            else:
                return 'critical'
                
        except Exception as e:
            logger.error(f"❌ Error assessing system health: {e}")
            return 'critical'

    async def _generate_performance_report(self):
        """Generate comprehensive performance report."""
        try:
            # Collect data from all components
            scanner_stats = self.market_scanner.get_scan_stats()
            selector_stats = self.symbol_selector.get_selection_stats()
            trader_status = self.multi_symbol_trader.get_portfolio_status()
            engine_status = self.decision_engine.get_engine_status()
            
            # Generate report
            report = {
                'timestamp': time.time(),
                'system_status': {
                    'running': self.system_status.running,
                    'health': self.system_status.system_health,
                    'uptime': time.time() - self.startup_time if self.startup_time > 0 else 0
                },
                'market_scanner': {
                    'symbols_scanned': scanner_stats.get('symbols_scanned', 0),
                    'opportunities_found': scanner_stats.get('opportunities_found', 0),
                    'avg_scan_time': scanner_stats.get('avg_scan_time', 0)
                },
                'symbol_selector': {
                    'current_symbol': self.symbol_selector.get_current_symbol(),
                    'total_selections': selector_stats.get('total_selections', 0),
                    'symbol_switches': selector_stats.get('symbol_switches', 0)
                },
                'multi_symbol_trader': {
                    'active_symbols': len(trader_status.get('active_symbols', [])),
                    'total_positions': trader_status['portfolio_metrics']['total_positions'],
                    'portfolio_value': trader_status['portfolio_metrics']['total_capital'],
                    'daily_pnl': trader_status['daily_stats']['total_pnl'],
                    'risk_utilization': trader_status['portfolio_metrics']['risk_utilization']
                },
                'decision_engine': {
                    'running': engine_status.get('running', False),
                    'recent_signals': engine_status.get('recent_signals_count', 0),
                    'avg_confidence': engine_status.get('engine_stats', {}).get('avg_confidence', 0)
                }
            }
            
            logger.info("📊 Performance Report:")
            logger.info(f"   🏥 System Health: {report['system_status']['health']}")
            logger.info(f"   📈 Active Symbols: {report['multi_symbol_trader']['active_symbols']}")
            logger.info(f"   💰 Daily PnL: ${report['multi_symbol_trader']['daily_pnl']:.2f}")
            logger.info(f"   🎯 Recent Signals: {report['decision_engine']['recent_signals']}")
            logger.info(f"   ⚖️ Risk Utilization: {report['multi_symbol_trader']['risk_utilization']:.1%}")
            
        except Exception as e:
            logger.error(f"❌ Error generating performance report: {e}")

    async def _log_system_status(self):
        """Log current system status."""
        logger.info("🎼 Multi-Symbol Trading System Status:")
        logger.info(f"   🏃 Running: {self.system_status.running}")
        logger.info(f"   🏥 Health: {self.system_status.system_health}")
        logger.info(f"   🔧 Active Components: {sum(self.system_status.components_active.values())}/4")
        logger.info(f"   📊 Active Symbols: {len(self.system_status.active_symbols)}")
        logger.info(f"   📈 Total Positions: {self.system_status.total_positions}")
        logger.info(f"   💰 Portfolio Value: ${self.system_status.portfolio_value:.2f}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get complete system status."""
        return {
            'running': self.system_status.running,
            'system_health': self.system_status.system_health,
            'components_active': self.system_status.components_active.copy(),
            'performance_metrics': self.system_status.performance_metrics.copy(),
            'active_symbols': self.system_status.active_symbols.copy(),
            'total_positions': self.system_status.total_positions,
            'portfolio_value': self.system_status.portfolio_value,
            'daily_pnl': self.system_status.daily_pnl,
            'uptime': time.time() - self.startup_time if self.startup_time > 0 else 0,
            'last_updated': self.system_status.last_updated
        }

    def get_component_status(self, component: str) -> Dict[str, Any]:
        """Get status of a specific component."""
        if component == 'market_scanner':
            return self.market_scanner.get_scan_stats()
        elif component == 'symbol_selector':
            return self.symbol_selector.get_selection_stats()
        elif component == 'multi_symbol_trader':
            return self.multi_symbol_trader.get_portfolio_status()
        elif component == 'decision_engine':
            return self.decision_engine.get_engine_status()
        else:
            return {'error': f'Unknown component: {component}'}

    async def emergency_stop(self):
        """Emergency stop of the entire system."""
        logger.warning("🚨 EMERGENCY STOP TRIGGERED!")
        
        # Stop all trading immediately
        if self.multi_symbol_trader:
            await self.multi_symbol_trader.stop_trading()
        
        # Stop decision engine
        if self.decision_engine:
            await self.decision_engine.stop_engine()
        
        # Stop other components
        await self.stop_system()
        
        logger.warning("🚨 EMERGENCY STOP COMPLETED!")

    def enable_component(self, component: str) -> bool:
        """Enable a specific component."""
        # Implementation would enable specific components
        logger.info(f"🔧 Enabling component: {component}")
        return True

    def disable_component(self, component: str) -> bool:
        """Disable a specific component."""
        # Implementation would disable specific components
        logger.info(f"🔧 Disabling component: {component}")
        return True
