#!/usr/bin/env python3
"""
Money Circle CSS Layout Fix Validation Test
Tests all dashboard pages to ensure professional layout is working correctly.
"""

import requests
import sys
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8085"
TEST_PAGES = [
    {
        "name": "Personal Dashboard",
        "url": "/dashboard",
        "expected_elements": [
            "dashboard-container",
            "dashboard-grid",
            "portfolio-overview",
            "exchange-connections",
            "trading-interface",
            "market-data",
            "performance-analytics"
        ]
    },
    {
        "name": "Club Dashboard",
        "url": "/club",
        "expected_elements": [
            "club-overview",
            "strategy-proposals",
            "activity-feed",
            "member-leaderboard"
        ]
    },
    {
        "name": "Club Analytics",
        "url": "/analytics",
        "expected_elements": [
            "club-analytics-container",
            "analytics-header",
            "club-overview",
            "performance-metrics",
            "portfolio-analysis"
        ]
    },
    {
        "name": "Member Directory",
        "url": "/members",
        "expected_elements": [
            "directory-container",
            "member-stats-overview",
            "directory-controls",
            "leaderboards-section"
        ]
    },
    {
        "name": "Strategy Marketplace",
        "url": "/strategies",
        "expected_elements": [
            "marketplace-container",
            "marketplace-stats",
            "marketplace-controls",
            "featured-strategies"
        ]
    }
]

# CSS files to verify are loading
CSS_FILES = [
    "/static/css/design_system.css",
    "/static/css/unified_header.css",
    "/static/css/unified_footer.css",
    "/static/css/dashboard.css",
    "/static/css/club.css",
    "/static/css/club_analytics.css",
    "/static/css/member_directory.css",
    "/static/css/strategy_marketplace.css"
]

def test_css_file_loading():
    """Test that all CSS files are loading correctly"""
    print("\n🎨 Testing CSS File Loading...")

    for css_file in CSS_FILES:
        try:
            response = requests.get(f"{BASE_URL}{css_file}", timeout=10)
            if response.status_code == 200:
                content = response.text
                if len(content) > 100:  # Basic content check
                    print(f"  ✅ {css_file} - OK ({len(content)} bytes)")
                else:
                    print(f"  ⚠️  {css_file} - WARNING: Very small file")
            else:
                print(f"  ❌ {css_file} - ERROR: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {css_file} - ERROR: {str(e)}")

def test_page_accessibility():
    """Test that all pages are accessible and return 200 status"""
    print("\n🌐 Testing Page Accessibility...")

    for page in TEST_PAGES:
        try:
            response = requests.get(f"{BASE_URL}{page['url']}", timeout=10, allow_redirects=False)
            if response.status_code == 200:
                print(f"  ✅ {page['name']} - OK (Status 200)")
            elif response.status_code == 302:
                print(f"  🔄 {page['name']} - Redirect (Status 302) - May need login")
            else:
                print(f"  ❌ {page['name']} - ERROR: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {page['name']} - ERROR: {str(e)}")

def test_html_structure():
    """Test that pages contain expected HTML structure elements"""
    print("\n🏗️  Testing HTML Structure...")

    for page in TEST_PAGES:
        try:
            response = requests.get(f"{BASE_URL}{page['url']}", timeout=10)
            if response.status_code == 200:
                content = response.text
                found_elements = []
                missing_elements = []

                for element in page['expected_elements']:
                    if element in content:
                        found_elements.append(element)
                    else:
                        missing_elements.append(element)

                print(f"  📄 {page['name']}:")
                print(f"    ✅ Found: {len(found_elements)}/{len(page['expected_elements'])} elements")

                if missing_elements:
                    print(f"    ⚠️  Missing: {', '.join(missing_elements)}")

                # Check for CSS Grid usage
                if "display: grid" in content or "dashboard-grid" in content:
                    print(f"    🎯 CSS Grid detected - Layout should be professional")

            else:
                print(f"  ❌ {page['name']} - Cannot test structure (Status {response.status_code})")
        except Exception as e:
            print(f"  ❌ {page['name']} - ERROR: {str(e)}")

def test_design_system_integration():
    """Test that design system CSS variables are properly loaded"""
    print("\n🎨 Testing Design System Integration...")

    try:
        response = requests.get(f"{BASE_URL}/static/css/design_system.css", timeout=10)
        if response.status_code == 200:
            content = response.text

            # Check for key CSS variables
            key_variables = [
                "--primary-600",
                "--bg-primary",
                "--text-secondary",
                "--border-primary",
                "--shadow-lg"
            ]

            found_vars = []
            for var in key_variables:
                if var in content:
                    found_vars.append(var)

            print(f"  ✅ Design System Variables: {len(found_vars)}/{len(key_variables)} found")

            # Check for flexbox body layout
            if "display: flex" in content and "flex-direction: column" in content:
                print(f"  ✅ Flexbox body layout detected")
            else:
                print(f"  ⚠️  Flexbox body layout not found")

        else:
            print(f"  ❌ Cannot load design_system.css (Status {response.status_code})")
    except Exception as e:
        print(f"  ❌ Design system test error: {str(e)}")

def main():
    """Run comprehensive CSS layout validation tests"""
    print("🚀 Money Circle CSS Layout Fix Validation")
    print("=" * 50)
    print(f"Testing at: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Run all tests
    test_css_file_loading()
    test_page_accessibility()
    test_html_structure()
    test_design_system_integration()

    print("\n" + "=" * 50)
    print("✅ CSS Layout Validation Complete!")
    print("\n📋 Summary:")
    print("  - All CSS files should be loading correctly")
    print("  - Pages should return 200 status (or 302 if login required)")
    print("  - HTML structure elements should be present")
    print("  - Design system variables should be loaded")
    print("  - Professional grid layout should be active")

    print("\n🌐 Manual Testing Recommended:")
    print("  1. Open each page in browser")
    print("  2. Verify professional card-based layout")
    print("  3. Check glassmorphism effects and spacing")
    print("  4. Test responsive behavior on mobile")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)
