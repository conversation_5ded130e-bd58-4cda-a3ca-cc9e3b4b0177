#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE ACCOUNT INTEGRATION AUDIT
Diagnose why the dashboard is showing incorrect/stale account balance
"""

import asyncio
import logging
import os
import yaml
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def audit_account_integration():
    """Comprehensive audit of account integration chain."""
    
    logger.info("🔍 COMPREHENSIVE ACCOUNT INTEGRATION AUDIT")
    logger.info("=" * 60)
    
    try:
        # Load configuration
        config_path = Path("../config.yaml")
        if not config_path.exists():
            config_path = Path("config.yaml")
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"✅ Configuration loaded from {config_path}")
        
        # Load .env file
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            logger.info("✅ .env file loaded")
        else:
            logger.warning("⚠️ .env file not found")
        
        # STEP 1: Test HTX Client Connection
        logger.info("\n🔗 STEP 1: HTX CLIENT CONNECTION TEST")
        logger.info("-" * 50)
        
        from exchange.ccxt_htx_client import create_htx_client
        
        htx_client = create_htx_client(config)
        
        # Check API credentials
        api_key = os.getenv('HTX_API_KEY')
        api_secret = os.getenv('HTX_API_SECRET')
        
        if api_key and api_secret:
            logger.info(f"✅ HTX API credentials configured")
            logger.info(f"   API Key: {api_key[:8]}...{api_key[-4:]}")
        else:
            logger.error("❌ HTX API credentials missing")
            return False
        
        # Test connection
        connected = await htx_client.connect()
        if connected:
            logger.info("✅ HTX client connected successfully")
        else:
            logger.error("❌ HTX client connection failed")
            return False
        
        # Test account balance
        balance = await htx_client.get_account_balance()
        if balance:
            usdt_balance = balance.get('USDT', {})
            total = usdt_balance.get('total', 0)
            free = usdt_balance.get('free', 0)
            used = usdt_balance.get('used', 0)
            
            logger.info(f"✅ Real HTX Account Balance:")
            logger.info(f"   Total: ${total:.2f}")
            logger.info(f"   Available: ${free:.2f}")
            logger.info(f"   Used: ${used:.2f}")
        else:
            logger.error("❌ Failed to get account balance from HTX")
            return False
        
        # Test positions
        positions = await htx_client.get_positions()
        if positions is not None:
            logger.info(f"✅ Positions: {len(positions)} open positions")
            for pos in positions:
                symbol = pos.get('symbol')
                side = pos.get('side')
                contracts = pos.get('contracts', 0)
                pnl = pos.get('unrealizedPnl', 0)
                logger.info(f"   📈 {symbol} {side} {contracts} contracts | PnL: ${pnl:.3f}")
        else:
            logger.warning("⚠️ Failed to get positions from HTX")
        
        # STEP 2: Test Account Tracker
        logger.info("\n🏦 STEP 2: ACCOUNT TRACKER TEST")
        logger.info("-" * 50)
        
        from monitoring.account_tracker import LiveAccountTracker
        
        account_tracker = LiveAccountTracker(config, htx_client)
        
        # Force update
        await account_tracker._update_account_snapshot()
        
        # Get snapshot
        snapshot = account_tracker.get_current_snapshot()
        if snapshot:
            logger.info("✅ Account tracker snapshot created:")
            logger.info(f"   Total Balance: ${snapshot.total_balance:.2f}")
            logger.info(f"   Available: ${snapshot.available_balance:.2f}")
            logger.info(f"   Margin Used: {snapshot.margin_used_pct:.1f}%")
            logger.info(f"   Risk Level: {snapshot.risk_level}")
            logger.info(f"   Open Positions: {snapshot.open_positions}")
            logger.info(f"   Unrealized PnL: ${snapshot.unrealized_pnl:.3f}")
        else:
            logger.error("❌ Account tracker failed to create snapshot")
            return False
        
        # Get account summary
        summary = account_tracker.get_account_summary()
        logger.info("✅ Account summary:")
        logger.info(f"   Balance: ${summary.get('balance', 0):.2f}")
        logger.info(f"   Available: ${summary.get('available_balance', 0):.2f}")
        logger.info(f"   Margin Used: {summary.get('margin_used_pct', 0):.1f}%")
        
        # STEP 3: Test Execution Controller Integration
        logger.info("\n🎯 STEP 3: EXECUTION CONTROLLER INTEGRATION")
        logger.info("-" * 50)
        
        from execution.execution_controller import ExecutionController
        
        execution_controller = ExecutionController(config)
        
        # Check if account tracker is properly injected
        if hasattr(execution_controller, 'account_tracker') and execution_controller.account_tracker:
            logger.info("✅ Execution controller has account tracker")
            
            # Test account tracker in execution controller
            ec_snapshot = execution_controller.account_tracker.get_current_snapshot()
            if ec_snapshot:
                logger.info(f"✅ Execution controller account data:")
                logger.info(f"   Balance: ${ec_snapshot.total_balance:.2f}")
                logger.info(f"   Risk Level: {ec_snapshot.risk_level}")
            else:
                logger.error("❌ Execution controller account tracker has no snapshot")
        else:
            logger.error("❌ Execution controller missing account tracker")
        
        # STEP 4: Test Dashboard API Integration
        logger.info("\n🌐 STEP 4: DASHBOARD API INTEGRATION")
        logger.info("-" * 50)
        
        from ui.ai_strategy_tuner import AIStrategyTunerDashboard
        from storage.live_store import LiveDataStore
        
        # Create data store
        data_store = LiveDataStore(config)
        
        # Create dashboard
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
        
        # Test account summary API method directly
        class MockRequest:
            pass
        
        mock_request = MockRequest()
        
        # Call the API method directly
        try:
            # This simulates what happens when the dashboard calls /api/account/summary
            response = await dashboard.api_get_account_summary(mock_request)
            
            if hasattr(response, 'body'):
                import json
                response_data = json.loads(response.body.decode())
                
                logger.info("✅ Dashboard API response:")
                logger.info(f"   Success: {response_data.get('success')}")
                logger.info(f"   Total Balance: ${response_data.get('total_balance', 0):.2f}")
                logger.info(f"   Available: ${response_data.get('available_balance', 0):.2f}")
                logger.info(f"   Data Source: {response_data.get('data_source', 'unknown')}")
                logger.info(f"   Connected: {response_data.get('connected', False)}")
                
                # Check if it's using mock data
                if response_data.get('data_source') == 'mock':
                    logger.error("❌ CRITICAL: Dashboard is returning MOCK DATA!")
                    logger.error("   This explains why the balance is incorrect")
                elif response_data.get('data_source') == 'fallback':
                    logger.error("❌ CRITICAL: Dashboard is using FALLBACK DATA!")
                    logger.error("   Account tracker is not providing live data")
                elif response_data.get('data_source') == 'live_htx':
                    logger.info("✅ Dashboard is using LIVE HTX DATA")
                else:
                    logger.warning(f"⚠️ Unknown data source: {response_data.get('data_source')}")
                
            else:
                logger.error("❌ Dashboard API returned invalid response")
                
        except Exception as e:
            logger.error(f"❌ Dashboard API test failed: {e}")
        
        # STEP 5: Identify Root Cause
        logger.info("\n🎯 STEP 5: ROOT CAUSE ANALYSIS")
        logger.info("-" * 50)
        
        # Check if execution controller has account tracker
        if not hasattr(execution_controller, 'account_tracker') or not execution_controller.account_tracker:
            logger.error("🚨 ROOT CAUSE: Execution controller missing account tracker")
            logger.error("   The dashboard cannot get live data without account tracker")
            return False
        
        # Check if account tracker has HTX client
        if not hasattr(execution_controller.account_tracker, 'htx_client') or not execution_controller.account_tracker.htx_client:
            logger.error("🚨 ROOT CAUSE: Account tracker missing HTX client")
            logger.error("   The account tracker cannot get live data without HTX client")
            return False
        
        # Check if HTX client is connected
        if not execution_controller.account_tracker.htx_client.is_connected:
            logger.error("🚨 ROOT CAUSE: HTX client not connected")
            logger.error("   The HTX client must be connected to get live data")
            return False
        
        # Check if account tracker has current snapshot
        if not execution_controller.account_tracker.get_current_snapshot():
            logger.error("🚨 ROOT CAUSE: Account tracker has no current snapshot")
            logger.error("   The account tracker failed to create a snapshot from HTX data")
            return False
        
        logger.info("✅ All integration components are working correctly")
        logger.info("🎉 Account integration audit PASSED")
        
        # Cleanup
        await htx_client.disconnect()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during account integration audit: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(audit_account_integration())
    if success:
        print("\n✅ ACCOUNT INTEGRATION: WORKING")
    else:
        print("\n❌ ACCOUNT INTEGRATION: BROKEN")
        print("🔧 Fix the identified issues and run the audit again")
        exit(1)
