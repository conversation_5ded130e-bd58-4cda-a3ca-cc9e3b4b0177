#!/usr/bin/env python3
"""
FINAL Money Circle Platform Test
Comprehensive test to verify the platform works without ANY middleware errors
"""

import asyncio
import aiohttp
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_platform():
    """Test complete Money Circle platform functionality."""
    logger.info("🧪 FINAL MONEY CIRCLE PLATFORM TEST")
    logger.info("=" * 60)
    logger.info("🎯 Testing all core functionality without middleware errors")
    logger.info("")
    
    base_url = "http://localhost:8086"
    tests_passed = 0
    total_tests = 8
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # Test 1: Health endpoint
            logger.info("📋 Test 1: Health Endpoint")
            try:
                async with session.get(f"{base_url}/health") as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        logger.info(f"✅ Health endpoint: {data.get('status', 'unknown')}")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ Health endpoint failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ Health endpoint error: {e}")
            
            # Test 2: Homepage redirect
            logger.info("\n📋 Test 2: Homepage Redirect")
            try:
                async with session.get(base_url, allow_redirects=False) as resp:
                    if resp.status == 302:
                        location = resp.headers.get('Location', '')
                        logger.info(f"✅ Homepage redirects to: {location}")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ Homepage failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ Homepage error: {e}")
            
            # Test 3: Login page
            logger.info("\n📋 Test 3: Login Page")
            try:
                async with session.get(f"{base_url}/login") as resp:
                    if resp.status == 200:
                        content = await resp.text()
                        if 'Money Circle' in content and 'login' in content.lower():
                            logger.info("✅ Login page loads with correct content")
                            tests_passed += 1
                        else:
                            logger.warning("⚠️ Login page content incomplete")
                    else:
                        logger.error(f"❌ Login page failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ Login page error: {e}")
            
            # Test 4: Register page
            logger.info("\n📋 Test 4: Register Page")
            try:
                async with session.get(f"{base_url}/register") as resp:
                    if resp.status == 200:
                        logger.info("✅ Register page loads correctly")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ Register page failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ Register page error: {e}")
            
            # Test 5: Static files
            logger.info("\n📋 Test 5: Static Files")
            try:
                async with session.get(f"{base_url}/static/css/dashboard.css") as resp:
                    if resp.status == 200:
                        logger.info("✅ Static CSS files serving correctly")
                        tests_passed += 1
                    else:
                        logger.warning(f"⚠️ Static files issue: {resp.status}")
            except Exception as e:
                logger.warning(f"⚠️ Static files warning: {e}")
            
            # Test 6: API endpoints (should require auth)
            logger.info("\n📋 Test 6: API Authentication")
            try:
                async with session.get(f"{base_url}/api/system/status") as resp:
                    if resp.status in [200, 302, 401, 403]:  # Valid responses
                        logger.info(f"✅ API endpoints responding correctly: {resp.status}")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ API endpoints failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ API endpoints error: {e}")
            
            # Test 7: Dashboard redirect (should redirect to login)
            logger.info("\n📋 Test 7: Dashboard Authentication")
            try:
                async with session.get(f"{base_url}/dashboard", allow_redirects=False) as resp:
                    if resp.status == 302:
                        location = resp.headers.get('Location', '')
                        if '/login' in location:
                            logger.info("✅ Dashboard correctly redirects to login")
                            tests_passed += 1
                        else:
                            logger.warning(f"⚠️ Dashboard redirects to: {location}")
                    else:
                        logger.error(f"❌ Dashboard authentication failed: {resp.status}")
            except Exception as e:
                logger.error(f"❌ Dashboard authentication error: {e}")
            
            # Test 8: Multiple rapid requests (stress test)
            logger.info("\n📋 Test 8: Rapid Requests Stress Test")
            try:
                start_time = time.time()
                tasks = []
                for i in range(10):
                    task = session.get(f"{base_url}/health")
                    tasks.append(task)
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(1 for r in responses if hasattr(r, 'status') and r.status == 200)
                
                if success_count >= 8:  # Allow some failures
                    elapsed = time.time() - start_time
                    logger.info(f"✅ Stress test: {success_count}/10 requests successful in {elapsed:.2f}s")
                    tests_passed += 1
                else:
                    logger.error(f"❌ Stress test failed: only {success_count}/10 successful")
                    
                # Close all responses
                for r in responses:
                    if hasattr(r, 'close'):
                        r.close()
                        
            except Exception as e:
                logger.error(f"❌ Stress test error: {e}")
    
    except Exception as e:
        logger.error(f"❌ Test session error: {e}")
    
    # Final Results
    logger.info("\n" + "=" * 60)
    logger.info("📊 FINAL TEST RESULTS")
    logger.info("=" * 60)
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")
    logger.info(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        logger.info("\n🎉 PERFECT! ALL TESTS PASSED!")
        logger.info("✅ Money Circle platform is 100% functional")
        logger.info("✅ No middleware errors detected")
        logger.info("✅ All core features working correctly")
        logger.info("✅ Platform ready for GitHub and Render deployment")
        
        logger.info(f"\n🌐 Platform Access: {base_url}")
        logger.info("🔐 Login Credentials: epinnox / securepass123")
        logger.info("\n🎯 Ready to test all features:")
        logger.info("   • 👤 Admin Dashboard")
        logger.info("   • 🤖 Auto Trader")
        logger.info("   • 📡 Trading Signals")
        logger.info("   • 📊 Portfolio Analytics")
        logger.info("   • 👥 Social Trading")
        logger.info("\n🚀 DEPLOYMENT READY!")
        
    elif tests_passed >= total_tests * 0.8:
        logger.info("\n✅ EXCELLENT! Platform is working well")
        logger.info("⚠️ Minor issues detected but platform is functional")
        logger.info("🎯 Ready for testing and deployment")
        
    else:
        logger.error("\n❌ CRITICAL ISSUES DETECTED")
        logger.error("🔧 Platform needs fixes before deployment")
    
    return tests_passed >= total_tests * 0.8

async def main():
    """Main test function."""
    print("🧪 Money Circle Final Platform Test")
    print("Testing complete platform without middleware errors")
    print()
    
    try:
        success = await test_complete_platform()
        
        if success:
            print("\n🎊 MONEY CIRCLE IS READY!")
            print("🌟 Platform tested and verified")
            print("🚀 Ready for production deployment")
        else:
            print("\n❌ Platform needs attention")
            print("🔧 Check server logs for details")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        return 1

if __name__ == '__main__':
    exit(asyncio.run(main()))
