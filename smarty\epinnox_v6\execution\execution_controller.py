#!/usr/bin/env python3
"""
Execution Intelligence Controller
Phase 6: Orchestrates the complete execution intelligence pipeline
"""

import logging
import time
import asyncio
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .trade_executor import RiskAwareTradeExecutor, TradeExecution
from .order_logic import AdaptiveOrderLogic, OrderRecommendation
from .execution_memory import ExecutionMemory
from .post_trade_reflection import PostTradeReflection
from exchange.htx_client import HTXClient

logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """Complete execution result with all intelligence layers."""
    decision_id: str
    execution: Optional[TradeExecution]
    order_recommendation: OrderRecommendation
    execution_quality: float
    reflection_triggered: bool
    performance_impact: float
    timestamp: float

class ExecutionController:
    """
    Master controller for Phase 6 Execution Intelligence.
    Orchestrates risk-aware execution, adaptive order logic, memory tracking,
    and post-trade reflection for continuous improvement.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.execution_config = config.get('execution_intelligence', {})

        # Initialize components
        self.trade_executor = RiskAwareTradeExecutor(config)
        self.order_logic = AdaptiveOrderLogic(config)
        self.execution_memory = ExecutionMemory(config)
        self.post_trade_reflection = PostTradeReflection(config)

        # Initialize HTX client for live trading (Phase 8: CCXT)
        from exchange.ccxt_htx_client import create_htx_client
        self.htx_client = create_htx_client(config)
        self.live_trading_enabled = config.get('development', {}).get('simulate_trades', True) == False

        # Initialize account tracker for Phase 8
        from monitoring.account_tracker import LiveAccountTracker
        self.account_tracker = LiveAccountTracker(config, self.htx_client)

        # 🔥 PRODUCTION SAFETY CONFIGURATION
        self.margin_usage_limit = float(os.getenv('MAX_MARGIN_USAGE', '10.0'))  # Hard 10% limit
        self.liquidation_buffer_min = float(os.getenv('LIQUIDATION_BUFFER_MIN', '2.5'))
        self.max_open_positions = int(os.getenv('MAX_OPEN_POSITIONS', '1'))
        self.max_position_size = float(os.getenv('MAX_POSITION_SIZE', '1.0'))
        self.max_daily_loss = float(os.getenv('MAX_DAILY_LOSS', '1.0'))
        self.max_drawdown_pct = float(os.getenv('MAX_DRAWDOWN_PCT', '20.0'))
        self.consecutive_loss_limit = int(os.getenv('CONSECUTIVE_LOSS_LIMIT', '3'))

        # Daily tracking
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.emergency_stopped = False

        # Execution settings
        self.auto_execute = self.execution_config.get('auto_execute', True)
        self.reflection_enabled = self.execution_config.get('reflection_enabled', True)
        self.performance_tracking = self.execution_config.get('performance_tracking', True)

        # Active positions tracking
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        self.pending_reflections: List[Dict[str, Any]] = []

        # Performance metrics
        self.execution_stats = {
            'total_decisions': 0,
            'executed_decisions': 0,
            'skipped_decisions': 0,
            'avg_execution_quality': 0.0,
            'total_pnl': 0.0
        }

        # Smart Strategy reference (will be injected)
        self.smart_strategy = None

        logger.info("Execution Intelligence Controller initialized")

        # Start account monitoring for Phase 8
        if self.live_trading_enabled:
            # 🔧 CRITICAL FIX: Initialize account tracker with first snapshot
            asyncio.create_task(self._initialize_account_tracker())
            logger.info("🏦 Live account monitoring started")

    def set_smart_strategy(self, smart_strategy):
        """🎯 Inject Smart Strategy reference for strategy mode updates."""
        self.smart_strategy = smart_strategy
        # Set signal callback to connect signal generation to execution
        smart_strategy.set_signal_callback(self.process_signal_for_execution)
        logger.info("🧠 Smart Strategy reference injected into Execution Controller")
        logger.info("🔗 Signal callback connected for automatic execution")

    def update_strategy_mode(self, strategy_mode: str):
        """🎯 Update strategy mode in Smart Strategy."""
        if self.smart_strategy and hasattr(self.smart_strategy, 'set_strategy_mode'):
            self.smart_strategy.set_strategy_mode(strategy_mode)
            logger.info(f"🎯 Strategy mode updated to: {strategy_mode}")
        else:
            logger.warning("⚠️ Smart Strategy not available for strategy mode update")

    async def process_signal_for_execution(self, signal_data: Dict[str, Any]):
        """🔗 Process trading signal from Smart Strategy for execution."""
        try:
            # Convert signal to trading decision format
            decision = {
                'symbol': signal_data.get('symbol', 'DOGE/USDT:USDT'),
                'action': signal_data.get('action', 'WAIT'),
                'confidence': signal_data.get('confidence', 0.0),
                'conviction_score': int(signal_data.get('confidence', 0.0) * 10),  # Scale to 1-10
                'reasoning': signal_data.get('reasoning', 'No reasoning provided'),
                'market_regime': 'normal',  # Default regime
                'timestamp': signal_data.get('timestamp', time.time())
            }

            # Get current market data (mock for now, should get from data store)
            market_data = {
                'last_price': signal_data.get('price', 0.40),  # DOGE approximate price
                'volume': 1000000,  # Mock volume
                'volatility': 0.02,  # Mock volatility
                'timestamp': time.time()
            }

            logger.info(f"🔗 Processing signal for execution: {decision['action']} {decision['symbol']} "
                       f"(confidence: {decision['confidence']:.2%})")

            # Process through execution intelligence pipeline
            result = await self.process_trading_decision(decision, market_data)

            if result and result.execution:
                logger.info(f"✅ Signal executed successfully: {result.execution.symbol} "
                           f"{result.execution.action} {result.execution.size:.2f}")
            else:
                logger.info(f"⏸️ Signal not executed (filtered by execution intelligence)")

        except Exception as e:
            logger.error(f"❌ Error processing signal for execution: {e}")

    async def _initialize_account_tracker(self):
        """🔧 CRITICAL FIX: Initialize account tracker with first snapshot."""
        try:
            if self.account_tracker:
                # Force initial update to get first snapshot
                await self.account_tracker._update_account_snapshot()

                # Start monitoring
                await self.account_tracker.start_monitoring()

                # Verify snapshot was created
                snapshot = self.account_tracker.get_current_snapshot()
                if snapshot:
                    logger.info(f"✅ Account tracker initialized with balance: ${snapshot.total_balance:.2f}")
                else:
                    logger.error("❌ Account tracker failed to create initial snapshot")
            else:
                logger.error("❌ No account tracker available for initialization")
        except Exception as e:
            logger.error(f"❌ Error initializing account tracker: {e}")

    async def process_trading_decision(self, decision: Dict[str, Any],
                                     market_data: Dict[str, Any]) -> ExecutionResult:
        """
        Process a trading decision through the complete execution intelligence pipeline.

        Args:
            decision: LLM trading decision with confidence, action, reasoning
            market_data: Current market data for execution

        Returns:
            ExecutionResult with complete execution analysis
        """
        try:
            decision_id = f"{decision.get('symbol', 'UNKNOWN')}_{int(time.time() * 1000)}"
            start_time = time.time()

            self.execution_stats['total_decisions'] += 1

            # Step 1: Get order recommendation from adaptive logic
            order_recommendation = await self._get_order_recommendation(decision, market_data)

            # Step 2: Execute trade if recommended
            execution = None
            if order_recommendation.strategy.value != 'skip_unfavorable' and self.auto_execute:
                execution = await self._execute_with_intelligence(
                    decision, market_data, order_recommendation
                )

            # Step 3: Record execution in memory
            execution_quality = 0.0
            if execution:
                execution_id = self.execution_memory.record_execution({
                    'execution_id': execution.execution_id,
                    'symbol': execution.symbol,
                    'timestamp': execution.timestamp,
                    'action': execution.action,
                    'size': execution.size,
                    'entry_price': execution.entry_price,
                    'execution_price': execution.execution_price,
                    'slippage': execution.slippage,
                    'latency_ms': execution.latency_ms,
                    'fill_quality': execution.fill_quality,
                    'order_type': execution.order_type,
                    'confidence': execution.confidence,
                    'conviction_score': execution.conviction_score,
                    'market_regime': execution.market_regime
                })

                execution_quality = execution.fill_quality
                self.execution_stats['executed_decisions'] += 1

                # Track active position
                self.active_positions[execution.execution_id] = {
                    'execution': execution,
                    'decision': decision,
                    'entry_time': time.time()
                }
            else:
                self.execution_stats['skipped_decisions'] += 1

            # Step 4: Calculate performance impact
            performance_impact = self._calculate_performance_impact(execution, decision)

            # Step 5: Update statistics
            self._update_execution_stats(execution_quality)

            # Create result
            result = ExecutionResult(
                decision_id=decision_id,
                execution=execution,
                order_recommendation=order_recommendation,
                execution_quality=execution_quality,
                reflection_triggered=False,  # Will be updated when position closes
                performance_impact=performance_impact,
                timestamp=start_time
            )

            # Only log executed decisions or first few skipped ones to reduce noise
            if execution or self.execution_stats['skipped_decisions'] <= 3:
                logger.info(f"Processed decision {decision_id}: "
                           f"{'EXECUTED' if execution else 'SKIPPED'} "
                           f"(Quality: {execution_quality:.2f})")
            else:
                logger.debug(f"Decision {decision_id}: SKIPPED (Quality: {execution_quality:.2f})")

            return result

        except Exception as e:
            logger.error(f"Error processing trading decision: {e}")
            return self._create_error_result(decision, str(e))

    async def update_position_status(self, symbol: str, current_price: float):
        """Update status of active positions and trigger reflections when closed."""
        try:
            positions_to_close = []

            for execution_id, position_data in self.active_positions.items():
                execution = position_data['execution']

                if execution.symbol == symbol:
                    # Update unrealized PnL
                    pnl = self.execution_memory.update_position_pnl(execution_id, current_price)

                    # Check for position closure conditions
                    if self._should_close_position(position_data, current_price):
                        positions_to_close.append(execution_id)

            # Close positions and trigger reflections
            for execution_id in positions_to_close:
                await self._close_position_and_reflect(execution_id, current_price)

        except Exception as e:
            logger.error(f"Error updating position status: {e}")

    async def _get_order_recommendation(self, decision: Dict[str, Any],
                                      market_data: Dict[str, Any]) -> OrderRecommendation:
        """Get intelligent order recommendation."""
        try:
            # Calculate position size for recommendation
            confidence = decision.get('confidence', 0.0)
            conviction_score = decision.get('conviction_score', 1)
            market_regime = decision.get('market_regime', 'unknown')

            # Estimate position size
            position_size = self.trade_executor._calculate_position_size(
                confidence, conviction_score, market_regime, decision.get('symbol', 'BTC-USDT')
            )

            # Get recommendation
            recommendation = self.order_logic.recommend_order_strategy(
                decision, market_data, position_size
            )

            logger.debug(f"Order recommendation: {recommendation.strategy.value} "
                        f"(confidence: {recommendation.confidence:.2f})")

            return recommendation

        except Exception as e:
            logger.error(f"Error getting order recommendation: {e}")
            return self.order_logic._get_fallback_recommendation()

    async def _execute_with_intelligence(self, decision: Dict[str, Any],
                                       market_data: Dict[str, Any],
                                       recommendation: OrderRecommendation) -> Optional[TradeExecution]:
        """Execute trade with intelligent order handling."""
        try:
            # Apply order recommendation parameters
            if recommendation.time_delay > 0:
                logger.debug(f"Delaying execution by {recommendation.time_delay}s")
                await asyncio.sleep(recommendation.time_delay)

            # Adjust price if needed
            current_price = market_data.get('last_price', 0.0)
            if recommendation.price_offset != 0:
                adjusted_price = current_price * (1 + recommendation.price_offset)
                market_data = {**market_data, 'last_price': adjusted_price}
                logger.debug(f"Price adjusted by {recommendation.price_offset*100:.2f}%")

            # Execute the trade (simulation or live)
            if self.live_trading_enabled:
                execution = await self._execute_live_trade(decision, market_data)
            else:
                execution = await self.trade_executor.execute_decision(decision, market_data)

            if execution:
                logger.info(f"{'LIVE' if self.live_trading_enabled else 'SIM'} execution completed: "
                          f"{execution.symbol} {execution.action} {execution.size:.2f} @ ${execution.execution_price:.2f}")

            return execution

        except Exception as e:
            logger.error(f"Error in intelligent execution: {e}")
            return None

    async def _execute_live_trade(self, decision: Dict[str, Any],
                                 market_data: Dict[str, Any]) -> Optional[TradeExecution]:
        """Execute live trade through HTX API."""
        try:
            symbol = decision.get('symbol', 'DOGE-USDT')
            action = decision.get('action', 'WAIT')
            confidence = decision.get('confidence', 0.0)
            conviction_score = decision.get('conviction_score', 1)
            market_regime = decision.get('market_regime', 'unknown')

            # Skip WAIT actions
            if action.upper() == 'WAIT':
                logger.info(f"🔄 WAIT decision - no trade executed")
                return None

            # 🚨 PRODUCTION SAFETY CHECKS
            if self.emergency_stopped:
                logger.warning("🛑 Emergency stop active - no trades allowed")
                return None

            # Check daily loss limit
            if self.daily_pnl <= -self.max_daily_loss:
                logger.warning(f"🚨 Daily loss limit reached: ${self.daily_pnl:.2f} <= -${self.max_daily_loss:.2f}")
                self._trigger_emergency_stop("Daily loss limit exceeded")
                return None

            # Check consecutive losses
            if self.consecutive_losses >= self.consecutive_loss_limit:
                logger.warning(f"🚨 Consecutive loss limit reached: {self.consecutive_losses} >= {self.consecutive_loss_limit}")
                self._trigger_emergency_stop("Consecutive loss limit exceeded")
                return None

            # 🔥 CRITICAL: Check max open positions limit
            current_open_positions = len(self.active_positions)
            if current_open_positions >= self.max_open_positions:
                logger.warning(f"🚨 Maximum positions reached: {current_open_positions}/{self.max_open_positions} - trade blocked")
                return None

            # 🛑 CRITICAL: Pre-trade margin safety check
            account_summary = self.account_tracker.get_account_summary()
            current_margin_pct = account_summary.get('margin_used_pct', 0.0)

            # Abort if margin usage exceeds production limit
            if current_margin_pct >= self.margin_usage_limit:
                logger.warning(f"❌ Aborting trade: margin used at {current_margin_pct:.1f}% exceeds limit ({self.margin_usage_limit}%)")
                return None

            # Phase 8: Account-aware safety checks
            can_trade, warnings = self.account_tracker.can_place_trade(
                trade_size=4.0,  # Estimate max trade size
                direction=action
            )

            if not can_trade:
                # Only log trade blocking once per minute to reduce noise
                current_time = time.time()
                if not hasattr(self, '_last_trade_block_log') or current_time - self._last_trade_block_log > 60:
                    logger.warning(f"🚨 Trade blocked by account safety: {'; '.join(warnings)}")
                    self._last_trade_block_log = current_time
                return None

            if warnings:
                for warning in warnings:
                    logger.warning(f"⚠️ Account warning: {warning}")

            # Calculate position size with margin constraints
            base_position_size = self.trade_executor._calculate_position_size(
                confidence, conviction_score, market_regime, symbol
            )

            if base_position_size <= 0:
                logger.warning(f"⚠️ Position size too small: {base_position_size}")
                return None

            # 🔐 PRODUCTION: Hard position size limits
            available_margin = account_summary.get('available_balance', 0.0)
            max_safe_position = available_margin * 0.8  # Use only 80% of available margin for safety

            # Enforce hard position size limit from .env
            position_size = min(base_position_size, max_safe_position, self.max_position_size)

            if position_size != base_position_size:
                logger.info(f"🔧 Position size capped by available margin: ${base_position_size:.2f} → ${position_size:.2f}")

            if position_size <= 0:
                logger.warning(f"⚠️ No available margin for trade (available: ${available_margin:.2f})")
                return None

            # Get current price
            current_price = market_data.get('last_price', 0.0)
            if current_price <= 0:
                logger.error(f"❌ Invalid current price: {current_price}")
                return None

            # Convert USD position size to actual token amount
            # position_size is in USD, we need to convert to number of tokens
            token_amount = position_size / current_price

            # CRITICAL: Ensure minimum position size for HTX (minimum 1 DOGE)
            min_token_amount = 1.0  # HTX minimum for DOGE/USDT:USDT
            if token_amount < min_token_amount:
                token_amount = min_token_amount
                logger.info(f"🔧 Adjusted position size to minimum: {token_amount} DOGE (was {position_size / current_price:.4f})")

            logger.info(f"💰 Position sizing: ${position_size:.2f} USD → {token_amount:.2f} DOGE @ ${current_price:.4f}")

            # Place order through CCXT HTX client
            order_result = await self.htx_client.place_market_order(
                symbol=symbol,
                side=action,
                amount=token_amount
            )

            if order_result:
                # Create execution record from CCXT order
                execution_price = float(order_result.get('average', current_price) or current_price)
                order_status = order_result.get('status', 'filled')  # HTX usually returns 'filled' for market orders

                execution = TradeExecution(
                    symbol=symbol,
                    action=action,
                    size=position_size,
                    entry_price=current_price,
                    execution_price=execution_price,
                    slippage=abs(execution_price - current_price) / current_price if current_price > 0 else 0,
                    latency_ms=100,  # Estimated
                    order_type='market',
                    confidence=confidence,
                    conviction_score=conviction_score,
                    market_regime=market_regime,
                    timestamp=time.time(),
                    execution_id=order_result['id'],
                    status=order_status,  # ✅ FIX: Add missing status parameter
                    fill_quality=0.95  # Estimated for live trading
                )

                logger.info(f"🚀 LIVE TRADE EXECUTED: {symbol} {action} {position_size:.2f} @ ${current_price:.4f}")
                return execution
            else:
                logger.error(f"❌ Failed to place live order: {symbol} {action}")
                return None

        except Exception as e:
            logger.error(f"Error executing live trade: {e}")
            # Fall back to simulation
            return await self.trade_executor.execute_decision(decision, market_data)

    def _calculate_performance_impact(self, execution: Optional[TradeExecution],
                                    decision: Dict[str, Any]) -> float:
        """Calculate expected performance impact of the execution."""
        try:
            if not execution:
                return 0.0

            # Base impact from position size and confidence
            confidence = decision.get('confidence', 0.0)
            conviction_score = decision.get('conviction_score', 1)

            # Estimate impact based on execution quality and decision strength
            base_impact = execution.size * execution.execution_price * 0.001  # 0.1% base
            confidence_multiplier = 0.5 + (confidence * 1.5)  # 0.5x to 2x
            conviction_multiplier = 0.8 + (conviction_score / 5 * 0.4)  # 0.8x to 1.2x
            quality_multiplier = 0.5 + (execution.fill_quality * 0.5)  # 0.5x to 1x

            impact = base_impact * confidence_multiplier * conviction_multiplier * quality_multiplier

            return impact

        except Exception as e:
            logger.error(f"Error calculating performance impact: {e}")
            return 0.0

    def _should_close_position(self, position_data: Dict[str, Any], current_price: float) -> bool:
        """Determine if position should be closed."""
        try:
            execution = position_data['execution']
            entry_time = position_data['entry_time']

            # Time-based closure (example: close after 4 hours)
            max_hold_time = 4 * 3600  # 4 hours
            if time.time() - entry_time > max_hold_time:
                return True

            # PnL-based closure (example: close at +/-5%)
            if execution.action.upper() in ['LONG', 'BUY']:
                pnl_percentage = (current_price - execution.execution_price) / execution.execution_price
            else:
                pnl_percentage = (execution.execution_price - current_price) / execution.execution_price

            # Take profit at +5% or stop loss at -3%
            if pnl_percentage > 0.05 or pnl_percentage < -0.03:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking position closure: {e}")
            return False

    async def _close_position_and_reflect(self, execution_id: str, close_price: float):
        """Close position and trigger post-trade reflection."""
        try:
            position_data = self.active_positions.get(execution_id)
            if not position_data:
                return

            execution = position_data['execution']
            decision = position_data['decision']
            entry_time = position_data['entry_time']

            # Close position in memory
            realized_pnl = self.execution_memory.close_position(execution_id, close_price)

            # Prepare trade data for reflection
            trade_data = {
                'trade_id': execution_id,
                'original_decision': decision,
                'symbol': execution.symbol,
                'action': execution.action,
                'size': execution.size,
                'entry_price': execution.execution_price,
                'exit_price': close_price,
                'pnl': realized_pnl or 0.0,
                'hold_duration': (time.time() - entry_time) / 3600,  # Hours
                'exit_reason': 'automatic_closure',
                'market_change': 'stable'  # Could be enhanced with actual market analysis
            }

            # Trigger reflection if enabled
            if self.reflection_enabled:
                reflection = await self.post_trade_reflection.analyze_completed_trade(trade_data)
                if reflection:
                    logger.info(f"Post-trade reflection completed for {execution_id}")

            # Remove from active positions
            del self.active_positions[execution_id]

            # Update total PnL and daily tracking
            self.execution_stats['total_pnl'] += realized_pnl or 0.0
            self.daily_pnl += realized_pnl or 0.0

            # Track consecutive losses
            if realized_pnl and realized_pnl < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0  # Reset on profit

            # Check for emergency stop conditions
            if self.daily_pnl <= -self.max_daily_loss:
                self._trigger_emergency_stop(f"Daily loss limit exceeded: ${self.daily_pnl:.2f}")
            elif self.consecutive_losses >= self.consecutive_loss_limit:
                self._trigger_emergency_stop(f"Consecutive losses: {self.consecutive_losses}")

            logger.info(f"Position closed: {execution.symbol} {execution.action} "
                       f"PnL: ${realized_pnl:.2f} | Daily: ${self.daily_pnl:.2f} | Consecutive losses: {self.consecutive_losses}")

        except Exception as e:
            logger.error(f"Error closing position and reflecting: {e}")

    def _trigger_emergency_stop(self, reason: str):
        """🚨 Trigger emergency stop due to safety violation."""
        self.emergency_stopped = True
        logger.error(f"🚨 EMERGENCY STOP TRIGGERED: {reason}")
        logger.error("🛑 All trading activities halted")

        # TODO: Send alert notification (email, webhook, etc.)
        # TODO: Close all open positions if needed

    def reset_emergency_stop(self):
        """🔄 Reset emergency stop (manual intervention required)."""
        self.emergency_stopped = False
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        logger.info("🔄 Emergency stop reset - trading re-enabled")

    def _update_execution_stats(self, execution_quality: float):
        """Update execution statistics."""
        try:
            # Update average execution quality
            total_executed = self.execution_stats['executed_decisions']
            if total_executed > 0:
                current_avg = self.execution_stats['avg_execution_quality']
                new_avg = ((current_avg * (total_executed - 1)) + execution_quality) / total_executed
                self.execution_stats['avg_execution_quality'] = new_avg

        except Exception as e:
            logger.error(f"Error updating execution stats: {e}")

    def _create_error_result(self, decision: Dict[str, Any], error_msg: str) -> ExecutionResult:
        """Create error result for failed processing."""
        return ExecutionResult(
            decision_id=f"ERROR_{int(time.time() * 1000)}",
            execution=None,
            order_recommendation=self.order_logic._get_fallback_recommendation(),
            execution_quality=0.0,
            reflection_triggered=False,
            performance_impact=0.0,
            timestamp=time.time()
        )

    def get_execution_intelligence_status(self) -> Dict[str, Any]:
        """Get comprehensive status of execution intelligence system."""
        try:
            # Get component statuses
            executor_stats = self.trade_executor.get_execution_stats()
            memory_metrics = self.execution_memory.get_performance_metrics(24)
            reflection_summary = self.post_trade_reflection.get_reflection_summary(7)

            # Active positions summary
            active_positions_summary = {
                'count': len(self.active_positions),
                'symbols': list(set(pos['execution'].symbol for pos in self.active_positions.values())),
                'total_value': sum(
                    pos['execution'].size * pos['execution'].execution_price
                    for pos in self.active_positions.values()
                )
            }

            # Overall performance
            execution_rate = (self.execution_stats['executed_decisions'] /
                            max(self.execution_stats['total_decisions'], 1))

            return {
                'system_status': 'operational',
                'execution_stats': self.execution_stats,
                'execution_rate': execution_rate,
                'active_positions': active_positions_summary,
                'executor_performance': executor_stats,
                'memory_metrics': memory_metrics,
                'reflection_insights': reflection_summary,
                'components': {
                    'trade_executor': 'active',
                    'order_logic': 'active',
                    'execution_memory': 'active',
                    'post_trade_reflection': 'active' if self.reflection_enabled else 'disabled'
                },
                'configuration': {
                    'auto_execute': self.auto_execute,
                    'reflection_enabled': self.reflection_enabled,
                    'performance_tracking': self.performance_tracking
                }
            }

        except Exception as e:
            logger.error(f"Error getting execution intelligence status: {e}")
            return {
                'system_status': 'error',
                'error': str(e),
                'components': {
                    'trade_executor': 'unknown',
                    'order_logic': 'unknown',
                    'execution_memory': 'unknown',
                    'post_trade_reflection': 'unknown'
                }
            }

    async def force_close_all_positions(self, current_prices: Dict[str, float]):
        """Force close all active positions (emergency function)."""
        try:
            positions_to_close = list(self.active_positions.keys())

            for execution_id in positions_to_close:
                position_data = self.active_positions[execution_id]
                execution = position_data['execution']
                symbol = execution.symbol

                if symbol in current_prices:
                    await self._close_position_and_reflect(execution_id, current_prices[symbol])
                    logger.warning(f"Force closed position: {execution_id}")

            logger.info(f"Force closed {len(positions_to_close)} positions")

        except Exception as e:
            logger.error(f"Error force closing positions: {e}")

    def get_performance_insights(self) -> Dict[str, Any]:
        """Get actionable performance insights from execution intelligence."""
        try:
            # Get insights from memory
            memory_insights = self.execution_memory.get_execution_insights()

            # Get reflection insights
            reflection_summary = self.post_trade_reflection.get_reflection_summary(30)

            # Combine insights
            combined_insights = {
                'execution_quality': memory_insights.get('execution_quality', {}),
                'timing_patterns': memory_insights.get('timing_analysis', {}),
                'regime_performance': memory_insights.get('regime_performance', {}),
                'confidence_calibration': memory_insights.get('confidence_calibration', {}),
                'reflection_lessons': reflection_summary.get('top_lessons', []),
                'recommendations': []
            }

            # Generate combined recommendations
            recommendations = []

            # From execution memory
            if 'recommendations' in memory_insights:
                recommendations.extend(memory_insights['recommendations'])

            # From reflection system
            if reflection_summary.get('avg_confidence_adjustment', 0) < -0.05:
                recommendations.append("Confidence levels may be too high - consider more conservative thresholds")

            if reflection_summary.get('avg_reasoning_quality', 0.5) < 0.6:
                recommendations.append("Reasoning quality below optimal - enhance decision analysis depth")

            combined_insights['recommendations'] = recommendations

            return combined_insights

        except Exception as e:
            logger.error(f"Error getting performance insights: {e}")
            return {'error': str(e)}
