#!/usr/bin/env python3
"""
Debug and fix authentication issues in Money Circle.
"""

import sqlite3
import bcrypt
import os
from datetime import datetime

def check_database_users():
    """Check what users exist in the database."""
    db_path = 'data/money_circle.db'
    
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        
        # Check users table
        cursor = conn.execute("""
            SELECT username, email, role, is_active, hashed_password 
            FROM users 
            ORDER BY username
        """)
        users = cursor.fetchall()
        
        print("📊 Users in database:")
        print("-" * 60)
        
        if not users:
            print("❌ No users found in database!")
            return False
        
        for username, email, role, is_active, hashed_password in users:
            status = "✅ Active" if is_active else "❌ Inactive"
            has_password = "✅ Has password" if hashed_password else "❌ No password"
            print(f"  {username:15} | {email:20} | {role:8} | {status} | {has_password}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_password_verification():
    """Test password verification for demo users."""
    print("\n🔐 Testing password verification:")
    print("-" * 60)
    
    # Test passwords
    test_passwords = {
        'demo123': 'Demo password for most users',
        'securepass123': 'Admin password for epinnox'
    }
    
    for password, description in test_passwords.items():
        print(f"\nTesting password: '{password}' ({description})")
        
        # Create a test hash
        salt = bcrypt.gensalt()
        test_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        
        # Verify it works
        verification = bcrypt.checkpw(password.encode('utf-8'), test_hash)
        print(f"  Hash creation and verification: {'✅ Success' if verification else '❌ Failed'}")

def create_demo_users():
    """Create demo users with correct passwords."""
    print("\n👥 Creating demo users:")
    print("-" * 60)
    
    db_path = 'data/money_circle.db'
    conn = sqlite3.connect(db_path)
    
    # Demo users with correct usernames and passwords
    demo_users = [
        {
            'username': 'alex_trader',
            'email': '<EMAIL>',
            'password': 'demo123',
            'role': 'admin'
        },
        {
            'username': 'sarah_crypto',
            'email': '<EMAIL>', 
            'password': 'demo123',
            'role': 'member'
        },
        {
            'username': 'mike_scalper',
            'email': '<EMAIL>',
            'password': 'demo123',
            'role': 'member'
        },
        {
            'username': 'emma_hodler',
            'email': '<EMAIL>',
            'password': 'demo123',
            'role': 'member'
        },
        {
            'username': 'epinnox',
            'email': '<EMAIL>',
            'password': 'securepass123',
            'role': 'admin'
        }
    ]
    
    for user_data in demo_users:
        try:
            # Hash password
            password_hash = bcrypt.hashpw(
                user_data['password'].encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Insert or replace user
            conn.execute("""
                INSERT OR REPLACE INTO users (
                    username, email, hashed_password, role, 
                    date_joined, last_login, is_active, 
                    email_verified, agreement_accepted
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_data['username'],
                user_data['email'],
                password_hash,
                user_data['role'],
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                True,  # is_active
                True,  # email_verified
                True   # agreement_accepted
            ))
            
            print(f"  ✅ Created/Updated: {user_data['username']} (password: {user_data['password']})")
            
        except Exception as e:
            print(f"  ❌ Failed to create {user_data['username']}: {e}")
    
    conn.commit()
    conn.close()

def test_authentication():
    """Test authentication for all demo users."""
    print("\n🔑 Testing authentication:")
    print("-" * 60)
    
    # Import the authentication system
    try:
        from database.db_manager import DatabaseManager
        from auth.user_manager import UserManager
        
        db_manager = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db_manager)
        
        # Test users
        test_cases = [
            ('alex_trader', 'demo123'),
            ('sarah_crypto', 'demo123'),
            ('mike_scalper', 'demo123'),
            ('emma_hodler', 'demo123'),
            ('epinnox', 'securepass123'),
            ('invalid_user', 'demo123'),  # Should fail
            ('alex_trader', 'wrong_password')  # Should fail
        ]
        
        for username, password in test_cases:
            try:
                user = user_manager.authenticate_user(username, password, '127.0.0.1')
                if user:
                    print(f"  ✅ {username:15} | {password:15} | Authentication successful")
                else:
                    print(f"  ❌ {username:15} | {password:15} | Authentication failed")
            except Exception as e:
                print(f"  ❌ {username:15} | {password:15} | Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def fix_authentication_issues():
    """Main function to fix authentication issues."""
    print("🔧 Money Circle Authentication Fix")
    print("=" * 60)
    
    # Step 1: Check current database state
    print("\nStep 1: Checking database...")
    if not check_database_users():
        print("❌ Database check failed!")
        return False
    
    # Step 2: Test password verification
    print("\nStep 2: Testing password verification...")
    test_password_verification()
    
    # Step 3: Create/update demo users
    print("\nStep 3: Creating demo users...")
    create_demo_users()
    
    # Step 4: Verify users were created
    print("\nStep 4: Verifying users after creation...")
    check_database_users()
    
    # Step 5: Test authentication
    print("\nStep 5: Testing authentication...")
    auth_success = test_authentication()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 AUTHENTICATION FIX SUMMARY")
    print("=" * 60)
    
    if auth_success:
        print("✅ Authentication system is working correctly!")
        print("\n📋 Demo Login Credentials:")
        print("   Username: alex_trader    | Password: demo123")
        print("   Username: sarah_crypto   | Password: demo123") 
        print("   Username: mike_scalper   | Password: demo123")
        print("   Username: emma_hodler    | Password: demo123")
        print("   Username: epinnox        | Password: securepass123")
        print("\n🚀 Ready to test login page!")
    else:
        print("❌ Authentication system still has issues!")
        print("💡 Check the error messages above for details.")
    
    return auth_success

if __name__ == "__main__":
    success = fix_authentication_issues()
    exit(0 if success else 1)
