#!/usr/bin/env python3
"""
Test Multi-Symbol Autonomous Trading System
Comprehensive test of the new multi-symbol futures trading system.
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the new multi-symbol system
from autonomy.multi_symbol_orchestrator import MultiSymbolTradingOrchestrator

async def test_multi_symbol_system():
    """Test the complete multi-symbol autonomous trading system."""
    
    logger.info("🚀 Starting Multi-Symbol Autonomous Trading System Test")
    
    try:
        # Load configuration
        config_path = Path(__file__).parent / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Add multi-symbol specific configuration
        config['market_scanner'] = {
            'scan_interval': 30,
            'min_volume_24h': 1000000,
            'min_price_change': 0.02,
            'max_symbols': 20
        }
        
        config['symbol_selector'] = {
            'selection_interval': 60,
            'min_confidence': 0.6,
            'max_switches_per_hour': 6,
            'min_hold_duration': 300
        }
        
        config['multi_symbol_trader'] = {
            'enabled': True,
            'max_symbols': 5,
            'max_positions_per_symbol': 2,
            'total_capital': 1000.0,
            'max_risk_per_symbol': 0.05,
            'max_portfolio_risk': 0.15
        }
        
        config['enhanced_decision_engine'] = {
            'decision_interval': 30,
            'min_confidence': 0.7,
            'max_signals_per_cycle': 3,
            'regime_analysis': True
        }
        
        config['orchestrator'] = {
            'startup_delay': 5,  # Reduced for testing
            'health_check_interval': 30,
            'report_interval': 120
        }
        
        # Initialize the orchestrator
        logger.info("🎼 Initializing Multi-Symbol Trading Orchestrator...")
        orchestrator = MultiSymbolTradingOrchestrator(config)
        
        # Start the system
        logger.info("🚀 Starting the complete system...")
        await orchestrator.start_system()
        
        # Let the system run for a test period
        test_duration = 300  # 5 minutes
        logger.info(f"⏱️ Running system for {test_duration} seconds...")
        
        # Monitor system during test
        start_time = time.time()
        while time.time() - start_time < test_duration:
            # Get system status
            status = orchestrator.get_system_status()
            
            # Log status every 30 seconds
            if int(time.time() - start_time) % 30 == 0:
                logger.info("📊 System Status Update:")
                logger.info(f"   🏥 Health: {status['system_health']}")
                logger.info(f"   📈 Active Symbols: {len(status['active_symbols'])}")
                logger.info(f"   📊 Total Positions: {status['total_positions']}")
                logger.info(f"   💰 Daily PnL: ${status['daily_pnl']:.2f}")
                logger.info(f"   ⚖️ Components Active: {sum(status['components_active'].values())}/4")
            
            await asyncio.sleep(5)
        
        # Get final status
        final_status = orchestrator.get_system_status()
        logger.info("📊 Final System Status:")
        logger.info(f"   🏥 Health: {final_status['system_health']}")
        logger.info(f"   📈 Active Symbols: {final_status['active_symbols']}")
        logger.info(f"   📊 Total Positions: {final_status['total_positions']}")
        logger.info(f"   💰 Portfolio Value: ${final_status['portfolio_value']:.2f}")
        logger.info(f"   📈 Daily PnL: ${final_status['daily_pnl']:.2f}")
        logger.info(f"   ⏱️ Uptime: {final_status['uptime']:.1f}s")
        
        # Get component details
        logger.info("🔧 Component Status Details:")
        
        # Market Scanner
        scanner_status = orchestrator.get_component_status('market_scanner')
        logger.info(f"   📊 Market Scanner: {scanner_status.get('symbols_scanned', 0)} symbols, "
                   f"{scanner_status.get('opportunities_found', 0)} opportunities")
        
        # Symbol Selector
        selector_status = orchestrator.get_component_status('symbol_selector')
        logger.info(f"   🎯 Symbol Selector: {selector_status.get('total_selections', 0)} selections, "
                   f"{selector_status.get('symbol_switches', 0)} switches")
        
        # Multi-Symbol Trader
        trader_status = orchestrator.get_component_status('multi_symbol_trader')
        logger.info(f"   🤖 Multi-Symbol Trader: {len(trader_status.get('active_symbols', []))} active symbols, "
                   f"{trader_status['daily_stats']['trades_executed']} trades executed")
        
        # Decision Engine
        engine_status = orchestrator.get_component_status('decision_engine')
        logger.info(f"   🧠 Decision Engine: {engine_status.get('recent_signals_count', 0)} recent signals, "
                   f"avg confidence: {engine_status.get('engine_stats', {}).get('avg_confidence', 0):.2%}")
        
        # Stop the system
        logger.info("🛑 Stopping the system...")
        await orchestrator.stop_system()
        
        logger.info("✅ Multi-Symbol Autonomous Trading System test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

async def test_individual_components():
    """Test individual components separately."""
    
    logger.info("🧪 Testing Individual Components...")
    
    try:
        # Load basic config
        config = {
            'symbol': 'DOGE/USDT:USDT',
            'market_scanner': {'scan_interval': 10, 'min_volume_24h': 1000000},
            'symbol_selector': {'selection_interval': 30, 'min_confidence': 0.6},
            'multi_symbol_trader': {'enabled': True, 'total_capital': 1000.0},
            'enhanced_decision_engine': {'decision_interval': 20, 'min_confidence': 0.7}
        }
        
        # Test Market Scanner
        logger.info("📊 Testing Market Scanner...")
        from autonomy.market_scanner import MultiSymbolMarketScanner
        
        scanner = MultiSymbolMarketScanner(config)
        await scanner.start_scanning()
        
        # Let it scan for 30 seconds
        await asyncio.sleep(30)
        
        # Check results
        scan_stats = scanner.get_scan_stats()
        top_symbols = scanner.get_top_symbols(5)
        opportunities = scanner.get_market_opportunities(5)
        
        logger.info(f"   ✅ Scanner Results: {scan_stats['symbols_scanned']} symbols scanned, "
                   f"{len(opportunities)} opportunities found")
        logger.info(f"   🏆 Top Symbols: {top_symbols[:3]}")
        
        await scanner.stop_scanning()
        
        # Test Symbol Selector
        logger.info("🎯 Testing Symbol Selector...")
        from autonomy.symbol_selector import DynamicSymbolSelector
        
        # Restart scanner for selector test
        await scanner.start_scanning()
        await asyncio.sleep(10)  # Let scanner gather data
        
        selector = DynamicSymbolSelector(config, scanner)
        await selector.start_selection()
        
        # Let it select for 30 seconds
        await asyncio.sleep(30)
        
        # Check results
        current_symbol = selector.get_current_symbol()
        selection_stats = selector.get_selection_stats()
        symbol_scores = selector.get_symbol_scores()
        
        logger.info(f"   ✅ Selector Results: Current symbol: {current_symbol}")
        logger.info(f"   📊 Selection Stats: {selection_stats['total_selections']} selections")
        logger.info(f"   🎯 Scored Symbols: {len(symbol_scores)}")
        
        await selector.stop_selection()
        await scanner.stop_scanning()
        
        logger.info("✅ Individual component tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Component test failed: {e}")
        raise

async def main():
    """Main test function."""
    
    logger.info("🎯 Multi-Symbol Autonomous Trading System - Comprehensive Test")
    logger.info("=" * 70)
    
    try:
        # Test individual components first
        await test_individual_components()
        
        logger.info("\n" + "=" * 70)
        
        # Test complete system
        await test_multi_symbol_system()
        
        logger.info("\n" + "=" * 70)
        logger.info("🎉 ALL TESTS PASSED! Multi-Symbol System is ready for deployment!")
        
    except Exception as e:
        logger.error(f"❌ TESTS FAILED: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 Multi-Symbol Autonomous Trading System is ready!")
        print("🚀 You can now deploy this system for live trading!")
    else:
        print("\n❌ Tests failed. Please check the logs and fix issues.")
