#!/usr/bin/env python3
"""
Setup Real Exchange API Credentials
Adds real HTX API credentials from .env file to the database for the epinnox user
"""

import os
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_real_credentials():
    """Setup real exchange credentials for the epinnox user."""
    try:
        logger.info("🔧 Setting up real exchange API credentials...")
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Get HTX credentials from environment
        htx_api_key = os.getenv('HTX_API_KEY')
        htx_api_secret = os.getenv('HTX_API_SECRET')
        
        if not htx_api_key or not htx_api_secret:
            logger.error("❌ HTX_API_KEY and HTX_API_SECRET must be set in .env file")
            return False
        
        logger.info(f"✅ Found HTX credentials: {htx_api_key[:8]}...")
        
        # Import required modules
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        from exchanges.account_manager import ExchangeAccountManager
        
        # Initialize managers
        db_manager = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db_manager)
        exchange_manager = ExchangeAccountManager(db_manager)
        
        # Get epinnox user
        epinnox_user = user_manager.get_user_by_username('epinnox')
        if not epinnox_user:
            logger.error("❌ Epinnox user not found")
            return False
        
        user_id = epinnox_user.id
        logger.info(f"✅ Found epinnox user: ID {user_id}")
        
        # Check if HTX account already exists
        existing_exchanges = exchange_manager.get_user_exchanges(user_id)
        htx_exists = any(ex.exchange_name == 'HTX' for ex in existing_exchanges)
        
        if htx_exists:
            logger.info("⚠️ HTX account already exists, removing old one...")
            # Remove existing HTX account
            db_manager.conn.execute("""
                DELETE FROM user_exchanges 
                WHERE user_id = ? AND exchange_name = 'HTX'
            """, (user_id,))
            db_manager.conn.commit()
        
        # Add HTX account with real credentials
        logger.info("🔐 Adding HTX account with real credentials...")
        success = exchange_manager.add_exchange_account(
            user_id=user_id,
            exchange_name='HTX',
            api_key=htx_api_key,
            secret_key=htx_api_secret,
            passphrase=None
        )
        
        if success:
            logger.info("✅ HTX account added successfully")
            
            # Test the credentials
            logger.info("🧪 Testing HTX credentials...")
            htx_client = exchange_manager.get_exchange_client(user_id, 'HTX')
            
            if htx_client:
                logger.info("✅ HTX client created successfully")
                
                # Test API call
                try:
                    balance = htx_client.fetch_balance()
                    logger.info("✅ HTX API call successful")
                    logger.info(f"Account balance keys: {list(balance.keys())}")
                except Exception as e:
                    logger.warning(f"⚠️ HTX API call failed (may be normal): {e}")
            else:
                logger.error("❌ Failed to create HTX client")
                return False
        else:
            logger.error("❌ Failed to add HTX account")
            return False
        
        # Also add Binance credentials if available
        binance_api_key = os.getenv('BINANCE_API_KEY')
        binance_api_secret = os.getenv('BINANCE_API_SECRET')
        
        if binance_api_key and binance_api_secret:
            logger.info("🔐 Adding Binance account...")
            
            # Remove existing Binance account if exists
            db_manager.conn.execute("""
                DELETE FROM user_exchanges 
                WHERE user_id = ? AND exchange_name = 'Binance'
            """, (user_id,))
            db_manager.conn.commit()
            
            binance_success = exchange_manager.add_exchange_account(
                user_id=user_id,
                exchange_name='Binance',
                api_key=binance_api_key,
                secret_key=binance_api_secret,
                passphrase=None
            )
            
            if binance_success:
                logger.info("✅ Binance account added successfully")
            else:
                logger.warning("⚠️ Failed to add Binance account")
        
        # Also add Bybit credentials if available
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if bybit_api_key and bybit_api_secret:
            logger.info("🔐 Adding Bybit account...")
            
            # Remove existing Bybit account if exists
            db_manager.conn.execute("""
                DELETE FROM user_exchanges 
                WHERE user_id = ? AND exchange_name = 'Bybit'
            """, (user_id,))
            db_manager.conn.commit()
            
            bybit_success = exchange_manager.add_exchange_account(
                user_id=user_id,
                exchange_name='Bybit',
                api_key=bybit_api_key,
                secret_key=bybit_api_secret,
                passphrase=None
            )
            
            if bybit_success:
                logger.info("✅ Bybit account added successfully")
            else:
                logger.warning("⚠️ Failed to add Bybit account")
        
        # Summary
        final_exchanges = exchange_manager.get_user_exchanges(user_id)
        logger.info("\n" + "=" * 50)
        logger.info("EXCHANGE ACCOUNTS SUMMARY")
        logger.info("=" * 50)
        
        for exchange in final_exchanges:
            logger.info(f"✅ {exchange.exchange_name}: Active")
        
        logger.info(f"\nTotal exchanges configured: {len(final_exchanges)}")
        logger.info("🎉 Real credentials setup completed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_credentials():
    """Test all configured exchange credentials."""
    try:
        logger.info("\n🧪 Testing all exchange credentials...")
        
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        from exchanges.account_manager import ExchangeAccountManager
        
        # Initialize managers
        db_manager = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db_manager)
        exchange_manager = ExchangeAccountManager(db_manager)
        
        # Get epinnox user
        epinnox_user = user_manager.get_user_by_username('epinnox')
        if not epinnox_user:
            logger.error("❌ Epinnox user not found")
            return False
        
        user_id = epinnox_user.id
        exchanges = exchange_manager.get_user_exchanges(user_id)
        
        logger.info(f"Testing {len(exchanges)} exchange accounts...")
        
        for exchange in exchanges:
            logger.info(f"\n🔍 Testing {exchange.exchange_name}...")
            
            client = exchange_manager.get_exchange_client(user_id, exchange.exchange_name)
            if client:
                logger.info(f"✅ {exchange.exchange_name} client created")
                
                try:
                    # Test API call
                    balance = client.fetch_balance()
                    logger.info(f"✅ {exchange.exchange_name} API call successful")
                except Exception as e:
                    logger.warning(f"⚠️ {exchange.exchange_name} API call failed: {e}")
            else:
                logger.error(f"❌ {exchange.exchange_name} client creation failed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Credential testing failed: {e}")
        return False

def main():
    """Main function."""
    logger.info("=" * 60)
    logger.info("MONEY CIRCLE REAL CREDENTIALS SETUP")
    logger.info("=" * 60)
    
    # Setup credentials
    if not setup_real_credentials():
        return 1
    
    # Test credentials
    if not test_credentials():
        return 1
    
    logger.info("\n🎉 All credentials setup and tested successfully!")
    logger.info("The Money Circle platform now has real exchange API access.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
