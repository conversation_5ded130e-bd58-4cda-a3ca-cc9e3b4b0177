#!/usr/bin/env python3
"""
Quick script to check current users in Money Circle database and create epinnox admin if missing
"""

import sqlite3
import sys
import os
import bcrypt

def check_users():
    """Check current users in the database."""
    db_path = "data/money_circle.db"

    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.execute("""
            SELECT id, username, email, role, is_active, date_joined
            FROM users
            ORDER BY id
        """)
        users = cursor.fetchall()

        print("📊 Current users in Money Circle database:")
        print("=" * 70)
        print(f"{'ID':<3} | {'Username':<15} | {'Email':<20} | {'Role':<8} | {'Active':<6}")
        print("-" * 70)

        if not users:
            print("❌ No users found in database!")
            print("\n💡 Run the demo seeder to create test users:")
            print("   python database/simple_demo_seeder.py")
        else:
            epinnox_exists = False
            for user in users:
                active_status = "✅ Yes" if user[4] else "❌ No"
                print(f"{user[0]:<3} | {user[1]:<15} | {user[2]:<20} | {user[3]:<8} | {active_status}")
                if user[1] == 'epinnox':
                    epinnox_exists = True

            # Create epinnox admin if it doesn't exist
            if not epinnox_exists:
                print("\n🔧 Creating epinnox admin user...")
                password_hash = bcrypt.hashpw('securepass123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                conn.execute("""
                    INSERT INTO users (username, email, hashed_password, role, email_verified, agreement_accepted, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, ('epinnox', '<EMAIL>', password_hash, 'admin', True, True, True))
                conn.commit()
                print("✅ Epinnox admin user created!")

            print("\n🔑 LOGIN CREDENTIALS:")
            print("=" * 40)
            print("ADMIN ACCOUNT:")
            print("  Username: epinnox")
            print("  Password: securepass123")
            print("\nDEMO ACCOUNTS:")
            print("  Username: alex_trader")
            print("  Username: sarah_crypto")
            print("  Username: mike_scalper")
            print("  Password: demo123 (for all demo accounts)")
            print("\n🌐 Login at: http://localhost:8085")

        conn.close()

    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_users()
