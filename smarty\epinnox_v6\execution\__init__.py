#!/usr/bin/env python3
"""
Execution Intelligence & Strategy Response Layer
Phase 6: From Thinking to Doing

This module implements smart execution logic that transforms AI decisions
into risk-aware trading actions with adaptive order management.
"""

from .trade_executor import RiskAwareTradeExecutor
from .order_logic import Adaptive<PERSON><PERSON>r<PERSON>og<PERSON>
from .execution_memory import ExecutionMemory
from .post_trade_reflection import PostTradeReflection
from .execution_controller import ExecutionController

__all__ = [
    'RiskAwareTradeExecutor',
    'AdaptiveOrderLogic',
    'ExecutionMemory',
    'PostTradeReflection',
    'ExecutionController'
]

__version__ = "6.0.0"
__author__ = "Epinnox AI Trading System"
