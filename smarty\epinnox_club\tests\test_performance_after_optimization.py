#!/usr/bin/env python3
"""
Money Circle Performance Testing - After Optimization
Measures performance improvements after implementing optimizations.
"""

import requests
import time
import json
import gzip
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8085"

def measure_critical_css_performance():
    """Measure performance impact of critical CSS inlining"""
    print("\n🚀 Critical CSS Performance Test")
    print("=" * 50)
    
    try:
        # Test login page (has critical CSS inlined)
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/login", timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            content = response.text
            
            # Measure critical CSS size
            critical_css_start = content.find('<style>')
            critical_css_end = content.find('</style>') + 8
            
            if critical_css_start != -1 and critical_css_end != -1:
                critical_css = content[critical_css_start:critical_css_end]
                critical_css_size = len(critical_css.encode('utf-8'))
                
                print(f"  ✅ Critical CSS inlined: {critical_css_size:,} bytes")
                print(f"  ⏱️  Page load time: {(end_time - start_time) * 1000:.1f}ms")
                
                # Check for async CSS loading
                async_css_count = content.count('rel="preload"')
                print(f"  ⚡ Async CSS files: {async_css_count}")
                
                # Check for resource hints
                preload_count = content.count('as="style"')
                print(f"  🔗 Resource hints: {preload_count}")
                
                return {
                    "critical_css_size": critical_css_size,
                    "load_time": (end_time - start_time) * 1000,
                    "async_css_count": async_css_count,
                    "preload_count": preload_count
                }
            else:
                print(f"  ❌ Critical CSS not found")
                
    except Exception as e:
        print(f"  ❌ Error testing critical CSS: {str(e)}")
    
    return None

def test_minified_css_performance():
    """Test performance of minified CSS files"""
    print("\n🗜️  Minified CSS Performance Test")
    print("=" * 50)
    
    css_files = [
        {"url": "/static/css/design_system.min.css", "name": "Design System (Minified)"},
        {"url": "/static/css/design_system.css", "name": "Design System (Original)"}
    ]
    
    results = {}
    
    for css_file in css_files:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{css_file['url']}", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                content = response.text
                size = len(content.encode('utf-8'))
                
                # Test compression potential
                compressed = gzip.compress(content.encode('utf-8'))
                compression_ratio = (1 - len(compressed) / size) * 100
                
                results[css_file['name']] = {
                    "size": size,
                    "load_time": (end_time - start_time) * 1000,
                    "compression_ratio": compression_ratio
                }
                
                print(f"  📄 {css_file['name']}:")
                print(f"    📦 Size: {size:,} bytes")
                print(f"    ⏱️  Load Time: {(end_time - start_time) * 1000:.1f}ms")
                print(f"    🗜️  Compression Potential: {compression_ratio:.1f}%")
                
        except Exception as e:
            print(f"  ❌ Error testing {css_file['name']}: {str(e)}")
    
    # Calculate improvement
    if "Design System (Original)" in results and "Design System (Minified)" in results:
        original = results["Design System (Original)"]
        minified = results["Design System (Minified)"]
        
        size_reduction = ((original["size"] - minified["size"]) / original["size"]) * 100
        time_improvement = ((original["load_time"] - minified["load_time"]) / original["load_time"]) * 100
        
        print(f"\n📊 Minification Benefits:")
        print(f"  📦 Size Reduction: {size_reduction:.1f}%")
        print(f"  ⏱️  Load Time Improvement: {time_improvement:.1f}%")
    
    return results

def test_async_loading_performance():
    """Test performance impact of async CSS/JS loading"""
    print("\n⚡ Async Loading Performance Test")
    print("=" * 50)
    
    try:
        # Test dashboard page with async loading
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/dashboard", timeout=30)
        end_time = time.time()
        
        if response.status_code in [200, 302]:  # 302 for redirect to login
            content = response.text if response.status_code == 200 else ""
            
            # Count async resources
            async_scripts = content.count('async src=')
            preload_styles = content.count('rel="preload"')
            defer_scripts = content.count('defer src=')
            
            print(f"  ⚡ Async Scripts: {async_scripts}")
            print(f"  🔗 Preload Styles: {preload_styles}")
            print(f"  ⏳ Defer Scripts: {defer_scripts}")
            print(f"  ⏱️  Initial Response: {(end_time - start_time) * 1000:.1f}ms")
            
            return {
                "async_scripts": async_scripts,
                "preload_styles": preload_styles,
                "defer_scripts": defer_scripts,
                "response_time": (end_time - start_time) * 1000
            }
            
    except Exception as e:
        print(f"  ❌ Error testing async loading: {str(e)}")
    
    return None

def benchmark_all_pages():
    """Benchmark all dashboard pages after optimization"""
    print("\n📊 Complete Page Performance Benchmark")
    print("=" * 50)
    
    pages = [
        {"url": "/login", "name": "Login Page"},
        {"url": "/dashboard", "name": "Personal Dashboard"},
        {"url": "/club", "name": "Club Dashboard"},
        {"url": "/analytics", "name": "Analytics Dashboard"},
        {"url": "/members", "name": "Member Directory"},
        {"url": "/strategies", "name": "Strategy Marketplace"}
    ]
    
    results = []
    total_time = 0
    
    for page in pages:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{page['url']}", timeout=30)
            end_time = time.time()
            
            load_time = (end_time - start_time) * 1000
            total_time += load_time
            
            # Calculate performance score
            score = 100
            if load_time > 1000:  # > 1s
                score -= 30
            elif load_time > 500:  # > 500ms
                score -= 15
            
            content_size = len(response.content) if response.status_code == 200 else 0
            if content_size > 100000:  # > 100KB
                score -= 20
            elif content_size > 50000:  # > 50KB
                score -= 10
            
            # Check for optimization features
            if response.status_code == 200:
                content = response.text
                has_critical_css = '<style>' in content
                has_async_loading = 'rel="preload"' in content
                
                if has_critical_css:
                    score += 5
                if has_async_loading:
                    score += 5
            
            score = max(0, min(100, score))
            
            results.append({
                "name": page["name"],
                "load_time": load_time,
                "content_size": content_size,
                "score": score,
                "status": response.status_code
            })
            
            print(f"  📄 {page['name']}:")
            print(f"    ⏱️  Load Time: {load_time:.1f}ms")
            print(f"    📦 Size: {content_size:,} bytes")
            print(f"    🎯 Score: {score}/100")
            
        except Exception as e:
            print(f"  ❌ Error testing {page['name']}: {str(e)}")
    
    # Calculate overall performance
    avg_load_time = total_time / len(results) if results else 0
    avg_score = sum(r["score"] for r in results) / len(results) if results else 0
    
    print(f"\n📈 Overall Performance:")
    print(f"  ⏱️  Average Load Time: {avg_load_time:.1f}ms")
    print(f"  🎯 Average Score: {avg_score:.1f}/100")
    
    return results

def generate_performance_report():
    """Generate comprehensive performance report"""
    print("\n📋 Performance Optimization Report")
    print("=" * 50)
    
    # Run all tests
    critical_css_results = measure_critical_css_performance()
    minified_css_results = test_minified_css_performance()
    async_loading_results = test_async_loading_performance()
    page_results = benchmark_all_pages()
    
    # Generate recommendations
    recommendations = []
    
    if critical_css_results and critical_css_results["critical_css_size"] > 0:
        recommendations.append("✅ Critical CSS inlining implemented")
    else:
        recommendations.append("⚠️  Consider implementing critical CSS inlining")
    
    if async_loading_results and async_loading_results["preload_styles"] > 0:
        recommendations.append("✅ Async CSS loading implemented")
    else:
        recommendations.append("⚠️  Consider implementing async CSS loading")
    
    if minified_css_results:
        recommendations.append("✅ CSS minification available")
    
    # Calculate overall grade
    if page_results:
        avg_score = sum(r["score"] for r in page_results) / len(page_results)
        
        if avg_score >= 90:
            grade = "A+ (Excellent)"
        elif avg_score >= 80:
            grade = "A (Good)"
        elif avg_score >= 70:
            grade = "B (Fair)"
        elif avg_score >= 60:
            grade = "C (Needs Improvement)"
        else:
            grade = "D (Poor)"
        
        print(f"\n🏆 Overall Performance Grade: {grade} ({avg_score:.1f}/100)")
    
    print(f"\n💡 Optimization Status:")
    for rec in recommendations:
        print(f"  {rec}")
    
    return {
        "critical_css": critical_css_results,
        "minified_css": minified_css_results,
        "async_loading": async_loading_results,
        "page_results": page_results,
        "recommendations": recommendations
    }

def main():
    """Run comprehensive performance testing after optimization"""
    print("🚀 Money Circle Performance Testing - After Optimization")
    print("=" * 70)
    print(f"Testing at: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Generate comprehensive report
    report = generate_performance_report()
    
    print("\n" + "=" * 70)
    print("🎯 Performance Optimization Summary")
    print("=" * 70)
    
    print(f"\n🚀 Optimizations Implemented:")
    print(f"  ✅ Critical CSS inlining for above-the-fold content")
    print(f"  ✅ Async CSS loading with preload hints")
    print(f"  ✅ CSS minification (design_system.min.css)")
    print(f"  ✅ Resource hints for better loading performance")
    print(f"  ✅ Async JavaScript loading")
    print(f"  ✅ Progressive enhancement fallbacks")
    
    print(f"\n📊 Performance Improvements:")
    if report["critical_css"]:
        print(f"  • Critical CSS: {report['critical_css']['critical_css_size']:,} bytes inlined")
        print(f"  • Async resources: {report['critical_css']['async_css_count']} CSS files")
    
    print(f"\n🎯 Next Steps:")
    print(f"  1. Enable server-side gzip compression")
    print(f"  2. Implement service worker for caching")
    print(f"  3. Add image optimization and lazy loading")
    print(f"  4. Consider CDN for static assets")
    print(f"  5. Implement performance monitoring")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import sys
        sys.exit(1)
