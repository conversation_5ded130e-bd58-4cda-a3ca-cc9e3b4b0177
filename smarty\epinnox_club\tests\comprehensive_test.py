#!/usr/bin/env python3
"""
Comprehensive Money Circle Platform Test
Tests all major functionality including authentication, dashboards, and APIs.
"""

import requests
import json
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8085"

class MoneyCircleTest:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = {}
    
    def test_server_health(self):
        """Test if server is responding."""
        try:
            response = self.session.get(f"{BASE_URL}/login")
            if response.status_code == 200:
                logger.info("✅ Server is responding")
                self.test_results['server_health'] = True
                return True
            else:
                logger.error(f"❌ Server health check failed: {response.status_code}")
                self.test_results['server_health'] = False
                return False
        except Exception as e:
            logger.error(f"❌ Server connection failed: {e}")
            self.test_results['server_health'] = False
            return False
    
    def test_admin_login(self):
        """Test admin login functionality."""
        try:
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            response = self.session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
            
            if response.status_code == 302 and 'dashboard' in response.headers.get('Location', ''):
                logger.info("✅ Admin login successful")
                self.test_results['admin_login'] = True
                return True
            else:
                logger.error(f"❌ Admin login failed: {response.status_code}")
                self.test_results['admin_login'] = False
                return False
        except Exception as e:
            logger.error(f"❌ Admin login error: {e}")
            self.test_results['admin_login'] = False
            return False
    
    def test_demo_user_login(self):
        """Test demo user login functionality."""
        demo_users = ['trader_alex', 'crypto_sarah', 'quant_mike', 'forex_emma']
        successful_logins = 0
        
        for username in demo_users:
            try:
                # Create new session for each user
                test_session = requests.Session()
                login_data = {
                    'username': username,
                    'password': 'securepass123'
                }
                response = test_session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
                
                if response.status_code == 302:
                    logger.info(f"✅ Demo user {username} login successful")
                    successful_logins += 1
                else:
                    logger.error(f"❌ Demo user {username} login failed: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ Demo user {username} login error: {e}")
        
        success_rate = successful_logins / len(demo_users)
        self.test_results['demo_user_login'] = success_rate
        
        if success_rate >= 0.75:  # At least 75% success rate
            logger.info(f"✅ Demo user login test passed ({successful_logins}/{len(demo_users)})")
            return True
        else:
            logger.error(f"❌ Demo user login test failed ({successful_logins}/{len(demo_users)})")
            return False
    
    def test_dashboard_access(self):
        """Test dashboard access after login."""
        try:
            response = self.session.get(f"{BASE_URL}/dashboard")
            if response.status_code == 200 and 'Personal Dashboard' in response.text:
                logger.info("✅ Dashboard access successful")
                self.test_results['dashboard_access'] = True
                return True
            else:
                logger.error(f"❌ Dashboard access failed: {response.status_code}")
                self.test_results['dashboard_access'] = False
                return False
        except Exception as e:
            logger.error(f"❌ Dashboard access error: {e}")
            self.test_results['dashboard_access'] = False
            return False
    
    def test_club_pages(self):
        """Test club-related pages."""
        club_pages = [
            ('/club', 'Club Dashboard'),
            ('/club/members', 'Member Directory'),
            ('/club/analytics', 'Club Analytics'),
            ('/club/strategies', 'Strategy Marketplace')
        ]
        
        successful_pages = 0
        
        for url, expected_content in club_pages:
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    logger.info(f"✅ Club page {url} accessible")
                    successful_pages += 1
                else:
                    logger.error(f"❌ Club page {url} failed: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ Club page {url} error: {e}")
        
        success_rate = successful_pages / len(club_pages)
        self.test_results['club_pages'] = success_rate
        
        if success_rate >= 0.5:  # At least 50% success rate
            logger.info(f"✅ Club pages test passed ({successful_pages}/{len(club_pages)})")
            return True
        else:
            logger.error(f"❌ Club pages test failed ({successful_pages}/{len(club_pages)})")
            return False
    
    def test_api_endpoints(self):
        """Test API endpoints."""
        api_endpoints = [
            '/api/portfolio',
            '/api/market/status',
            '/api/market/tickers',
            '/api/club/analytics/overview'
        ]
        
        successful_apis = 0
        
        for endpoint in api_endpoints:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                if response.status_code in [200, 401]:  # 401 is expected for some endpoints
                    logger.info(f"✅ API endpoint {endpoint} responding")
                    successful_apis += 1
                else:
                    logger.error(f"❌ API endpoint {endpoint} failed: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ API endpoint {endpoint} error: {e}")
        
        success_rate = successful_apis / len(api_endpoints)
        self.test_results['api_endpoints'] = success_rate
        
        if success_rate >= 0.75:  # At least 75% success rate
            logger.info(f"✅ API endpoints test passed ({successful_apis}/{len(api_endpoints)})")
            return True
        else:
            logger.error(f"❌ API endpoints test failed ({successful_apis}/{len(api_endpoints)})")
            return False
    
    def test_static_files(self):
        """Test static file serving."""
        static_files = [
            '/static/css/dashboard.css',
            '/static/css/club.css',
            '/static/js/personal_dashboard.js',
            '/static/js/club_dashboard.js'
        ]
        
        successful_files = 0
        
        for file_path in static_files:
            try:
                response = self.session.get(f"{BASE_URL}{file_path}")
                if response.status_code == 200:
                    logger.info(f"✅ Static file {file_path} served correctly")
                    successful_files += 1
                else:
                    logger.error(f"❌ Static file {file_path} failed: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ Static file {file_path} error: {e}")
        
        success_rate = successful_files / len(static_files)
        self.test_results['static_files'] = success_rate
        
        if success_rate >= 0.75:  # At least 75% success rate
            logger.info(f"✅ Static files test passed ({successful_files}/{len(static_files)})")
            return True
        else:
            logger.error(f"❌ Static files test failed ({successful_files}/{len(static_files)})")
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report."""
        logger.info("🚀 Starting comprehensive Money Circle platform test...")
        
        tests = [
            ('Server Health', self.test_server_health),
            ('Admin Login', self.test_admin_login),
            ('Demo User Login', self.test_demo_user_login),
            ('Dashboard Access', self.test_dashboard_access),
            ('Club Pages', self.test_club_pages),
            ('API Endpoints', self.test_api_endpoints),
            ('Static Files', self.test_static_files)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name} test...")
            if test_func():
                passed_tests += 1
        
        # Generate final report
        logger.info(f"\n📊 TEST RESULTS SUMMARY:")
        logger.info(f"{'='*50}")
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info(f"{'='*50}")
        
        # Detailed results
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result is True or (isinstance(result, float) and result >= 0.5) else "❌ FAIL"
            if isinstance(result, float):
                logger.info(f"{test_name}: {status} ({result*100:.1f}%)")
            else:
                logger.info(f"{test_name}: {status}")
        
        return passed_tests >= total_tests * 0.7  # 70% pass rate required

if __name__ == "__main__":
    tester = MoneyCircleTest()
    success = tester.run_all_tests()
    
    if success:
        logger.info("\n🎉 Money Circle platform is functioning well!")
    else:
        logger.error("\n⚠️ Money Circle platform has significant issues that need attention.")
