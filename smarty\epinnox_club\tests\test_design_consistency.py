#!/usr/bin/env python3
"""
Test design consistency and improvements across Money Circle enhanced club features.
Tests unified header, footer, chart fixes, and professional fintech design.
"""

import requests
import sys

def test_design_system_integration():
    """Test that design system CSS is properly integrated."""
    print("🎨 TESTING DESIGN SYSTEM INTEGRATION")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]
    
    design_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for design system components
                design_elements = [
                    'design_system.css',
                    'unified_header.css',
                    'unified_footer.css',
                    '--primary-600',
                    'var(--',
                    'fintech'
                ]
                
                found_elements = sum(1 for element in design_elements if element in content)
                
                if found_elements >= 4:
                    print(f"✅ {name}: Design system integrated ({found_elements}/{len(design_elements)})")
                    design_success += 1
                else:
                    print(f"⚠️ {name}: Partial design system ({found_elements}/{len(design_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Design System Integration: {design_success}/{len(pages)} pages")
    return design_success >= len(pages) * 0.8

def test_unified_header_consistency():
    """Test unified header across all pages."""
    print("\n🧭 TESTING UNIFIED HEADER CONSISTENCY")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]
    
    header_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for unified header elements
                header_elements = [
                    'unified-header',
                    'header-branding',
                    'header-nav',
                    'header-user-info',
                    'breadcrumb-content',
                    'user-avatar',
                    'Investment Club Member'
                ]
                
                found_elements = sum(1 for element in header_elements if element in content)
                
                if found_elements >= 6:
                    print(f"✅ {name}: Unified header implemented ({found_elements}/{len(header_elements)})")
                    header_success += 1
                else:
                    print(f"⚠️ {name}: Partial header consistency ({found_elements}/{len(header_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Header Consistency: {header_success}/{len(pages)} pages")
    return header_success >= len(pages) * 0.8

def test_professional_footer():
    """Test professional footer implementation."""
    print("\n🦶 TESTING PROFESSIONAL FOOTER")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/analytics', 'Club Analytics')  # Footer implemented on analytics page
    ]
    
    footer_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for footer elements
                footer_elements = [
                    'unified-footer',
                    'footer-main',
                    'footer-company',
                    'Epinnox Investment Club',
                    'Money Circle',
                    'Bank-Grade Security',
                    '<EMAIL>',
                    'footer-copyright',
                    'Privacy Policy',
                    'Terms of Service'
                ]
                
                found_elements = sum(1 for element in footer_elements if element in content)
                
                if found_elements >= 8:
                    print(f"✅ {name}: Professional footer implemented ({found_elements}/{len(footer_elements)})")
                    footer_success += 1
                else:
                    print(f"⚠️ {name}: Partial footer implementation ({found_elements}/{len(footer_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Professional Footer: {footer_success}/{len(pages)} pages")
    return footer_success >= len(pages) * 0.8

def test_chart_container_fixes():
    """Test chart container fixes and responsive behavior."""
    print("\n📊 TESTING CHART CONTAINER FIXES")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    # Test analytics page specifically for chart fixes
    try:
        response = session.get('http://localhost:8084/club/analytics')
        if response.status_code == 200:
            content = response.text
            
            # Check for chart container improvements
            chart_elements = [
                'chart-container',
                'chart-wrapper',
                'max-width: 100%',
                'overflow: hidden',
                'height: 400px',
                'canvas',
                'Chart.js',
                'performanceChart',
                'allocationChart'
            ]
            
            found_elements = sum(1 for element in chart_elements if element in content)
            
            if found_elements >= 7:
                print(f"✅ Chart containers: Fixed and responsive ({found_elements}/{len(chart_elements)})")
                return True
            else:
                print(f"⚠️ Chart containers: Partial fixes ({found_elements}/{len(chart_elements)})")
                return False
        else:
            print(f"❌ Analytics page: Not accessible")
            return False
    except Exception as e:
        print(f"❌ Chart containers: Error - {e}")
        return False

def test_fintech_design_elements():
    """Test fintech-specific design elements."""
    print("\n💼 TESTING FINTECH DESIGN ELEMENTS")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]
    
    fintech_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for fintech design elements
                fintech_elements = [
                    'Investment Club',
                    'trading',
                    'performance',
                    'portfolio',
                    'risk',
                    'strategy',
                    'analytics',
                    'professional',
                    'automated',
                    'algorithm'
                ]
                
                found_elements = sum(1 for element in fintech_elements if element.lower() in content.lower())
                
                if found_elements >= 7:
                    print(f"✅ {name}: Fintech design elements present ({found_elements}/{len(fintech_elements)})")
                    fintech_success += 1
                else:
                    print(f"⚠️ {name}: Limited fintech elements ({found_elements}/{len(fintech_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Fintech Design: {fintech_success}/{len(pages)} pages")
    return fintech_success >= len(pages) * 0.8

def test_responsive_design_improvements():
    """Test responsive design improvements."""
    print("\n📱 TESTING RESPONSIVE DESIGN IMPROVEMENTS")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]
    
    responsive_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for responsive design improvements
                responsive_elements = [
                    '@media (max-width: 768px)',
                    '@media (max-width: 1024px)',
                    'flex-wrap',
                    'grid-template-columns',
                    'responsive',
                    'mobile',
                    'viewport'
                ]
                
                found_elements = sum(1 for element in responsive_elements if element in content)
                
                if found_elements >= 5:
                    print(f"✅ {name}: Responsive design improved ({found_elements}/{len(responsive_elements)})")
                    responsive_success += 1
                else:
                    print(f"⚠️ {name}: Basic responsive features ({found_elements}/{len(responsive_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Responsive Design: {responsive_success}/{len(pages)} pages")
    return responsive_success >= len(pages) * 0.7

def test_color_scheme_consistency():
    """Test color scheme consistency across pages."""
    print("\n🎨 TESTING COLOR SCHEME CONSISTENCY")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]
    
    color_success = 0
    
    for url, name in pages:
        try:
            response = session.get(f'http://localhost:8084{url}')
            if response.status_code == 200:
                content = response.text
                
                # Check for consistent color scheme
                color_elements = [
                    '#8b5cf6',  # Primary purple
                    '#22c55e',  # Success green
                    '#ef4444',  # Error red
                    '#f59e0b',  # Warning amber
                    'linear-gradient',
                    'rgba(139, 92, 246',
                    'rgba(255, 255, 255, 0.05)'
                ]
                
                found_elements = sum(1 for element in color_elements if element in content)
                
                if found_elements >= 5:
                    print(f"✅ {name}: Consistent color scheme ({found_elements}/{len(color_elements)})")
                    color_success += 1
                else:
                    print(f"⚠️ {name}: Partial color consistency ({found_elements}/{len(color_elements)})")
            else:
                print(f"❌ {name}: Not accessible")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print(f"\n📊 Color Consistency: {color_success}/{len(pages)} pages")
    return color_success >= len(pages) * 0.8

def main():
    """Run comprehensive design consistency tests."""
    print("🎯 MONEY CIRCLE DESIGN CONSISTENCY & IMPROVEMENTS TEST")
    print("=" * 70)
    
    tests = [
        ("Design System Integration", test_design_system_integration),
        ("Unified Header Consistency", test_unified_header_consistency),
        ("Professional Footer", test_professional_footer),
        ("Chart Container Fixes", test_chart_container_fixes),
        ("Fintech Design Elements", test_fintech_design_elements),
        ("Responsive Design Improvements", test_responsive_design_improvements),
        ("Color Scheme Consistency", test_color_scheme_consistency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"⚠️ {test_name}: NEEDS IMPROVEMENT")
                passed += 0.5
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 FINAL DESIGN TEST RESULTS: {passed}/{total} tests passed")
    
    if passed >= total * 0.9:
        print("🎉 EXCELLENT DESIGN CONSISTENCY ACHIEVED!")
        print("✅ Unified design system implemented across all pages")
        print("✅ Professional fintech-grade visual design")
        print("✅ Consistent headers, footers, and navigation")
        print("✅ Fixed chart containers with proper responsive behavior")
        print("✅ Professional color scheme and typography")
        print("✅ Mobile-responsive design optimized for all devices")
        print("\n🌟 MONEY CIRCLE NOW HAS INSTITUTIONAL-GRADE DESIGN!")
        return 0
    elif passed >= total * 0.7:
        print("✅ GOOD DESIGN CONSISTENCY ACHIEVED!")
        print("Most design improvements successfully implemented")
        print("Minor refinements may be needed for perfect consistency")
        return 0
    else:
        print("⚠️ DESIGN IMPROVEMENTS PARTIALLY IMPLEMENTED")
        print("Additional work needed for full design consistency")
        return 1

if __name__ == "__main__":
    sys.exit(main())
