# Multi-Symbol Autonomous Trading System Configuration
# Conservative settings for $100 account balance

# Multi-Symbol System Control
multi_symbol_enabled: true
multi_symbol_mode: "conservative"  # conservative, balanced, aggressive

# Market Scanner Configuration
market_scanner:
  scan_interval: 60                # Scan every 60 seconds (conservative)
  min_volume_24h: 5000000         # Minimum $5M daily volume for safety
  min_price_change: 0.015         # Minimum 1.5% price change
  max_symbols: 10                 # Track only top 10 symbols
  excluded_symbols:               # Exclude high-risk symbols
    - "SHIB/USDT:USDT"
    - "PEPE/USDT:USDT"
    - "FLOKI/USDT:USDT"

# Symbol Selector Configuration  
symbol_selector:
  selection_interval: 300         # Evaluate every 5 minutes (conservative)
  min_confidence: 0.75            # High confidence requirement (75%)
  max_switches_per_hour: 2        # Very limited switching (2 per hour)
  min_hold_duration: 900          # Hold symbol for at least 15 minutes
  preferred_symbols:              # Focus on major cryptocurrencies
    - "BTC/USDT:USDT"
    - "ETH/USDT:USDT"
    - "DOGE/USDT:USDT"
    - "SOL/USDT:USDT"
    - "ADA/USDT:USDT"

# Multi-Symbol Trader Configuration
multi_symbol_trader:
  enabled: true
  max_symbols: 2                  # Trade only 2 symbols simultaneously
  max_positions_per_symbol: 1     # Only 1 position per symbol
  total_capital: 100.0            # Current account balance
  max_risk_per_symbol: 0.03       # Max 3% risk per symbol (very conservative)
  max_portfolio_risk: 0.08        # Max 8% total portfolio risk
  position_sizing_method: "fixed_risk"
  
  # Conservative Trading Controls
  trading_controls:
    max_margin_pct: 20            # Ultra conservative margin (20%)
    position_size_usd: 0.50       # Very small positions ($0.50)
    stop_loss_pct: 0.8            # Tight stop loss (0.8%)
    take_profit_pct: 0.4          # Conservative take profit (0.4%)
    max_daily_trades: 3           # Very limited trades per day
    emergency_stop_loss_pct: 2.0  # Emergency stop at 2% loss

# Enhanced Decision Engine Configuration
enhanced_decision_engine:
  decision_interval: 120          # Make decisions every 2 minutes
  min_confidence: 0.8             # Very high confidence requirement (80%)
  max_signals_per_cycle: 1        # Only 1 signal per cycle (conservative)
  regime_analysis: true
  
  # Conservative Signal Filters
  signal_filters:
    min_liquidity_score: 3.0      # High liquidity requirement
    max_volatility: 0.04          # Max 4% volatility
    min_volume_trend: 1.1         # Require volume confirmation
    blackout_periods:             # No trading during high-risk times
      - "22:00-02:00"             # Avoid low liquidity hours

# System Orchestrator Configuration
orchestrator:
  startup_delay: 15               # 15 second startup delay
  health_check_interval: 30       # Check health every 30 seconds
  report_interval: 180            # Report every 3 minutes
  
  # Safety Features
  safety_features:
    auto_stop_on_drawdown: true
    max_daily_drawdown: 5.0       # Stop if daily drawdown > 5%
    max_consecutive_losses: 3     # Stop after 3 consecutive losses
    emergency_contact: false      # Disable for now

# Risk Management Rules
risk_management:
  # Portfolio Level
  portfolio_rules:
    max_total_exposure: 0.08      # Max 8% total exposure
    max_correlation: 0.7          # Avoid highly correlated positions
    diversification_min: 2        # Minimum 2 different symbols
    
  # Position Level  
  position_rules:
    max_position_size: 0.03       # Max 3% per position
    max_leverage: 5               # Conservative leverage limit
    stop_loss_required: true      # Always use stop loss
    take_profit_required: true    # Always use take profit
    
  # Time-based Rules
  time_rules:
    max_hold_time: 3600           # Max 1 hour hold time
    min_hold_time: 300            # Min 5 minutes hold time
    trading_hours: "24/7"         # Trade 24/7 but with filters
    
# Performance Targets (Conservative)
performance_targets:
  daily_target: 0.5               # Target 0.5% daily return
  weekly_target: 2.0              # Target 2% weekly return
  monthly_target: 8.0             # Target 8% monthly return
  max_drawdown: 5.0               # Max 5% drawdown tolerance
  
# Monitoring and Alerts
monitoring:
  log_level: "INFO"
  performance_tracking: true
  real_time_alerts: true
  
  # Alert Thresholds
  alerts:
    position_loss_pct: 1.5        # Alert if position loses 1.5%
    daily_loss_pct: 3.0           # Alert if daily loss > 3%
    system_error: true            # Alert on system errors
    
# Symbol Universe (Conservative Selection)
symbol_universe:
  primary_symbols:                # Main trading symbols
    - "BTC/USDT:USDT"
    - "ETH/USDT:USDT"
    - "DOGE/USDT:USDT"
    
  secondary_symbols:              # Secondary options
    - "SOL/USDT:USDT"
    - "ADA/USDT:USDT"
    - "XRP/USDT:USDT"
    - "MATIC/USDT:USDT"
    
  excluded_symbols:               # Never trade these
    - "SHIB/USDT:USDT"
    - "PEPE/USDT:USDT"
    - "FLOKI/USDT:USDT"
    - "BONK/USDT:USDT"

# Integration Settings
integration:
  dashboard_enabled: true
  api_endpoints_enabled: true
  websocket_updates: true
  real_time_monitoring: true
  
# Testing and Validation
testing:
  paper_trading_mode: false       # Use real trading
  validation_period: 300          # 5 minute validation
  performance_benchmark: "DOGE/USDT:USDT"
  
# Backup and Recovery
backup:
  auto_backup_enabled: true
  backup_interval: 3600           # Backup every hour
  max_backup_files: 24            # Keep 24 hours of backups
