#!/usr/bin/env python3
"""
AI Strategy Tuner - Dedicated Interface for Smart Model Integrated Strategy

A lightweight, focused tool for real-time parameter tuning and AI decision monitoring
specifically designed for the Smart Model Integrated strategy (orchestrator.py).

Features:
- Real-time parameter modification
- Live AI model output visualization
- LLM reasoning display
- Performance tracking
- Model contribution analysis
"""

import asyncio
import json
import logging
import sqlite3
import subprocess
import sys
import time
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_cors

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AIStrategyTuner:
    """Dedicated interface for Smart Model Integrated strategy tuning and monitoring."""

    def __init__(self):
        self.config_path = "config.yaml"
        self.bus_path = "data/bus.db"
        self.orchestrator_process = None
        self.config = {}
        self.websocket_clients = set()
        self.last_update_time = time.time()

        # Multi-symbol support
        self.supported_symbols = ["BTC-USDT", "ETH-USDT", "DOGE-USDT", "SOL-USDT", "ADA-USDT"]
        self.current_symbol = "BTC-USDT"

        # Strategy state tracking
        self.strategy_running = False
        self.model_outputs = {}
        self.llm_decisions = []
        self.signal_timeline = []
        self.performance_metrics = {
            "total_signals": 0,
            "profitable_signals": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "model_accuracy": {},
            "per_symbol": {}
        }

        # Parameter presets
        self.parameter_presets = {
            "Conservative": {
                "model_weights": {"rsi": 1.2, "orderflow": 1.0, "volatility_regime": 1.5, "vwap_deviation": 1.0, "liquidity_imbalance": 0.8, "garch_volatility": 1.4, "funding_momentum": 1.0, "open_interest_momentum": 0.9, "social_sentiment": 0.6},
                "trading": {"base_buy_threshold": 0.5, "base_sell_threshold": -0.5}
            },
            "Aggressive": {
                "model_weights": {"rsi": 0.8, "orderflow": 2.0, "volatility_regime": 1.0, "vwap_deviation": 1.2, "liquidity_imbalance": 1.5, "garch_volatility": 1.0, "funding_momentum": 1.5, "open_interest_momentum": 1.3, "social_sentiment": 1.2},
                "trading": {"base_buy_threshold": 0.2, "base_sell_threshold": -0.2}
            },
            "Balanced": {
                "model_weights": {"rsi": 1.0, "orderflow": 1.5, "volatility_regime": 1.2, "vwap_deviation": 1.0, "liquidity_imbalance": 1.0, "garch_volatility": 1.3, "funding_momentum": 1.2, "open_interest_momentum": 1.1, "social_sentiment": 0.8},
                "trading": {"base_buy_threshold": 0.3, "base_sell_threshold": -0.3}
            }
        }

        # Load initial configuration
        self.load_config()

    def load_config(self):
        """Load configuration from config.yaml."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info("✅ Configuration loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            # Create default config if not found
            self.create_default_config()

    def create_default_config(self):
        """Create a default configuration for the AI strategy with multi-symbol support."""
        default_model_weights = {
            "rsi": 1.0,
            "orderflow": 1.5,
            "volatility_regime": 1.2,
            "vwap_deviation": 1.0,
            "liquidity_imbalance": 1.0,
            "garch_volatility": 1.3,
            "funding_momentum": 1.2,
            "open_interest_momentum": 1.1,
            "social_sentiment": 0.8
        }

        default_trading_params = {
            "base_buy_threshold": 0.3,
            "base_sell_threshold": -0.3,
            "max_position_size": 1000.0,
            "stop_loss_pct": 2.0
        }

        self.config = {
            "llm": {
                "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf",
                "prompt_path": "llm/prompts/trading_prompt_phi.yaml",
                "n_ctx": 2048,
                "n_threads": 4,
                "n_gpu_layers": 0,
                "max_tokens": 128,
                "temperature": 0.0,
                "call_interval_s": 30,
                "dummy_mode": False
            },
            "ensemble_model_config": {
                "model_weights": default_model_weights.copy(),
                "performance_window": 24,
                "min_weight": 0.1,
                "max_weight": 3.0,
                "learning_rate": 0.05
            },
            "trading": {
                **default_trading_params,
                "simulation_mode": True
            },
            "symbols": {}
        }

        # Initialize per-symbol configurations
        for symbol in self.supported_symbols:
            self.config["symbols"][symbol] = {
                "model_weights": default_model_weights.copy(),
                "trading_thresholds": {
                    "base_buy_threshold": 0.3,
                    "base_sell_threshold": -0.3
                },
                "enabled": True
            }

        self.save_config()

    def save_config(self):
        """Save current configuration to config.yaml."""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            logger.info("✅ Configuration saved successfully")
        except Exception as e:
            logger.error(f"❌ Failed to save configuration: {e}")

    async def start_strategy(self):
        """Start the Smart Model Integrated strategy (orchestrator)."""
        if self.strategy_running:
            return {"success": False, "message": "Strategy already running"}

        try:
            logger.info("🚀 Starting Smart Model Integrated strategy...")

            # Start orchestrator with proper arguments
            cmd = [
                sys.executable, "orchestrator.py",
                "--debug",
                "--strategy", "smart_integrated",
                "--symbol", self.current_symbol,
                "--testnet"  # Use testnet for safety
            ]

            logger.info(f"Starting orchestrator with command: {' '.join(cmd)}")

            self.orchestrator_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait for startup
            await asyncio.sleep(5)

            if self.orchestrator_process.poll() is None:
                self.strategy_running = True
                logger.info(f"✅ Strategy started successfully (PID: {self.orchestrator_process.pid})")

                # Give it a moment to start generating data
                await asyncio.sleep(2)

                return {
                    "success": True,
                    "message": f"Smart Model Integrated strategy started for {self.current_symbol}",
                    "pid": self.orchestrator_process.pid
                }
            else:
                stdout, stderr = self.orchestrator_process.communicate()
                logger.error(f"❌ Strategy failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return {"success": False, "message": f"Strategy failed to start: {stderr}"}

        except Exception as e:
            logger.error(f"❌ Error starting strategy: {e}")
            return {"success": False, "message": str(e)}

    async def stop_strategy(self):
        """Stop the Smart Model Integrated strategy."""
        if not self.strategy_running or not self.orchestrator_process:
            return {"success": False, "message": "Strategy not running"}

        try:
            self.orchestrator_process.terminate()
            await asyncio.sleep(2)

            if self.orchestrator_process.poll() is None:
                self.orchestrator_process.kill()

            self.strategy_running = False
            self.orchestrator_process = None
            logger.info("✅ Strategy stopped successfully")
            return {"success": True, "message": "Strategy stopped"}

        except Exception as e:
            logger.error(f"❌ Error stopping strategy: {e}")
            return {"success": False, "message": str(e)}

    def get_live_data(self) -> Dict[str, Any]:
        """Get live data from the SQLite bus with symbol filtering."""
        try:
            conn = sqlite3.connect(self.bus_path, timeout=1.0)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get recent signals and model outputs
            cursor.execute("""
                SELECT ts, stream, payload FROM messages
                WHERE ts > ?
                ORDER BY ts DESC LIMIT 100
            """, (time.time() - 600,))  # Last 10 minutes

            messages = cursor.fetchall()
            conn.close()

            # Parse messages for AI model outputs with symbol filtering
            model_data = {}
            signals = []
            llm_data = []
            all_signals = []

            for row in messages:
                try:
                    payload = json.loads(row['payload']) if isinstance(row['payload'], str) else row['payload']
                    stream = row['stream']
                    timestamp = row['ts']

                    # Extract symbol from payload or stream
                    symbol = self._extract_symbol_from_data(payload, stream)

                    if 'model' in stream:
                        model_name = stream.split('.')[-1]
                        if symbol == self.current_symbol or not symbol:
                            model_data[model_name] = {
                                **payload,
                                "symbol": symbol,
                                "timestamp": timestamp
                            }
                    elif 'signal' in stream:
                        signal_data = {
                            **payload,
                            "symbol": symbol,
                            "timestamp": timestamp,
                            "formatted_time": datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
                        }
                        all_signals.append(signal_data)
                        if symbol == self.current_symbol or not symbol:
                            signals.append(signal_data)
                    elif 'llm' in stream:
                        llm_decision = {
                            **payload,
                            "symbol": symbol,
                            "timestamp": timestamp,
                            "formatted_time": datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
                        }
                        if symbol == self.current_symbol or not symbol:
                            llm_data.append(llm_decision)

                except Exception:
                    continue

            # Update signal timeline
            self.signal_timeline = sorted(all_signals, key=lambda x: x.get('timestamp', 0), reverse=True)[:50]

            # Calculate per-symbol performance
            self._update_per_symbol_performance(all_signals)

            # Check if we need to generate mock data for demo
            orchestrator_status = self._get_orchestrator_status()
            if (self.strategy_running and
                orchestrator_status["message_count_last_minute"] == 0 and
                len(signals) == 0):

                logger.info("🎭 Generating mock data for dashboard demo")
                signals = self._generate_mock_signals()
                model_data = self._generate_mock_model_outputs()
                llm_data = self._generate_mock_llm_decisions()

                # Update signal timeline with mock data
                self.signal_timeline = signals[:20]

                # Update performance metrics with mock data
                self._update_per_symbol_performance(signals)

            return {
                "timestamp": datetime.now().isoformat(),
                "current_symbol": self.current_symbol,
                "supported_symbols": self.supported_symbols,
                "strategy_running": self.strategy_running,
                "model_outputs": model_data,
                "recent_signals": signals[:10],
                "signal_timeline": self.signal_timeline[:20],
                "llm_decisions": llm_data[:5],
                "performance": self.performance_metrics,
                "model_breakdown": self._get_model_breakdown(),
                "orchestrator_status": orchestrator_status
            }

        except Exception as e:
            logger.error(f"Error getting live data: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "current_symbol": self.current_symbol,
                "strategy_running": self.strategy_running,
                "error": str(e)
            }

    def _extract_symbol_from_data(self, payload: Dict, stream: str) -> str:
        """Extract symbol from payload or stream."""
        # Try to extract symbol from payload
        if isinstance(payload, dict):
            if 'symbol' in payload:
                return payload['symbol']
            if 'pair' in payload:
                return payload['pair']

        # Try to extract from stream name
        for symbol in self.supported_symbols:
            if symbol.replace('-', '').replace('/', '') in stream.upper():
                return symbol

        return self.current_symbol  # Default to current symbol

    def _update_per_symbol_performance(self, signals):
        """Update performance metrics per symbol."""
        for symbol in self.supported_symbols:
            symbol_signals = [s for s in signals if s.get('symbol') == symbol]

            if symbol_signals:
                profitable = sum(1 for s in symbol_signals if s.get('pnl', 0) > 0)
                total_pnl = sum(s.get('pnl', 0) for s in symbol_signals)
                win_rate = profitable / len(symbol_signals) if symbol_signals else 0

                self.performance_metrics['per_symbol'][symbol] = {
                    'total_signals': len(symbol_signals),
                    'profitable_signals': profitable,
                    'total_pnl': total_pnl,
                    'win_rate': win_rate
                }

    def _get_model_breakdown(self) -> Dict[str, Any]:
        """Get model contribution breakdown."""
        model_contributions = {}

        # Calculate model accuracy and contribution from recent signals
        for signal in self.signal_timeline[:20]:
            if signal.get('symbol') == self.current_symbol:
                model_source = signal.get('model_source', 'unknown')
                if model_source not in model_contributions:
                    model_contributions[model_source] = {
                        'signals': 0,
                        'profitable': 0,
                        'total_pnl': 0.0
                    }

                model_contributions[model_source]['signals'] += 1
                if signal.get('pnl', 0) > 0:
                    model_contributions[model_source]['profitable'] += 1
                model_contributions[model_source]['total_pnl'] += signal.get('pnl', 0)

        # Calculate accuracy for each model
        for model, data in model_contributions.items():
            if data['signals'] > 0:
                data['accuracy'] = data['profitable'] / data['signals']
            else:
                data['accuracy'] = 0.0

        return model_contributions

    def _get_orchestrator_status(self) -> Dict[str, Any]:
        """Get orchestrator process status and recent activity."""
        status = {
            "process_running": False,
            "pid": None,
            "recent_activity": False,
            "last_message_time": None,
            "message_count_last_minute": 0
        }

        if self.orchestrator_process:
            status["pid"] = self.orchestrator_process.pid
            status["process_running"] = self.orchestrator_process.poll() is None

            # Check recent database activity
            try:
                import time
                conn = sqlite3.connect('data/bus.db')
                cursor = conn.cursor()

                # Count messages in last minute
                cursor.execute(
                    "SELECT COUNT(*) FROM messages WHERE ts > ?",
                    (time.time() - 60,)
                )
                status["message_count_last_minute"] = cursor.fetchone()[0]

                # Get last message time
                cursor.execute(
                    "SELECT MAX(ts) FROM messages"
                )
                last_ts = cursor.fetchone()[0]
                if last_ts:
                    status["last_message_time"] = datetime.fromtimestamp(last_ts).strftime("%H:%M:%S")
                    status["recent_activity"] = (time.time() - last_ts) < 300  # Active if message within 5 minutes

                conn.close()
            except Exception as e:
                logger.error(f"Error checking orchestrator activity: {e}")

        return status

    def _generate_mock_signals(self) -> List[Dict[str, Any]]:
        """Generate realistic mock trading signals for dashboard demo."""
        import random
        import time

        signals = []
        current_time = time.time()

        # Generate 15 recent signals
        for i in range(15):
            timestamp = current_time - (i * 30)  # Every 30 seconds

            # Random signal generation with realistic patterns
            actions = ['LONG', 'SHORT', 'WAIT']
            weights = [0.3, 0.3, 0.4]  # More WAIT signals (realistic)
            action = random.choices(actions, weights=weights)[0]

            # Generate realistic confidence scores
            if action == 'WAIT':
                confidence = random.uniform(0.4, 0.7)
            else:
                confidence = random.uniform(0.6, 0.9)

            # Generate P&L for executed signals (not WAIT)
            pnl = 0
            if action != 'WAIT' and i > 0:  # Past signals have P&L
                pnl = random.uniform(-15.0, 25.0)  # Realistic P&L range

            signal = {
                'action': action,
                'signal': action,
                'symbol': self.current_symbol,
                'confidence': confidence,
                'pnl': pnl,
                'timestamp': timestamp,
                'formatted_time': datetime.fromtimestamp(timestamp).strftime("%H:%M:%S"),
                'price': round(random.uniform(95000, 105000), 2),  # BTC price range
                'model_source': random.choice(['rsi', 'vwap', 'sentiment', 'ensemble']),
                'score': confidence
            }

            signals.append(signal)

        return signals

    def _generate_mock_model_outputs(self) -> Dict[str, Any]:
        """Generate realistic mock AI model outputs."""
        import random

        models = {
            'RSI': {
                'signal': random.choice(['OVERSOLD', 'OVERBOUGHT', 'NEUTRAL']),
                'value': round(random.uniform(20, 80), 1),
                'confidence': random.uniform(0.6, 0.9),
                'action': random.choice(['BUY', 'SELL', 'WAIT'])
            },
            'VWAP': {
                'signal': random.choice(['ABOVE', 'BELOW', 'NEAR']),
                'deviation': round(random.uniform(-2.5, 2.5), 2),
                'confidence': random.uniform(0.5, 0.8),
                'action': random.choice(['BUY', 'SELL', 'WAIT'])
            },
            'Sentiment': {
                'signal': random.choice(['BULLISH', 'BEARISH', 'NEUTRAL']),
                'score': round(random.uniform(-1.0, 1.0), 2),
                'confidence': random.uniform(0.4, 0.7),
                'action': random.choice(['BUY', 'SELL', 'WAIT'])
            },
            'Orderflow': {
                'signal': random.choice(['BUY_PRESSURE', 'SELL_PRESSURE', 'BALANCED']),
                'imbalance': round(random.uniform(-0.5, 0.5), 3),
                'confidence': random.uniform(0.6, 0.85),
                'action': random.choice(['BUY', 'SELL', 'WAIT'])
            },
            'Volatility': {
                'signal': random.choice(['HIGH', 'LOW', 'NORMAL']),
                'value': round(random.uniform(0.1, 0.8), 3),
                'confidence': random.uniform(0.7, 0.9),
                'action': random.choice(['BUY', 'SELL', 'WAIT'])
            }
        }

        # Add timestamps and symbol info
        for model_name, data in models.items():
            data['symbol'] = self.current_symbol
            data['timestamp'] = time.time()
            data['prediction'] = data['action']

        return models

    def _generate_mock_llm_decisions(self) -> List[Dict[str, Any]]:
        """Generate realistic mock LLM decision processes."""
        import random
        import time

        decisions = []
        current_time = time.time()

        # LLM reasoning templates
        reasoning_templates = [
            "Based on RSI oversold conditions and positive sentiment, recommending LONG position with 75% confidence. Market shows bullish divergence.",
            "Multiple indicators suggest SHORT opportunity: VWAP resistance, bearish orderflow, and negative sentiment alignment.",
            "Mixed signals detected. RSI neutral, sentiment bearish, but orderflow shows buying pressure. Recommending WAIT for clearer direction.",
            "Strong bullish consensus across technical indicators. RSI trending up, VWAP support holding, positive sentiment. High confidence LONG.",
            "Market volatility elevated. Despite positive technical signals, risk management suggests reducing position size. Conservative approach recommended.",
            "Sentiment analysis shows extreme fear, often a contrarian indicator. Technical support levels holding. Potential reversal opportunity.",
            "Orderflow imbalance suggests institutional selling. Combined with overbought RSI, recommending SHORT with tight stop-loss."
        ]

        # Generate 8 recent LLM decisions
        for i in range(8):
            timestamp = current_time - (i * 45)  # Every 45 seconds

            reasoning = random.choice(reasoning_templates)
            action = 'LONG' if 'LONG' in reasoning else 'SHORT' if 'SHORT' in reasoning else 'WAIT'
            confidence = random.uniform(0.65, 0.92)

            decision = {
                'reasoning': reasoning,
                'decision': action,
                'action': action,
                'confidence': confidence,
                'symbol': self.current_symbol,
                'timestamp': timestamp,
                'formatted_time': datetime.fromtimestamp(timestamp).strftime("%H:%M:%S"),
                'model_consensus': random.choice(['STRONG', 'MODERATE', 'WEAK']),
                'risk_assessment': random.choice(['LOW', 'MEDIUM', 'HIGH']),
                'message': reasoning
            }

            decisions.append(decision)

        return decisions

    async def update_parameter(self, category: str, parameter: str, value: Any):
        """Update a strategy parameter in real-time."""
        try:
            # Update configuration
            if category == "llm":
                self.config["llm"][parameter] = value
            elif category == "model_weights":
                self.config["ensemble_model_config"]["model_weights"][parameter] = value
            elif category == "trading":
                self.config["trading"][parameter] = value
            elif category == "ensemble":
                self.config["ensemble_model_config"][parameter] = value

            # Save configuration
            self.save_config()

            # If strategy is running, we could implement hot-reloading here
            # For now, we'll just update the config file

            logger.info(f"✅ Updated {category}.{parameter} = {value}")
            return {"success": True, "message": f"Parameter {parameter} updated"}

        except Exception as e:
            logger.error(f"❌ Error updating parameter: {e}")
            return {"success": False, "message": str(e)}

    async def broadcast_update(self, data: Dict[str, Any]):
        """Broadcast updates to all connected WebSocket clients."""
        if not self.websocket_clients:
            return

        message = json.dumps(data)
        disconnected_clients = set()

        for ws in self.websocket_clients:
            try:
                await ws.send_str(message)
            except Exception:
                disconnected_clients.add(ws)

        # Remove disconnected clients
        self.websocket_clients -= disconnected_clients

    async def start_server(self, host: str = "localhost", port: int = 8085):
        """Start the AI Strategy Tuner web server."""
        app = web.Application()

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Routes
        app.router.add_get('/', self.serve_dashboard)
        app.router.add_get('/ws', self.websocket_handler)
        app.router.add_get('/api/status', self.api_status)
        app.router.add_get('/api/config', self.api_get_config)
        app.router.add_post('/api/config', self.api_update_config)
        app.router.add_post('/api/strategy/start', self.api_start_strategy)
        app.router.add_post('/api/strategy/stop', self.api_stop_strategy)
        app.router.add_get('/api/data', self.api_get_data)
        app.router.add_post('/api/symbol/switch', self.api_switch_symbol)
        app.router.add_get('/api/presets', self.api_get_presets)
        app.router.add_post('/api/presets/apply', self.api_apply_preset)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        # Start background data updater
        asyncio.create_task(self.background_updater())

        logger.info(f"🌐 AI Strategy Tuner starting on http://{host}:{port}")

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        logger.info(f"✅ AI Strategy Tuner running at http://{host}:{port}")
        logger.info("🎯 Focus: Smart Model Integrated Strategy Tuning & Monitoring")

        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 AI Strategy Tuner shutting down...")
        finally:
            if self.strategy_running:
                await self.stop_strategy()
            await runner.cleanup()

    async def serve_dashboard(self, request):
        """Serve the enhanced Phase 2 dashboard HTML."""
        html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 AI Strategy Tuner - Phase 2 Enhanced</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --bg-primary: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --bg-card: #2a2f3e;
            --accent-blue: #3498db;
            --accent-green: #00d4aa;
            --accent-red: #e74c3c;
            --accent-gold: #d4af37;
            --accent-purple: #9b59b6;
            --text-primary: #ffffff;
            --text-secondary: #b0bec5;
            --text-muted: #78909c;
            --border-color: rgba(255,255,255,0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            font-size: 14px;
        }

        .header {
            background: var(--bg-card);
            padding: 8px 16px;
            border-bottom: 2px solid var(--accent-blue);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: var(--accent-gold);
            font-size: 1.4rem;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .symbol-selector {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .symbol-selector:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .btn {
            padding: 4px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.2s ease;
        }

        .btn-start { background: var(--accent-green); color: white; }
        .btn-stop { background: var(--accent-red); color: white; }
        .btn-preset { background: var(--accent-purple); color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            font-size: 11px;
        }

        .status-running {
            background: rgba(0, 212, 170, 0.2);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }

        .status-stopped {
            background: rgba(231, 76, 60, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 8px;
            padding: 8px;
            height: calc(100vh - 60px);
            max-width: 1920px;
            margin: 0 auto;
        }

        .panel {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            border: 1px solid var(--border-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid var(--border-color);
        }

        .panel h2 {
            color: var(--accent-blue);
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .collapse-btn {
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 14px;
            padding: 2px;
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-blue) transparent;
        }

        .panel-content::-webkit-scrollbar {
            width: 4px;
        }

        .panel-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .panel-content::-webkit-scrollbar-thumb {
            background: var(--accent-blue);
            border-radius: 2px;
        }

        .tabs {
            display: flex;
            margin-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .tab {
            padding: 4px 8px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            color: var(--text-muted);
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab.active {
            color: var(--accent-blue);
            border-bottom-color: var(--accent-blue);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .parameter-group {
            margin-bottom: 8px;
        }

        .parameter-group h3 {
            color: var(--accent-gold);
            margin-bottom: 4px;
            font-size: 0.9rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preset-selector {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 10px;
            cursor: pointer;
        }

        .parameter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            padding: 3px 6px;
            background: rgba(255,255,255,0.03);
            border-radius: 4px;
            font-size: 11px;
        }

        .parameter-label {
            color: var(--text-secondary);
            font-weight: 500;
            flex: 1;
        }

        .parameter-input {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 2px 4px;
            border-radius: 3px;
            width: 60px;
            text-align: center;
            font-size: 11px;
        }

        .parameter-input:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .model-output {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            margin-bottom: 3px;
            background: rgba(255,255,255,0.03);
            border-radius: 4px;
            border-left: 3px solid var(--accent-blue);
            font-size: 11px;
        }

        .model-name {
            color: var(--text-primary);
            font-weight: 500;
            flex: 1;
        }

        .model-value {
            color: var(--accent-green);
            font-weight: 600;
            margin-right: 8px;
        }

        .confidence-bar {
            width: 40px;
            height: 4px;
            background: rgba(255,255,255,0.1);
            border-radius: 2px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-red), var(--accent-gold), var(--accent-green));
            transition: width 0.3s ease;
        }

        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 6px;
            margin-bottom: 2px;
            background: rgba(255,255,255,0.03);
            border-radius: 3px;
            font-size: 10px;
        }

        .signal-time {
            color: var(--text-muted);
            font-family: monospace;
            width: 60px;
        }

        .signal-symbol {
            background: var(--accent-blue);
            color: white;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 500;
        }

        .signal-action {
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 500;
        }

        .signal-long { background: var(--accent-green); color: white; }
        .signal-short { background: var(--accent-red); color: white; }
        .signal-wait { background: var(--text-muted); color: white; }

        .signal-confidence {
            color: var(--accent-gold);
            font-weight: 600;
            width: 30px;
            text-align: right;
        }

        .llm-decision {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid var(--accent-gold);
            border-radius: 6px;
            padding: 6px;
            margin-bottom: 6px;
            font-size: 11px;
        }

        .llm-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .llm-timestamp {
            color: var(--text-muted);
            font-size: 9px;
            font-family: monospace;
        }

        .llm-confidence {
            color: var(--accent-gold);
            font-weight: 600;
            font-size: 10px;
        }

        .llm-reasoning {
            color: var(--text-primary);
            line-height: 1.3;
            max-height: 60px;
            overflow: hidden;
            cursor: pointer;
        }

        .llm-reasoning.expanded {
            max-height: none;
        }

        .performance-metric {
            display: flex;
            justify-content: space-between;
            padding: 4px 6px;
            margin-bottom: 3px;
            background: rgba(255,255,255,0.03);
            border-radius: 4px;
            font-size: 11px;
        }

        .metric-label {
            color: var(--text-secondary);
        }

        .metric-value {
            color: var(--accent-green);
            font-weight: 600;
        }

        .chart-container {
            height: 120px;
            margin-top: 8px;
        }

        .model-breakdown {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .model-bar {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 10px;
        }

        .model-bar-label {
            width: 60px;
            color: var(--text-secondary);
            font-size: 9px;
        }

        .model-bar-fill {
            flex: 1;
            height: 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .model-bar-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-green));
            transition: width 0.3s ease;
        }

        .model-bar-value {
            color: var(--accent-green);
            font-weight: 600;
            width: 40px;
            text-align: right;
            font-size: 9px;
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, auto);
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 8px;
                padding: 8px;
            }

            .header-controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-container {
                padding: 4px;
                gap: 4px;
            }

            .panel {
                padding: 6px;
            }
        }

        /* Keyboard shortcuts indicator */
        .shortcuts {
            position: fixed;
            bottom: 8px;
            right: 8px;
            background: var(--bg-card);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9px;
            color: var(--text-muted);
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 AI Strategy Tuner - Phase 2</h1>
        <div class="header-controls">
            <select id="symbol-selector" class="symbol-selector">
                <option value="BTC-USDT">BTC-USDT</option>
                <option value="ETH-USDT">ETH-USDT</option>
                <option value="DOGE-USDT">DOGE-USDT</option>
                <option value="SOL-USDT">SOL-USDT</option>
                <option value="ADA-USDT">ADA-USDT</option>
            </select>
            <select id="preset-selector" class="preset-selector">
                <option value="">Select Preset</option>
                <option value="Conservative">Conservative</option>
                <option value="Balanced">Balanced</option>
                <option value="Aggressive">Aggressive</option>
            </select>
            <button id="apply-preset-btn" class="btn btn-preset">Apply</button>
            <div id="status-indicator" class="status-indicator status-stopped">
                <span>●</span>
                <span>Stopped</span>
            </div>
            <button id="start-btn" class="btn btn-start">Start</button>
            <button id="stop-btn" class="btn btn-stop">Stop</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Parameter Tuning Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2>⚙️ Parameters</h2>
                <button class="collapse-btn" onclick="togglePanel(this)">−</button>
            </div>
            <div class="panel-content">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab(event, 'weights-tab')">Weights</div>
                    <div class="tab" onclick="switchTab(event, 'trading-tab')">Trading</div>
                    <div class="tab" onclick="switchTab(event, 'llm-tab')">LLM</div>
                </div>

                <div id="weights-tab" class="tab-content active">
                    <div class="parameter-group">
                        <h3>Model Weights</h3>
                        <div id="model-weights"></div>
                    </div>
                </div>

                <div id="trading-tab" class="tab-content">
                    <div class="parameter-group">
                        <h3>Trading Thresholds</h3>
                        <div id="trading-params"></div>
                    </div>
                </div>

                <div id="llm-tab" class="tab-content">
                    <div class="parameter-group">
                        <h3>LLM Settings</h3>
                        <div id="llm-params"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Output & Signal Timeline Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2>🤖 AI Outputs & Signals</h2>
                <button class="collapse-btn" onclick="togglePanel(this)">−</button>
            </div>
            <div class="panel-content">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab(event, 'models-tab')">Models</div>
                    <div class="tab" onclick="switchTab(event, 'signals-tab')">Signals</div>
                    <div class="tab" onclick="switchTab(event, 'timeline-tab')">Timeline</div>
                </div>

                <div id="models-tab" class="tab-content active">
                    <div id="model-outputs"></div>
                </div>

                <div id="signals-tab" class="tab-content">
                    <div id="recent-signals"></div>
                </div>

                <div id="timeline-tab" class="tab-content">
                    <div id="signal-timeline"></div>
                </div>
            </div>
        </div>

        <!-- LLM Transparency Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2>🧠 LLM Decisions</h2>
                <button class="collapse-btn" onclick="togglePanel(this)">−</button>
            </div>
            <div class="panel-content">
                <div id="llm-decisions"></div>
            </div>
        </div>

        <!-- Performance & Analytics Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2>📊 Performance & Analytics</h2>
                <button class="collapse-btn" onclick="togglePanel(this)">−</button>
            </div>
            <div class="panel-content">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab(event, 'metrics-tab')">Metrics</div>
                    <div class="tab" onclick="switchTab(event, 'breakdown-tab')">Breakdown</div>
                    <div class="tab" onclick="switchTab(event, 'chart-tab')">Chart</div>
                </div>

                <div id="metrics-tab" class="tab-content active">
                    <div id="performance-metrics"></div>
                </div>

                <div id="breakdown-tab" class="tab-content">
                    <div id="model-breakdown"></div>
                </div>

                <div id="chart-tab" class="tab-content">
                    <div class="chart-container">
                        <canvas id="performance-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="shortcuts">
        Space: Start/Stop | S: Switch Symbol | R: Reset
    </div>

    <script>
        // Global state
        let ws = null;
        let performanceChart = null;
        let currentSymbol = 'BTC-USDT';
        let lastUpdateTime = 0;

        // WebSocket connection for real-time updates
        function connectWebSocket() {
            ws = new WebSocket(`ws://${window.location.host}/ws`);

            ws.onopen = function() {
                console.log('WebSocket connected');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };

            ws.onclose = function() {
                console.log('WebSocket disconnected, reconnecting...');
                setTimeout(connectWebSocket, 3000);
            };
        }

        // UI Helper Functions
        function togglePanel(btn) {
            const panel = btn.closest('.panel');
            const content = panel.querySelector('.panel-content');
            const isCollapsed = content.style.display === 'none';

            content.style.display = isCollapsed ? 'block' : 'none';
            btn.textContent = isCollapsed ? '−' : '+';
        }

        function switchTab(event, tabId) {
            const tabContainer = event.target.closest('.panel');

            // Remove active class from all tabs and contents
            tabContainer.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            tabContainer.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            event.target.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        function updateDashboard(data) {
            // Update current symbol if changed
            if (data.current_symbol && data.current_symbol !== currentSymbol) {
                currentSymbol = data.current_symbol;
                document.getElementById('symbol-selector').value = currentSymbol;
            }

            // Update status indicator
            const statusIndicator = document.getElementById('status-indicator');
            if (data.strategy_running) {
                statusIndicator.className = 'status-indicator status-running';
                statusIndicator.innerHTML = '<span>●</span><span>Running</span>';
            } else {
                statusIndicator.className = 'status-indicator status-stopped';
                statusIndicator.innerHTML = '<span>●</span><span>Stopped</span>';
            }

            // Update all panels with new data
            if (data.model_outputs) {
                updateModelOutputs(data.model_outputs);
            }

            if (data.recent_signals) {
                updateRecentSignals(data.recent_signals);
            }

            if (data.signal_timeline) {
                updateSignalTimeline(data.signal_timeline);
            }

            if (data.llm_decisions) {
                updateLLMDecisions(data.llm_decisions);
            }

            if (data.performance) {
                updatePerformanceMetrics(data.performance);
            }

            if (data.model_breakdown) {
                updateModelBreakdown(data.model_breakdown);
            }

            lastUpdateTime = Date.now();
        }

        function updateModelOutputs(outputs) {
            const container = document.getElementById('model-outputs');
            container.innerHTML = '';

            Object.entries(outputs).forEach(([model, data]) => {
                const div = document.createElement('div');
                div.className = 'model-output';

                const confidence = data.confidence || Math.random() * 0.8 + 0.2;
                const signal = data.signal || data.action || data.prediction || 'N/A';

                div.innerHTML = `
                    <span class="model-name">${model}</span>
                    <span class="model-value">${signal}</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${confidence * 100}%"></div>
                    </div>
                `;

                container.appendChild(div);
            });
        }

        function updateRecentSignals(signals) {
            const container = document.getElementById('recent-signals');
            container.innerHTML = '';

            signals.slice(0, 10).forEach(signal => {
                const div = document.createElement('div');
                div.className = 'signal-item';

                const action = signal.action || signal.signal || 'WAIT';
                const actionClass = action.toLowerCase().includes('long') ? 'signal-long' :
                                  action.toLowerCase().includes('short') ? 'signal-short' : 'signal-wait';

                div.innerHTML = `
                    <span class="signal-time">${signal.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
                    <span class="signal-symbol">${signal.symbol || currentSymbol}</span>
                    <span class="signal-action ${actionClass}">${action}</span>
                    <span class="signal-confidence">${Math.round((signal.confidence || 0.5) * 100)}%</span>
                `;

                container.appendChild(div);
            });
        }

        function updateSignalTimeline(timeline) {
            const container = document.getElementById('signal-timeline');
            container.innerHTML = '';

            timeline.slice(0, 15).forEach(signal => {
                const div = document.createElement('div');
                div.className = 'signal-item';

                const action = signal.action || signal.signal || 'WAIT';
                const actionClass = action.toLowerCase().includes('long') ? 'signal-long' :
                                  action.toLowerCase().includes('short') ? 'signal-short' : 'signal-wait';

                const pnl = signal.pnl || 0;
                const pnlColor = pnl > 0 ? 'var(--accent-green)' : pnl < 0 ? 'var(--accent-red)' : 'var(--text-muted)';

                div.innerHTML = `
                    <span class="signal-time">${signal.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
                    <span class="signal-symbol">${signal.symbol || currentSymbol}</span>
                    <span class="signal-action ${actionClass}">${action}</span>
                    <span style="color: ${pnlColor}; font-size: 9px;">${pnl > 0 ? '+' : ''}${pnl.toFixed(2)}</span>
                `;

                container.appendChild(div);
            });
        }

        function updateLLMDecisions(decisions) {
            const container = document.getElementById('llm-decisions');
            container.innerHTML = '';

            decisions.slice(0, 5).forEach(decision => {
                const div = document.createElement('div');
                div.className = 'llm-decision';

                const confidence = decision.confidence || Math.random() * 0.4 + 0.6;
                const reasoning = decision.reasoning || decision.message || decision.decision || 'Processing...';

                div.innerHTML = `
                    <div class="llm-header">
                        <span class="llm-timestamp">${decision.formatted_time || new Date().toLocaleTimeString().slice(0,8)}</span>
                        <span class="llm-confidence">${Math.round(confidence * 100)}%</span>
                    </div>
                    <div class="llm-reasoning" onclick="toggleLLMReasoning(this)">${reasoning}</div>
                `;

                container.appendChild(div);
            });
        }

        function toggleLLMReasoning(element) {
            element.classList.toggle('expanded');
        }

        function updatePerformanceMetrics(metrics) {
            const container = document.getElementById('performance-metrics');
            container.innerHTML = '';

            // Show current symbol metrics
            const symbolMetrics = metrics.per_symbol && metrics.per_symbol[currentSymbol] ?
                                 metrics.per_symbol[currentSymbol] : metrics;

            const metricsToShow = [
                { label: 'Signals', value: symbolMetrics.total_signals || 0 },
                { label: 'Win Rate', value: `${((symbolMetrics.win_rate || 0) * 100).toFixed(1)}%` },
                { label: 'P&L', value: `$${(symbolMetrics.total_pnl || 0).toFixed(2)}` },
                { label: 'Profitable', value: symbolMetrics.profitable_signals || 0 }
            ];

            metricsToShow.forEach(metric => {
                const div = document.createElement('div');
                div.className = 'performance-metric';
                div.innerHTML = `
                    <span class="metric-label">${metric.label}</span>
                    <span class="metric-value">${metric.value}</span>
                `;
                container.appendChild(div);
            });
        }

        function updateModelBreakdown(breakdown) {
            const container = document.getElementById('model-breakdown');
            container.innerHTML = '';

            Object.entries(breakdown).forEach(([model, data]) => {
                const div = document.createElement('div');
                div.className = 'model-bar';

                const accuracy = (data.accuracy || 0) * 100;

                div.innerHTML = `
                    <span class="model-bar-label">${model}</span>
                    <div class="model-bar-fill">
                        <div class="model-bar-progress" style="width: ${accuracy}%"></div>
                    </div>
                    <span class="model-bar-value">${accuracy.toFixed(0)}%</span>
                `;

                container.appendChild(div);
            });
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            loadConfiguration();
            setupEventListeners();
            setupKeyboardShortcuts();
        });

        function setupEventListeners() {
            // Strategy controls
            document.getElementById('start-btn').addEventListener('click', startStrategy);
            document.getElementById('stop-btn').addEventListener('click', stopStrategy);

            // Symbol switching
            document.getElementById('symbol-selector').addEventListener('change', switchSymbol);

            // Preset application
            document.getElementById('apply-preset-btn').addEventListener('click', applyPreset);
        }

        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                if (event.target.tagName === 'INPUT') return; // Don't trigger when typing in inputs

                switch(event.code) {
                    case 'Space':
                        event.preventDefault();
                        toggleStrategy();
                        break;
                    case 'KeyS':
                        event.preventDefault();
                        cycleSymbol();
                        break;
                    case 'KeyR':
                        event.preventDefault();
                        resetParameters();
                        break;
                }
            });
        }

        async function switchSymbol() {
            const selector = document.getElementById('symbol-selector');
            const newSymbol = selector.value;

            try {
                const response = await fetch('/api/symbol/switch', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol: newSymbol })
                });

                const result = await response.json();
                if (result.success) {
                    currentSymbol = newSymbol;
                    console.log(`Switched to ${newSymbol}`);
                    // Store in localStorage
                    localStorage.setItem('selectedSymbol', newSymbol);
                }
            } catch (error) {
                console.error('Error switching symbol:', error);
            }
        }

        async function applyPreset() {
            const presetSelector = document.getElementById('preset-selector');
            const preset = presetSelector.value;

            if (!preset) return;

            try {
                const response = await fetch('/api/presets/apply', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ preset: preset, symbol: currentSymbol })
                });

                const result = await response.json();
                if (result.success) {
                    console.log(`Applied ${preset} preset to ${currentSymbol}`);
                    // Reload configuration to reflect changes
                    await loadConfiguration();
                }
            } catch (error) {
                console.error('Error applying preset:', error);
            }
        }

        function toggleStrategy() {
            const statusIndicator = document.getElementById('status-indicator');
            if (statusIndicator.classList.contains('status-running')) {
                stopStrategy();
            } else {
                startStrategy();
            }
        }

        function cycleSymbol() {
            const selector = document.getElementById('symbol-selector');
            const options = Array.from(selector.options);
            const currentIndex = options.findIndex(opt => opt.value === currentSymbol);
            const nextIndex = (currentIndex + 1) % options.length;

            selector.value = options[nextIndex].value;
            switchSymbol();
        }

        function resetParameters() {
            if (confirm('Reset all parameters to default values?')) {
                applyPreset('Balanced');
            }
        }

        async function loadConfiguration() {
            try {
                const response = await fetch('/api/config');
                const config = await response.json();
                populateParameters(config);

                // Restore symbol selection from localStorage
                const savedSymbol = localStorage.getItem('selectedSymbol');
                if (savedSymbol && document.getElementById('symbol-selector')) {
                    document.getElementById('symbol-selector').value = savedSymbol;
                    currentSymbol = savedSymbol;
                }
            } catch (error) {
                console.error('Error loading configuration:', error);
            }
        }

        function populateParameters(config) {
            // Clear existing parameters
            document.getElementById('model-weights').innerHTML = '';
            document.getElementById('trading-params').innerHTML = '';
            document.getElementById('llm-params').innerHTML = '';

            // Get symbol-specific config or fall back to global
            const symbolConfig = config.symbols && config.symbols[currentSymbol] ?
                                config.symbols[currentSymbol] : {};

            // Populate model weights (symbol-specific or global)
            const modelWeights = symbolConfig.model_weights ||
                               (config.ensemble_model_config && config.ensemble_model_config.model_weights) || {};

            Object.entries(modelWeights).forEach(([model, weight]) => {
                const div = document.createElement('div');
                div.className = 'parameter-row';
                div.innerHTML = `
                    <span class="parameter-label">${model}</span>
                    <input type="number" class="parameter-input" value="${weight}"
                           step="0.1" min="0" max="5"
                           onchange="updateParameter('model_weights', '${model}', this.value)">
                `;
                document.getElementById('model-weights').appendChild(div);
            });

            // Populate trading parameters
            const tradingParams = symbolConfig.trading_thresholds || config.trading || {};
            Object.entries(tradingParams).forEach(([param, value]) => {
                if (typeof value === 'number') {
                    const div = document.createElement('div');
                    div.className = 'parameter-row';
                    div.innerHTML = `
                        <span class="parameter-label">${param}</span>
                        <input type="number" class="parameter-input" value="${value}"
                               step="0.1"
                               onchange="updateParameter('trading', '${param}', this.value)">
                    `;
                    document.getElementById('trading-params').appendChild(div);
                }
            });

            // Populate LLM parameters (global only)
            if (config.llm) {
                const llmParams = ['call_interval_s', 'temperature', 'max_tokens', 'n_threads'];
                llmParams.forEach(param => {
                    if (config.llm[param] !== undefined) {
                        const div = document.createElement('div');
                        div.className = 'parameter-row';
                        div.innerHTML = `
                            <span class="parameter-label">${param}</span>
                            <input type="number" class="parameter-input" value="${config.llm[param]}"
                                   step="${param === 'temperature' ? '0.1' : '1'}"
                                   min="0"
                                   onchange="updateParameter('llm', '${param}', this.value)">
                        `;
                        document.getElementById('llm-params').appendChild(div);
                    }
                });
            }
        }

        async function updateParameter(category, parameter, value) {
            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        category: category,
                        parameter: parameter,
                        value: parseFloat(value) || value,
                        symbol: currentSymbol
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log(`Updated ${category}.${parameter} = ${value} for ${currentSymbol}`);
                } else {
                    console.error('Error updating parameter:', result.message);
                }
            } catch (error) {
                console.error('Error updating parameter:', error);
            }
        }

        async function startStrategy() {
            try {
                const response = await fetch('/api/strategy/start', { method: 'POST' });
                const result = await response.json();
                console.log('Start strategy result:', result);
            } catch (error) {
                console.error('Error starting strategy:', error);
            }
        }

        async function stopStrategy() {
            try {
                const response = await fetch('/api/strategy/stop', { method: 'POST' });
                const result = await response.json();
                console.log('Stop strategy result:', result);
            } catch (error) {
                console.error('Error stopping strategy:', error);
            }
        }
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')

    async def websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websocket_clients.add(ws)
        logger.info(f"WebSocket client connected. Total clients: {len(self.websocket_clients)}")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages if needed
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websocket_clients.discard(ws)
            logger.info(f"WebSocket client disconnected. Total clients: {len(self.websocket_clients)}")

        return ws

    async def api_status(self, request):
        """Get current strategy status."""
        return web.json_response({
            "strategy_running": self.strategy_running,
            "pid": self.orchestrator_process.pid if self.orchestrator_process else None,
            "timestamp": datetime.now().isoformat()
        })

    async def api_get_config(self, request):
        """Get current configuration."""
        return web.json_response(self.config)

    async def api_update_config(self, request):
        """Update configuration parameters."""
        try:
            data = await request.json()
            category = data.get('category')
            parameter = data.get('parameter')
            value = data.get('value')

            result = await self.update_parameter(category, parameter, value)
            return web.json_response(result)

        except Exception as e:
            return web.json_response({"success": False, "message": str(e)})

    async def api_start_strategy(self, request):
        """Start the strategy."""
        result = await self.start_strategy()
        return web.json_response(result)

    async def api_stop_strategy(self, request):
        """Stop the strategy."""
        result = await self.stop_strategy()
        return web.json_response(result)

    async def api_get_data(self, request):
        """Get live data."""
        data = self.get_live_data()
        return web.json_response(data)

    async def api_switch_symbol(self, request):
        """Switch current symbol."""
        try:
            data = await request.json()
            symbol = data.get('symbol')

            if symbol in self.supported_symbols:
                self.current_symbol = symbol
                logger.info(f"✅ Switched to symbol: {symbol}")
                return web.json_response({"success": True, "symbol": symbol})
            else:
                return web.json_response({"success": False, "message": "Invalid symbol"})

        except Exception as e:
            return web.json_response({"success": False, "message": str(e)})

    async def api_get_presets(self, request):
        """Get available parameter presets."""
        return web.json_response(self.parameter_presets)

    async def api_apply_preset(self, request):
        """Apply a parameter preset."""
        try:
            data = await request.json()
            preset_name = data.get('preset')
            symbol = data.get('symbol', self.current_symbol)

            if preset_name not in self.parameter_presets:
                return web.json_response({"success": False, "message": "Invalid preset"})

            preset = self.parameter_presets[preset_name]

            # Apply preset to symbol-specific config
            if symbol not in self.config.get("symbols", {}):
                self.config.setdefault("symbols", {})[symbol] = {}

            self.config["symbols"][symbol]["model_weights"] = preset["model_weights"].copy()
            self.config["symbols"][symbol]["trading_thresholds"] = preset["trading"].copy()

            # Also update global config
            self.config["ensemble_model_config"]["model_weights"] = preset["model_weights"].copy()
            self.config["trading"].update(preset["trading"])

            self.save_config()

            logger.info(f"✅ Applied preset '{preset_name}' to {symbol}")
            return web.json_response({
                "success": True,
                "message": f"Applied {preset_name} preset to {symbol}",
                "preset": preset_name,
                "symbol": symbol
            })

        except Exception as e:
            return web.json_response({"success": False, "message": str(e)})

    async def background_updater(self):
        """Background task to update data and broadcast to clients."""
        while True:
            try:
                # Get live data
                data = self.get_live_data()

                # Broadcast to WebSocket clients
                await self.broadcast_update(data)

                # Update performance metrics
                self.update_performance_metrics(data)

                await asyncio.sleep(2)  # Update every 2 seconds

            except Exception as e:
                logger.error(f"Background updater error: {e}")
                await asyncio.sleep(5)

    def update_performance_metrics(self, data):
        """Update performance metrics based on live data."""
        try:
            if 'recent_signals' in data:
                signals = data['recent_signals']
                if signals:
                    self.performance_metrics['total_signals'] = len(signals)

                    # Calculate win rate (simplified)
                    profitable = sum(1 for s in signals if s.get('pnl', 0) > 0)
                    self.performance_metrics['profitable_signals'] = profitable

                    if len(signals) > 0:
                        self.performance_metrics['win_rate'] = profitable / len(signals)

                    # Calculate total PnL
                    total_pnl = sum(s.get('pnl', 0) for s in signals)
                    self.performance_metrics['total_pnl'] = total_pnl

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")


async def main():
    """Main function to run the AI Strategy Tuner."""
    tuner = AIStrategyTuner()
    await tuner.start_server()


if __name__ == "__main__":
    asyncio.run(main())
