#!/usr/bin/env python3
"""
Money Circle Documentation Validation
Validates completeness and accuracy of documentation.
"""

import os
import re
from datetime import datetime

# Documentation files to validate
DOCS_DIR = "docs"
REQUIRED_DOCS = [
    "README.md",
    "DEVELOPER_ONBOARDING.md", 
    "DEVELOPMENT_GUIDELINES.md",
    "CSS_ARCHITECTURE.md",
    "PERFORMANCE_GUIDE.md",
    "COMPONENT_LIBRARY.md"
]

# Additional documentation files
ADDITIONAL_DOCS = [
    "../BROWSER_COMPATIBILITY_GUIDE.md",
    "../PERFORMANCE_OPTIMIZATION_REPORT.md"
]

def validate_file_exists(filepath):
    """Check if documentation file exists"""
    if os.path.exists(filepath):
        return True, f"✅ {filepath} exists"
    else:
        return False, f"❌ {filepath} missing"

def validate_file_content(filepath):
    """Validate documentation file content"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check file size
        if len(content) < 1000:
            issues.append("⚠️  File seems too short (< 1000 characters)")
        
        # Check for required sections
        required_sections = ["##", "###"]
        has_sections = any(section in content for section in required_sections)
        if not has_sections:
            issues.append("⚠️  No markdown sections found")
        
        # Check for code blocks
        code_blocks = len(re.findall(r'```', content))
        if code_blocks % 2 != 0:
            issues.append("❌ Unclosed code blocks detected")
        
        # Check for links
        links = len(re.findall(r'\[.*?\]\(.*?\)', content))
        
        # Check last updated date
        if "Last Updated" in content:
            date_match = re.search(r'Last Updated.*?(\d{4}-\d{2}-\d{2})', content)
            if date_match:
                doc_date = date_match.group(1)
                if doc_date != "2025-05-31":
                    issues.append(f"⚠️  Last updated date may be outdated: {doc_date}")
        else:
            issues.append("⚠️  No 'Last Updated' date found")
        
        return len(issues) == 0, issues, {
            "size": len(content),
            "sections": content.count("##"),
            "code_blocks": code_blocks // 2,
            "links": links
        }
        
    except Exception as e:
        return False, [f"❌ Error reading file: {str(e)}"], {}

def validate_cross_references():
    """Validate cross-references between documentation files"""
    print("\n🔗 Validating Cross-References...")
    
    issues = []
    
    # Check README.md links
    readme_path = os.path.join(DOCS_DIR, "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            readme_content = f.read()
        
        # Extract linked files
        links = re.findall(r'\[.*?\]\((.*?\.md)\)', readme_content)
        
        for link in links:
            # Resolve relative paths
            if link.startswith('./'):
                link_path = os.path.join(DOCS_DIR, link[2:])
            elif link.startswith('../'):
                link_path = link[3:]
            else:
                link_path = os.path.join(DOCS_DIR, link)
            
            if not os.path.exists(link_path):
                issues.append(f"❌ Broken link in README.md: {link}")
            else:
                issues.append(f"✅ Valid link: {link}")
    
    return len([i for i in issues if i.startswith("❌")]) == 0, issues

def validate_performance_claims():
    """Validate performance claims in documentation"""
    print("\n🚀 Validating Performance Claims...")
    
    claims = {
        "Grade A+": 0,
        "100/100": 0,
        "sub-30ms": 0,
        "15.3ms": 0,
        "33.1%": 0
    }
    
    for doc_file in REQUIRED_DOCS:
        filepath = os.path.join(DOCS_DIR, doc_file)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for claim in claims:
                if claim in content:
                    claims[claim] += 1
    
    print(f"  📊 Performance Claims Found:")
    for claim, count in claims.items():
        print(f"    • {claim}: {count} occurrences")
    
    # Validate consistency
    consistent = claims["Grade A+"] > 0 and claims["100/100"] > 0
    return consistent, claims

def validate_code_examples():
    """Validate code examples in documentation"""
    print("\n💻 Validating Code Examples...")
    
    code_stats = {
        "css_blocks": 0,
        "html_blocks": 0,
        "javascript_blocks": 0,
        "bash_blocks": 0,
        "python_blocks": 0
    }
    
    for doc_file in REQUIRED_DOCS:
        filepath = os.path.join(DOCS_DIR, doc_file)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count code blocks by language
            code_stats["css_blocks"] += len(re.findall(r'```css', content))
            code_stats["html_blocks"] += len(re.findall(r'```html', content))
            code_stats["javascript_blocks"] += len(re.findall(r'```javascript', content))
            code_stats["bash_blocks"] += len(re.findall(r'```bash', content))
            code_stats["python_blocks"] += len(re.findall(r'```python', content))
    
    print(f"  📝 Code Examples Found:")
    for lang, count in code_stats.items():
        print(f"    • {lang.replace('_', ' ').title()}: {count}")
    
    # Check for adequate examples
    adequate = (code_stats["css_blocks"] >= 5 and 
                code_stats["html_blocks"] >= 3 and
                code_stats["bash_blocks"] >= 2)
    
    return adequate, code_stats

def generate_documentation_report():
    """Generate comprehensive documentation validation report"""
    print("📚 Money Circle Documentation Validation")
    print("=" * 60)
    print(f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_valid = True
    total_files = 0
    valid_files = 0
    
    # Validate required documentation files
    print(f"\n📄 Required Documentation Files:")
    for doc_file in REQUIRED_DOCS:
        filepath = os.path.join(DOCS_DIR, doc_file)
        exists, message = validate_file_exists(filepath)
        print(f"  {message}")
        
        if exists:
            valid, issues, stats = validate_file_content(filepath)
            if valid:
                print(f"    ✅ Content validation passed")
                print(f"    📊 Stats: {stats['size']:,} chars, {stats['sections']} sections, {stats['code_blocks']} code blocks")
                valid_files += 1
            else:
                print(f"    ❌ Content validation failed:")
                for issue in issues:
                    print(f"      {issue}")
                all_valid = False
        else:
            all_valid = False
        
        total_files += 1
    
    # Validate additional documentation
    print(f"\n📄 Additional Documentation Files:")
    for doc_file in ADDITIONAL_DOCS:
        exists, message = validate_file_exists(doc_file)
        print(f"  {message}")
        if exists:
            valid_files += 1
        total_files += 1
    
    # Validate cross-references
    cross_ref_valid, cross_ref_issues = validate_cross_references()
    if cross_ref_valid:
        print(f"  ✅ All cross-references valid")
    else:
        print(f"  ❌ Cross-reference issues found:")
        for issue in cross_ref_issues:
            if issue.startswith("❌"):
                print(f"    {issue}")
                all_valid = False
    
    # Validate performance claims
    perf_valid, perf_claims = validate_performance_claims()
    if perf_valid:
        print(f"  ✅ Performance claims consistent")
    else:
        print(f"  ⚠️  Performance claims may be inconsistent")
    
    # Validate code examples
    code_valid, code_stats = validate_code_examples()
    if code_valid:
        print(f"  ✅ Adequate code examples provided")
    else:
        print(f"  ⚠️  Consider adding more code examples")
    
    # Generate summary
    print(f"\n" + "=" * 60)
    print(f"📋 Documentation Validation Summary")
    print(f"=" * 60)
    
    print(f"📊 File Statistics:")
    print(f"  • Total Files: {total_files}")
    print(f"  • Valid Files: {valid_files}")
    print(f"  • Success Rate: {(valid_files/total_files)*100:.1f}%")
    
    print(f"\n🎯 Validation Results:")
    print(f"  • File Existence: {'✅ Pass' if valid_files == total_files else '❌ Fail'}")
    print(f"  • Content Quality: {'✅ Pass' if all_valid else '❌ Fail'}")
    print(f"  • Cross-References: {'✅ Pass' if cross_ref_valid else '❌ Fail'}")
    print(f"  • Performance Claims: {'✅ Pass' if perf_valid else '⚠️  Check'}")
    print(f"  • Code Examples: {'✅ Pass' if code_valid else '⚠️  Improve'}")
    
    overall_grade = "A+" if all_valid and cross_ref_valid else "A" if valid_files >= total_files * 0.8 else "B"
    print(f"\n🏆 Overall Documentation Grade: {overall_grade}")
    
    if overall_grade == "A+":
        print(f"🎉 Excellent! Documentation is comprehensive and well-maintained.")
    elif overall_grade == "A":
        print(f"👍 Good documentation with minor improvements needed.")
    else:
        print(f"📝 Documentation needs attention to reach production quality.")
    
    return {
        "overall_valid": all_valid,
        "files_valid": valid_files,
        "total_files": total_files,
        "grade": overall_grade
    }

def main():
    """Run comprehensive documentation validation"""
    try:
        # Change to the correct directory
        if os.path.exists("epinnox_club"):
            os.chdir("epinnox_club")
        
        report = generate_documentation_report()
        
        print(f"\n🚀 Next Steps:")
        if report["grade"] == "A+":
            print(f"  ✅ Documentation is production-ready")
            print(f"  📚 Consider adding user guides or tutorials")
            print(f"  🔄 Set up regular documentation reviews")
        else:
            print(f"  📝 Address validation issues identified above")
            print(f"  🔍 Review content quality and completeness")
            print(f"  🔗 Fix any broken cross-references")
        
        return 0 if report["overall_valid"] else 1
        
    except Exception as e:
        print(f"\n❌ Validation failed: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
