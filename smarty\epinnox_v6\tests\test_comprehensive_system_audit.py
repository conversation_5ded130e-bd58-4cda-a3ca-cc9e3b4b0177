#!/usr/bin/env python3
"""
Comprehensive System Audit - Phase 9.14
Check for all potential issues similar to DOM access problems
"""

import asyncio
import logging
import time
import yaml
import re
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_potential_issues():
    """Analyze the codebase for potential issues similar to DOM access problems."""
    logger.info("🔍 Comprehensive System Audit - Phase 9.14")
    
    issues_found = []
    
    # 1. Check for API endpoint access without error handling
    logger.info("\n🎯 1. Checking API endpoint error handling...")
    api_issues = check_api_error_handling()
    issues_found.extend(api_issues)
    
    # 2. Check for WebSocket connection issues
    logger.info("\n🎯 2. Checking WebSocket connection handling...")
    ws_issues = check_websocket_issues()
    issues_found.extend(ws_issues)
    
    # 3. Check for file/path access issues
    logger.info("\n🎯 3. Checking file and path access...")
    file_issues = check_file_access_issues()
    issues_found.extend(file_issues)
    
    # 4. Check for dictionary/attribute access without validation
    logger.info("\n🎯 4. Checking dictionary and attribute access...")
    dict_issues = check_dictionary_access_issues()
    issues_found.extend(dict_issues)
    
    # 5. Check for async/await issues
    logger.info("\n🎯 5. Checking async/await patterns...")
    async_issues = check_async_issues()
    issues_found.extend(async_issues)
    
    # 6. Check for network/exchange connection issues
    logger.info("\n🎯 6. Checking network and exchange connections...")
    network_issues = check_network_issues()
    issues_found.extend(network_issues)
    
    # 7. Check for configuration access issues
    logger.info("\n🎯 7. Checking configuration access...")
    config_issues = check_config_issues()
    issues_found.extend(config_issues)
    
    return issues_found

def check_api_error_handling():
    """Check for API endpoints without proper error handling."""
    issues = []
    
    dashboard_file = Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py"
    
    if dashboard_file.exists():
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find API endpoints without try-catch
        api_pattern = r'async def api_(\w+)\(self, request\):'
        api_matches = re.findall(api_pattern, content)
        
        for api_name in api_matches:
            # Check if this API has proper error handling
            api_section_pattern = f'async def api_{api_name}.*?(?=async def|$)'
            api_section = re.search(api_section_pattern, content, re.DOTALL)
            
            if api_section:
                api_code = api_section.group(0)
                if 'try:' not in api_code or 'except' not in api_code:
                    issues.append({
                        'type': 'api_no_error_handling',
                        'api': f'api_{api_name}',
                        'severity': 'MEDIUM',
                        'description': f"API endpoint 'api_{api_name}' may lack proper error handling"
                    })
        
        logger.info(f"   Found {len([i for i in issues if i['type'] == 'api_no_error_handling'])} API endpoints with potential error handling issues")
    
    return issues

def check_websocket_issues():
    """Check for WebSocket connection issues."""
    issues = []
    
    # Check for WebSocket usage patterns
    files_to_check = [
        Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py",
        Path(__file__).parent.parent / "feeds" / "htx_data_producer.py"
    ]
    
    ws_issues_found = 0
    
    for file_path in files_to_check:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for WebSocket without connection validation
            if 'websocket' in content.lower() or 'ws.' in content:
                if 'ws.closed' not in content and 'connection_lost' not in content:
                    issues.append({
                        'type': 'websocket_no_validation',
                        'file': file_path.name,
                        'severity': 'HIGH',
                        'description': f"WebSocket usage in {file_path.name} may lack connection validation"
                    })
                    ws_issues_found += 1
    
    logger.info(f"   Found {ws_issues_found} potential WebSocket connection issues")
    return issues

def check_file_access_issues():
    """Check for file access without proper validation."""
    issues = []
    
    # Common file access patterns that could fail
    patterns = [
        (r'open\([\'"]([^\'\"]+)[\'"]', 'file_open_without_check'),
        (r'Path\([\'"]([^\'\"]+)[\'"]', 'path_without_validation'),
        (r'\.read\(\)', 'file_read_without_check'),
        (r'\.write\(', 'file_write_without_check')
    ]
    
    files_to_check = [
        Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py",
        Path(__file__).parent.parent / "storage" / "live_store.py",
        Path(__file__).parent.parent / "config" / "strategy.yaml"
    ]
    
    file_issues_found = 0
    
    for file_path in files_to_check:
        if file_path.exists() and file_path.suffix == '.py':
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern, issue_type in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # Check if there's proper error handling nearby
                    for match in matches:
                        if 'try:' not in content or 'FileNotFoundError' not in content:
                            issues.append({
                                'type': issue_type,
                                'file': file_path.name,
                                'pattern': match if isinstance(match, str) else str(match),
                                'severity': 'MEDIUM',
                                'description': f"File access in {file_path.name} may lack proper error handling"
                            })
                            file_issues_found += 1
                            break
    
    logger.info(f"   Found {file_issues_found} potential file access issues")
    return issues

def check_dictionary_access_issues():
    """Check for dictionary/attribute access without validation."""
    issues = []
    
    dashboard_file = Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py"
    
    if dashboard_file.exists():
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for direct dictionary access without .get()
        dict_patterns = [
            (r'(\w+)\[[\'"]\w+[\'\"]\]', 'direct_dict_access'),
            (r'(\w+)\.(\w+)\.(\w+)', 'chained_attribute_access'),
            (r'request\.json\(\)', 'request_json_without_validation')
        ]
        
        dict_issues_found = 0
        
        for pattern, issue_type in dict_patterns:
            matches = re.findall(pattern, content)
            if matches:
                # Sample a few matches to avoid spam
                for match in matches[:3]:
                    issues.append({
                        'type': issue_type,
                        'pattern': str(match),
                        'severity': 'LOW',
                        'description': f"Potential unsafe {issue_type.replace('_', ' ')}: {match}"
                    })
                    dict_issues_found += 1
        
        logger.info(f"   Found {dict_issues_found} potential dictionary/attribute access issues")
    
    return issues

def check_async_issues():
    """Check for async/await issues."""
    issues = []
    
    dashboard_file = Path(__file__).parent.parent / "ui" / "ai_strategy_tuner.py"
    
    if dashboard_file.exists():
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for missing await keywords
        async_patterns = [
            (r'async def \w+.*?:\s*\n.*?(\w+\.\w+\()', 'missing_await_maybe'),
            (r'fetch\(', 'fetch_without_await_check')
        ]
        
        async_issues_found = 0
        
        for pattern, issue_type in async_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                async_issues_found += len(matches)
        
        logger.info(f"   Found {async_issues_found} potential async/await issues")
    
    return issues

def check_network_issues():
    """Check for network connection issues."""
    issues = []
    
    # Check exchange client files
    exchange_files = [
        Path(__file__).parent.parent / "exchange" / "ccxt_htx_client.py",
        Path(__file__).parent.parent / "feeds" / "htx_data_producer.py"
    ]
    
    network_issues_found = 0
    
    for file_path in exchange_files:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for network calls without timeout/retry
            network_patterns = [
                'requests.get(',
                'requests.post(',
                'aiohttp.ClientSession',
                'websocket.connect'
            ]
            
            for pattern in network_patterns:
                if pattern in content:
                    if 'timeout' not in content or 'retry' not in content:
                        issues.append({
                            'type': 'network_no_timeout_retry',
                            'file': file_path.name,
                            'pattern': pattern,
                            'severity': 'MEDIUM',
                            'description': f"Network call in {file_path.name} may lack timeout/retry logic"
                        })
                        network_issues_found += 1
                        break
    
    logger.info(f"   Found {network_issues_found} potential network connection issues")
    return issues

def check_config_issues():
    """Check for configuration access issues."""
    issues = []
    
    config_file = Path(__file__).parent.parent / "config" / "strategy.yaml"
    
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check for missing required config keys
            required_keys = ['symbol', 'exchange', 'trading', 'risk_management']
            missing_keys = [key for key in required_keys if key not in config]
            
            for key in missing_keys:
                issues.append({
                    'type': 'missing_config_key',
                    'key': key,
                    'severity': 'HIGH',
                    'description': f"Required configuration key '{key}' missing from strategy.yaml"
                })
            
            logger.info(f"   Found {len(missing_keys)} missing configuration keys")
            
        except Exception as e:
            issues.append({
                'type': 'config_load_error',
                'error': str(e),
                'severity': 'HIGH',
                'description': f"Error loading configuration: {e}"
            })
            logger.warning(f"   Configuration loading error: {e}")
    
    return issues

async def test_comprehensive_system_audit():
    """Main test function for comprehensive system audit."""
    try:
        logger.info("🧪 Testing Comprehensive System Audit - Phase 9.14")
        logger.info("="*60)
        
        # Analyze all potential issues
        all_issues = analyze_potential_issues()
        
        # Categorize issues by severity
        high_severity = [i for i in all_issues if i['severity'] == 'HIGH']
        medium_severity = [i for i in all_issues if i['severity'] == 'MEDIUM']
        low_severity = [i for i in all_issues if i['severity'] == 'LOW']
        
        # Report findings
        logger.info("\n" + "="*60)
        logger.info("📋 COMPREHENSIVE SYSTEM AUDIT RESULTS")
        logger.info("="*60)
        
        logger.info(f"🔍 Total Issues Found: {len(all_issues)}")
        logger.info(f"   🔴 HIGH severity: {len(high_severity)} issues")
        logger.info(f"   🟡 MEDIUM severity: {len(medium_severity)} issues")
        logger.info(f"   🟢 LOW severity: {len(low_severity)} issues")
        
        # Show top priority issues
        if high_severity:
            logger.info("\n🔴 HIGH PRIORITY ISSUES:")
            for i, issue in enumerate(high_severity[:5], 1):
                logger.info(f"   {i}. {issue['description']}")
        
        if medium_severity:
            logger.info("\n🟡 MEDIUM PRIORITY ISSUES:")
            for i, issue in enumerate(medium_severity[:5], 1):
                logger.info(f"   {i}. {issue['description']}")
        
        # Recommendations
        logger.info("\n🔧 RECOMMENDED NEXT STEPS:")
        
        if high_severity:
            logger.info("   1. 🔴 Fix HIGH severity issues immediately")
            logger.info("      - Configuration loading errors")
            logger.info("      - WebSocket connection validation")
            logger.info("      - Missing required config keys")
        
        if medium_severity:
            logger.info("   2. 🟡 Address MEDIUM severity issues")
            logger.info("      - Add error handling to API endpoints")
            logger.info("      - Implement network timeouts and retries")
            logger.info("      - Add file access validation")
        
        if low_severity:
            logger.info("   3. 🟢 Consider LOW severity improvements")
            logger.info("      - Use .get() for dictionary access")
            logger.info("      - Add validation for chained attribute access")
            logger.info("      - Review async/await patterns")
        
        logger.info("\n🎯 AREAS THAT NEED ATTENTION:")
        issue_types = {}
        for issue in all_issues:
            issue_type = issue['type']
            if issue_type not in issue_types:
                issue_types[issue_type] = 0
            issue_types[issue_type] += 1
        
        for issue_type, count in sorted(issue_types.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"   📊 {issue_type.replace('_', ' ').title()}: {count} occurrences")
        
        # Overall assessment
        if len(all_issues) == 0:
            logger.info("\n🎉 EXCELLENT! No major issues found!")
        elif len(high_severity) == 0:
            logger.info("\n✅ GOOD! No critical issues, only minor improvements needed")
        elif len(high_severity) <= 3:
            logger.info("\n⚠️ ATTENTION NEEDED! Few critical issues to fix")
        else:
            logger.info("\n🚨 ACTION REQUIRED! Multiple critical issues need immediate attention")
        
        logger.info("\n✅ COMPREHENSIVE SYSTEM AUDIT COMPLETE!")
        
        return all_issues
        
    except Exception as e:
        logger.error(f"❌ Audit failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_comprehensive_system_audit())
