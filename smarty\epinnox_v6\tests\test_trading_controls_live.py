#!/usr/bin/env python3
"""
Test Trading Controls Live Updates - Phase 9.6
Tests that dashboard trading controls properly update live trading system
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

async def test_trading_controls_live_updates():
    """Test that trading controls properly update the live trading system."""
    try:
        logger.info("🧪 Testing Trading Controls Live Updates - Phase 9.6")
        
        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        dashboard = AIStrategyTunerDashboard(config, data_store, execution_controller)
        
        # Test 1: Check initial max_open_positions
        logger.info("🎯 Test 1: Checking initial max_open_positions...")
        initial_max_positions = execution_controller.max_open_positions
        logger.info(f"   Initial max_open_positions: {initial_max_positions}")
        
        # Test 2: Update max_open_positions via dashboard API
        logger.info("🎯 Test 2: Updating max_open_positions via dashboard...")
        new_max_positions = 3
        
        # Simulate dashboard settings update
        test_settings = {
            'max-open-positions': new_max_positions
        }
        
        await dashboard._apply_live_settings(test_settings)
        
        # Verify the update
        updated_max_positions = execution_controller.max_open_positions
        logger.info(f"   Updated max_open_positions: {updated_max_positions}")
        
        if updated_max_positions == new_max_positions:
            logger.info("✅ Max open positions successfully updated!")
        else:
            logger.error(f"❌ Max open positions update failed: expected {new_max_positions}, got {updated_max_positions}")
        
        # Test 3: Test position limit enforcement
        logger.info("🎯 Test 3: Testing position limit enforcement...")
        
        # Create mock trading decisions to test position limits
        test_decisions = []
        for i in range(5):  # Try to create 5 positions (should be limited to 3)
            test_decisions.append({
                'symbol': 'DOGE/USDT:USDT',
                'action': 'LONG',
                'confidence': 0.8,
                'conviction_score': 8,
                'reasoning': f'Test position {i+1} for limit enforcement',
                'market_regime': 'normal',
                'timestamp': time.time()
            })
        
        # Mock market data
        market_data = {
            'last_price': 0.40,
            'volume': 1000000,
            'volatility': 0.02,
            'timestamp': time.time()
        }
        
        executed_positions = 0
        blocked_positions = 0
        
        for i, decision in enumerate(test_decisions):
            logger.info(f"   Attempting to execute position {i+1}...")
            
            # Try to execute the decision
            result = await execution_controller.process_trading_decision(decision, market_data)
            
            if result and result.execution:
                executed_positions += 1
                logger.info(f"   ✅ Position {i+1} executed successfully")
            else:
                blocked_positions += 1
                logger.info(f"   🚫 Position {i+1} blocked (likely by position limit)")
        
        logger.info(f"📊 Position Execution Results:")
        logger.info(f"   Executed: {executed_positions}")
        logger.info(f"   Blocked: {blocked_positions}")
        logger.info(f"   Max allowed: {new_max_positions}")
        
        # Test 4: Update other trading controls
        logger.info("🎯 Test 4: Testing other trading control updates...")
        
        other_settings = {
            'position-size-usd': 2.0,
            'max-margin-pct': 15.0,
            'stop-loss-pct': 1.0,
            'take-profit-pct': 0.5
        }
        
        await dashboard._apply_live_settings(other_settings)
        logger.info("✅ Other trading controls updated successfully")
        
        # Test 5: Verify settings persistence
        logger.info("🎯 Test 5: Verifying settings persistence...")
        
        # Check if execution controller still has updated values
        final_max_positions = execution_controller.max_open_positions
        logger.info(f"   Final max_open_positions: {final_max_positions}")
        
        if final_max_positions == new_max_positions:
            logger.info("✅ Settings persistence verified!")
        else:
            logger.error(f"❌ Settings not persisted: expected {new_max_positions}, got {final_max_positions}")
        
        logger.info("✅ Trading Controls Live Updates Test Complete!")
        logger.info("\n🎯 Summary:")
        logger.info(f"   ✅ Max open positions updated: {initial_max_positions} → {updated_max_positions}")
        logger.info(f"   ✅ Position limit enforcement: {executed_positions} executed, {blocked_positions} blocked")
        logger.info(f"   ✅ Other controls updated successfully")
        logger.info(f"   ✅ Settings persistence verified")
        
        logger.info("\n🚀 Next Steps:")
        logger.info("   1. Run the complete system and test dashboard controls")
        logger.info("   2. Change max open positions in dashboard and verify immediate effect")
        logger.info("   3. Monitor position limits in real trading")
        
    except Exception as e:
        logger.error(f"❌ Error in trading controls test: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_trading_controls_live_updates())
