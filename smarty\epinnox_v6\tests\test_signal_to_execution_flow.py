#!/usr/bin/env python3
"""
Test Signal to Execution Flow - Phase 9.5
Tests the complete signal generation to trade execution pipeline
"""

import asyncio
import logging
import time
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from models.smart_strategy import SmartStrategy
from feeds.trade_parser import MarketFeatures

async def test_signal_to_execution_flow():
    """Test the complete signal to execution flow."""
    try:
        logger.info("🧪 Testing Signal to Execution Flow - Phase 9.5")

        # Load configuration
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "config" / "strategy.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize components
        logger.info("📊 Initializing components...")
        data_store = LiveDataStore(config)
        execution_controller = ExecutionController(config)
        smart_strategy = SmartStrategy(config, data_store)

        # Connect components
        logger.info("🔗 Connecting signal flow...")
        execution_controller.set_smart_strategy(smart_strategy)

        # Create mock market features to trigger signal generation
        logger.info("📈 Creating mock market features...")
        mock_features = MarketFeatures(
            symbol="DOGE/USDT:USDT",
            timestamp=int(time.time() * 1000),
            # Required price features
            last_price=0.40,
            price_change_1m=0.005,  # 0.5% change
            price_velocity=0.001,
            # Required volume features
            volume_1m=1000000,
            buy_volume_ratio=0.65,  # Strong buy pressure
            volume_delta=100000,
            # Required order flow features
            order_flow_imbalance=0.4,  # Strong buy imbalance
            trade_intensity=8.0,
            avg_trade_size=1000,
            # Technical indicators
            rsi=25.0,  # Oversold
            vwap=0.395,  # Below current price
            volatility=0.02
        )

        # Test 1: Process features through strategy (should generate signal)
        logger.info("🎯 Test 1: Processing features through Smart Strategy...")
        signal = await smart_strategy.process_features(mock_features)

        if signal:
            logger.info(f"✅ Signal generated: {signal.action} {signal.symbol} "
                       f"(confidence: {signal.confidence:.2%})")
        else:
            logger.info("ℹ️ No signal generated (may be in cooldown or below threshold)")

        # Test 2: Manually trigger signal callback
        logger.info("🎯 Test 2: Manually triggering signal callback...")
        test_signal_data = {
            'symbol': 'DOGE/USDT:USDT',
            'action': 'LONG',
            'confidence': 0.85,
            'score': 1.0,
            'reasoning': 'Test signal for execution flow validation',
            'model_contributions': {'test': 0.85},
            'timestamp': time.time(),
            'price': 0.40
        }

        # Send signal directly to execution controller
        await execution_controller.process_signal_for_execution(test_signal_data)

        # Test 3: Check execution stats
        logger.info("🎯 Test 3: Checking execution statistics...")
        execution_status = execution_controller.get_execution_intelligence_status()

        logger.info(f"📊 Execution Stats:")
        logger.info(f"   Total Decisions: {execution_status.get('execution_stats', {}).get('total_decisions', 0)}")
        logger.info(f"   Executed Decisions: {execution_status.get('execution_stats', {}).get('executed_decisions', 0)}")
        logger.info(f"   Auto Execute: {execution_status.get('execution_config', {}).get('auto_execute', False)}")

        # Test 4: Check data store for signals
        logger.info("🎯 Test 4: Checking data store for stored signals...")
        stored_signals = data_store.get_signals('DOGE/USDT:USDT', limit=5)
        logger.info(f"📊 Stored Signals: {len(stored_signals)} signals found")

        for i, signal in enumerate(stored_signals[-3:], 1):  # Show last 3 signals
            logger.info(f"   Signal {i}: {signal.get('action', 'UNKNOWN')} "
                       f"(confidence: {signal.get('confidence', 0):.2%}) "
                       f"at {signal.get('formatted_time', 'unknown')}")

        logger.info("✅ Signal to Execution Flow Test Complete!")
        logger.info("\n🎯 Next Steps:")
        logger.info("   1. Run the complete system: python run_complete_onnyx_system.py")
        logger.info("   2. Monitor dashboard for signal generation and execution")
        logger.info("   3. Check logs for signal flow from generation to execution")

    except Exception as e:
        logger.error(f"❌ Error in signal to execution flow test: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_signal_to_execution_flow())
