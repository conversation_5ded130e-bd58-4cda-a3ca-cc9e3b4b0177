#!/usr/bin/env python3
"""
Diagnose Money Circle Startup Issues
"""

import sys
import traceback
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test all imports."""
    try:
        logger.info("Testing imports...")
        
        # Test basic imports
        import asyncio
        import aiohttp
        logger.info("✅ Basic imports OK")
        
        # Test app import
        from app import MoneyCircleApp
        logger.info("✅ App import OK")
        
        # Test app creation
        app = MoneyCircleApp('development')
        logger.info("✅ App creation OK")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

async def test_app_creation():
    """Test app creation."""
    try:
        logger.info("Testing app creation...")
        
        from app import MoneyCircleApp
        app = MoneyCircleApp('development')
        
        # Test web app creation
        web_app = await app.create_app()
        logger.info("✅ Web app creation OK")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ App creation error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function."""
    logger.info("🔍 Diagnosing Money Circle startup issues...")
    
    # Test imports
    if not test_imports():
        return 1
    
    # Test app creation
    try:
        import asyncio
        if not asyncio.run(test_app_creation()):
            return 1
    except Exception as e:
        logger.error(f"❌ Async test error: {e}")
        traceback.print_exc()
        return 1
    
    logger.info("✅ All diagnostic tests passed!")
    logger.info("The issue might be with server startup, not app creation.")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
