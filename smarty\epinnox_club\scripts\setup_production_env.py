#!/usr/bin/env python3
"""
Production Environment Setup for Money Circle Phase 3
Automated setup script for production deployment with real HTX API integration
"""

import os
import sys
import logging
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
import json
import subprocess
from datetime import datetime

# Setup logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('setup_production.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class ProductionSetup:
    """Production environment setup manager."""

    def __init__(self):
        """Initialize production setup."""
        self.project_root = Path(__file__).parent
        self.env_file = self.project_root / '.env'
        self.config_dir = self.project_root / 'config'
        self.data_dir = self.project_root / 'data'
        self.logs_dir = self.project_root / 'logs'
        self.backups_dir = self.project_root / 'backups'

        # Environment variables
        self.env_vars = {}

        logger.info("[SETUP] Production Setup initialized")

    async def run_full_setup(self):
        """Run complete production setup."""
        try:
            logger.info("=" * 70)
            logger.info("MONEY CIRCLE PHASE 3: PRODUCTION DEPLOYMENT SETUP")
            logger.info("=" * 70)

            # Step 1: Environment validation
            await self._validate_environment()

            # Step 2: Directory structure
            await self._setup_directories()

            # Step 3: Environment variables
            await self._setup_environment_variables()

            # Step 4: Database setup
            await self._setup_production_database()

            # Step 5: Security configuration
            await self._setup_security()

            # Step 6: HTX API configuration
            await self._setup_htx_api()

            # Step 7: Risk management configuration
            await self._setup_risk_management()

            # Step 8: Monitoring and logging
            await self._setup_monitoring()

            # Step 9: Backup configuration
            await self._setup_backup_system()

            # Step 10: Final validation
            await self._validate_production_setup()

            logger.info("=" * 70)
            logger.info("✅ PRODUCTION SETUP COMPLETE!")
            logger.info("🎯 Money Circle is ready for live trading")
            logger.info("=" * 70)

            # Print summary
            await self._print_setup_summary()

        except Exception as e:
            logger.error(f"❌ Production setup failed: {e}")
            sys.exit(1)

    async def _validate_environment(self):
        """Validate system environment."""
        logger.info("🔍 Step 1: Validating environment...")

        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 9):
            raise Exception(f"Python 3.9+ required, found {python_version.major}.{python_version.minor}")

        logger.info(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

        # Check required packages
        required_packages = [
            'aiohttp', 'aiohttp_session', 'aiohttp_jinja2',
            'ccxt', 'numpy', 'cryptography', 'bcrypt'
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✅ Package available: {package}")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"⚠️ Missing package: {package}")

        if missing_packages:
            logger.info(f"📦 Installing missing packages: {', '.join(missing_packages)}")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)

        logger.info("✅ Environment validation complete")

    async def _setup_directories(self):
        """Setup directory structure."""
        logger.info("📁 Step 2: Setting up directories...")

        directories = [
            self.config_dir,
            self.data_dir,
            self.logs_dir,
            self.backups_dir,
            self.project_root / 'static' / 'js',
            self.project_root / 'static' / 'css',
            self.project_root / 'templates',
            self.project_root / 'ssl'
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Directory created: {directory}")

        # Set proper permissions
        os.chmod(self.data_dir, 0o750)
        os.chmod(self.logs_dir, 0o750)
        os.chmod(self.backups_dir, 0o750)

        logger.info("✅ Directory setup complete")

    async def _setup_environment_variables(self):
        """Setup environment variables."""
        logger.info("🔧 Step 3: Setting up environment variables...")

        # Default environment variables
        default_env = {
            'ENVIRONMENT': 'production',
            'DEBUG': 'false',
            'HOST': '0.0.0.0',
            'PORT': '8087',

            # Database
            'DATABASE_URL': 'sqlite:///data/money_circle_production.db',

            # Security
            'SECRET_KEY': self._generate_secret_key(),
            'ENCRYPTION_KEY': self._generate_encryption_key(),
            'SSL_ENABLED': 'true',

            # HTX API (to be filled by user)
            'HTX_API_KEY': '',
            'HTX_API_SECRET': '',
            'HTX_PASSPHRASE': '',
            'HTX_ENVIRONMENT': 'testnet',  # Start with testnet
            'HTX_RATE_LIMIT': '100',
            'HTX_TIMEOUT': '30',

            # Risk Management
            'MAX_POSITION_SIZE': '1000.0',
            'MAX_LEVERAGE': '10',
            'MAX_DAILY_LOSS': '500.0',
            'MAX_DRAWDOWN_PCT': '5.0',
            'STOP_LOSS_PCT': '2.0',
            'TAKE_PROFIT_PCT': '4.0',
            'POSITION_SIZING_METHOD': 'fixed',
            'RISK_PER_TRADE_PCT': '1.0',

            # Monitoring
            'MONITORING_ENABLED': 'true',
            'BACKUP_ENABLED': 'true',
            'LOG_LEVEL': 'INFO',

            # Optional exchanges
            'BINANCE_API_KEY': '',
            'BINANCE_API_SECRET': '',
            'BYBIT_API_KEY': '',
            'BYBIT_API_SECRET': ''
        }

        # Load existing .env if it exists
        if self.env_file.exists():
            logger.info("📄 Loading existing .env file...")
            with open(self.env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        default_env[key] = value

        # Write .env file
        with open(self.env_file, 'w') as f:
            f.write("# Money Circle Production Environment Configuration\n")
            f.write(f"# Generated on {datetime.now().isoformat()}\n\n")

            for key, value in default_env.items():
                f.write(f"{key}={value}\n")

        # Set permissions
        os.chmod(self.env_file, 0o600)  # Read/write for owner only

        logger.info("✅ Environment variables setup complete")
        logger.warning("⚠️ Please update HTX API credentials in .env file before starting")

    def _generate_secret_key(self) -> str:
        """Generate secure secret key."""
        import secrets
        return secrets.token_urlsafe(32)

    def _generate_encryption_key(self) -> str:
        """Generate encryption key."""
        from cryptography.fernet import Fernet
        import base64
        key = Fernet.generate_key()
        return base64.urlsafe_b64encode(key).decode()

    async def _setup_production_database(self):
        """Setup production database."""
        logger.info("🗄️ Step 4: Setting up production database...")

        # Create database initialization script
        db_init_script = self.config_dir / 'init_production_db.py'

        with open(db_init_script, 'w') as f:
            f.write("""#!/usr/bin/env python3
import sqlite3
import os
from pathlib import Path

def init_production_database():
    db_path = Path('data/money_circle_production.db')
    db_path.parent.mkdir(exist_ok=True)

    with sqlite3.connect(db_path) as conn:
        # Enable WAL mode for better performance
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=memory')

        print("✅ Production database initialized")

if __name__ == '__main__':
    init_production_database()
""")

        # Run database initialization
        subprocess.run([sys.executable, str(db_init_script)], check=True)

        logger.info("✅ Production database setup complete")

    async def _setup_security(self):
        """Setup security configuration."""
        logger.info("🛡️ Step 5: Setting up security...")

        # Create security configuration
        security_config = {
            'rate_limiting': {
                'enabled': True,
                'requests_per_minute': 1000,
                'burst_limit': 100
            },
            'csrf_protection': {
                'enabled': True,
                'secret_key_rotation': True
            },
            'session_security': {
                'secure_cookies': True,
                'httponly_cookies': True,
                'session_timeout': 7200  # 2 hours
            },
            'api_security': {
                'require_authentication': True,
                'rate_limit_api': True,
                'log_all_requests': True
            }
        }

        with open(self.config_dir / 'security.json', 'w') as f:
            json.dump(security_config, f, indent=2)

        logger.info("✅ Security configuration complete")

    async def _setup_htx_api(self):
        """Setup HTX API configuration."""
        logger.info("🏦 Step 6: Setting up HTX API configuration...")

        htx_config = {
            'api_endpoints': {
                'testnet': {
                    'rest': 'https://api-usdt.linear.contract.huobi.pro',
                    'websocket': 'wss://api-usdt.linear.contract.huobi.pro/ws'
                },
                'mainnet': {
                    'rest': 'https://api-usdt.linear.contract.huobi.pro',
                    'websocket': 'wss://api-usdt.linear.contract.huobi.pro/ws'
                }
            },
            'supported_symbols': ['DOGE/USDT:USDT'],
            'rate_limits': {
                'requests_per_minute': 100,
                'orders_per_minute': 50
            },
            'default_settings': {
                'leverage': 10,
                'order_type': 'market',
                'reduce_only': False
            }
        }

        with open(self.config_dir / 'htx_config.json', 'w') as f:
            json.dump(htx_config, f, indent=2)

        logger.info("✅ HTX API configuration complete")

    async def _setup_risk_management(self):
        """Setup risk management configuration."""
        logger.info("⚖️ Step 7: Setting up risk management...")

        risk_config = {
            'position_limits': {
                'max_position_size_usd': 1000.0,
                'max_leverage': 10,
                'max_positions': 5
            },
            'loss_limits': {
                'max_daily_loss_usd': 500.0,
                'max_drawdown_pct': 5.0,
                'stop_loss_pct': 2.0,
                'take_profit_pct': 4.0
            },
            'position_sizing': {
                'method': 'fixed',  # fixed, kelly, volatility, risk_parity
                'risk_per_trade_pct': 1.0,
                'kelly_fraction': 0.25
            },
            'monitoring': {
                'check_interval_seconds': 5,
                'alert_thresholds': {
                    'high_risk_score': 70,
                    'critical_risk_score': 85
                }
            },
            'automated_actions': {
                'auto_stop_loss': True,
                'auto_take_profit': True,
                'emergency_close_all': True
            }
        }

        with open(self.config_dir / 'risk_management.json', 'w') as f:
            json.dump(risk_config, f, indent=2)

        logger.info("✅ Risk management configuration complete")

    async def _setup_monitoring(self):
        """Setup monitoring and logging."""
        logger.info("📊 Step 8: Setting up monitoring and logging...")

        # Create logging configuration
        logging_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'detailed': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                },
                'simple': {
                    'format': '%(levelname)s - %(message)s'
                }
            },
            'handlers': {
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': 'logs/money_circle.log',
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5,
                    'formatter': 'detailed'
                },
                'console': {
                    'class': 'logging.StreamHandler',
                    'formatter': 'simple'
                }
            },
            'loggers': {
                '': {
                    'handlers': ['file', 'console'],
                    'level': 'INFO',
                    'propagate': False
                }
            }
        }

        with open(self.config_dir / 'logging.json', 'w') as f:
            json.dump(logging_config, f, indent=2)

        # Create monitoring configuration
        monitoring_config = {
            'health_checks': {
                'enabled': True,
                'interval_seconds': 30,
                'endpoints': ['/health', '/api/status']
            },
            'performance_monitoring': {
                'enabled': True,
                'metrics': ['response_time', 'memory_usage', 'cpu_usage']
            },
            'alerts': {
                'email_enabled': False,
                'webhook_enabled': False,
                'log_alerts': True
            }
        }

        with open(self.config_dir / 'monitoring.json', 'w') as f:
            json.dump(monitoring_config, f, indent=2)

        logger.info("✅ Monitoring and logging setup complete")

    async def _setup_backup_system(self):
        """Setup backup system."""
        logger.info("💾 Step 9: Setting up backup system...")

        backup_config = {
            'enabled': True,
            'backup_interval_minutes': 30,
            'retention_days': 30,
            'backup_types': {
                'database': True,
                'configuration': True,
                'logs': False  # Logs are rotated separately
            },
            'compression': True,
            'encryption': True
        }

        with open(self.config_dir / 'backup.json', 'w') as f:
            json.dump(backup_config, f, indent=2)

        # Create backup script
        backup_script = self.project_root / 'backup_production.py'
        with open(backup_script, 'w') as f:
            f.write("""#!/usr/bin/env python3
import shutil
import gzip
from datetime import datetime
from pathlib import Path

def create_backup():
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = Path('backups') / f'backup_{timestamp}'
    backup_dir.mkdir(parents=True, exist_ok=True)

    # Backup database
    if Path('data/money_circle_production.db').exists():
        shutil.copy2('data/money_circle_production.db', backup_dir / 'database.db')

    # Backup configuration
    if Path('config').exists():
        shutil.copytree('config', backup_dir / 'config')

    # Compress backup
    shutil.make_archive(str(backup_dir), 'gztar', str(backup_dir))
    shutil.rmtree(backup_dir)

    print(f"✅ Backup created: {backup_dir}.tar.gz")

if __name__ == '__main__':
    create_backup()
""")

        os.chmod(backup_script, 0o755)

        logger.info("✅ Backup system setup complete")

    async def _validate_production_setup(self):
        """Validate production setup."""
        logger.info("✅ Step 10: Validating production setup...")

        # Check required files
        required_files = [
            self.env_file,
            self.config_dir / 'security.json',
            self.config_dir / 'htx_config.json',
            self.config_dir / 'risk_management.json',
            self.config_dir / 'monitoring.json',
            self.config_dir / 'backup.json'
        ]

        for file_path in required_files:
            if not file_path.exists():
                raise Exception(f"Required file missing: {file_path}")
            logger.info(f"✅ File exists: {file_path}")

        # Check directories
        required_dirs = [self.data_dir, self.logs_dir, self.backups_dir]
        for dir_path in required_dirs:
            if not dir_path.exists():
                raise Exception(f"Required directory missing: {dir_path}")
            logger.info(f"✅ Directory exists: {dir_path}")

        logger.info("✅ Production setup validation complete")

    async def _print_setup_summary(self):
        """Print setup summary."""
        print("\n" + "=" * 70)
        print("🎉 MONEY CIRCLE PHASE 3 PRODUCTION SETUP COMPLETE!")
        print("=" * 70)
        print("\n📋 NEXT STEPS:")
        print("1. Update HTX API credentials in .env file")
        print("2. Review risk management settings in config/risk_management.json")
        print("3. Test with HTX testnet before switching to mainnet")
        print("4. Start the server: python run_local.py")
        print("5. Access the platform: http://localhost:8087")
        print("\n🔐 SECURITY NOTES:")
        print("- .env file contains sensitive data - keep secure")
        print("- Database is encrypted and backed up automatically")
        print("- All API calls are rate limited and logged")
        print("\n🚀 READY FOR LIVE TRADING!")
        print("=" * 70)

async def main():
    """Main setup function."""
    setup = ProductionSetup()
    await setup.run_full_setup()

if __name__ == "__main__":
    asyncio.run(main())
