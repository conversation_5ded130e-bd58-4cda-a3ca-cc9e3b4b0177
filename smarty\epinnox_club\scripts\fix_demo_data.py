#!/usr/bin/env python3
"""
Fix Demo Data Population for Money Circle
Ensures all demo users have realistic trading data, portfolios, and exchange connections.
"""

import sqlite3
import random
import secrets
from datetime import datetime, timedelta
from decimal import Decimal

class DemoDataFixer:
    """Fix and populate demo data for Money Circle."""

    def __init__(self, db_path: str = "data/money_circle.db"):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.execute("PRAGMA foreign_keys = ON")

        # Demo data configuration
        self.exchanges = ['HTX', 'Binance', 'Bybit']
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        self.base_prices = {
            'BTCUSDT': 50000,
            'ETHUSDT': 3000,
            'ADAUSDT': 0.5,
            'SOLUSDT': 100,
            'DOTUSDT': 8
        }

    def get_demo_users(self):
        """Get all demo users from database."""
        cursor = self.conn.execute("""
            SELECT id, username, role FROM users
            WHERE username IN ('alex_trader', 'sarah_crypto', 'mike_scalper', 'emma_hodler', 'epinnox')
            ORDER BY username
        """)
        return cursor.fetchall()

    def clear_existing_demo_data(self):
        """Clear existing demo data for fresh start."""
        print("🧹 Clearing existing demo data...")

        # Get demo user IDs
        demo_users = self.get_demo_users()
        user_ids = [user[0] for user in demo_users]

        if not user_ids:
            print("❌ No demo users found!")
            return False

        # Clear existing data
        placeholders = ','.join(['?' for _ in user_ids])

        tables_to_clear = [
            'user_trades',
            'user_positions',
            'user_exchanges',
            'trading_performance',
            'member_profiles'
        ]

        for table in tables_to_clear:
            try:
                self.conn.execute(f"DELETE FROM {table} WHERE user_id IN ({placeholders})", user_ids)
                print(f"   Cleared {table}")
            except Exception as e:
                print(f"   Warning: Could not clear {table}: {e}")

        self.conn.commit()
        print("✅ Demo data cleared")
        return True

    def create_exchange_connections(self, users):
        """Create exchange connections for demo users."""
        print("🏦 Creating exchange connections...")

        for user_id, username, role in users:
            # Each user gets 2-3 exchange connections
            num_exchanges = random.randint(2, 3)
            user_exchanges = random.sample(self.exchanges, num_exchanges)

            for exchange in user_exchanges:
                # Generate fake encrypted credentials
                fake_api_key = f"demo_{exchange.lower()}_{secrets.token_hex(8)}"
                fake_secret = secrets.token_hex(16)

                self.conn.execute("""
                    INSERT INTO user_exchanges (
                        user_id, exchange_name, api_key_encrypted, secret_key_encrypted,
                        is_active, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    user_id,
                    exchange,
                    fake_api_key.encode(),
                    fake_secret.encode(),
                    True,
                    (datetime.now() - timedelta(days=random.randint(10, 60))).isoformat()
                ))

                print(f"   Connected {username} to {exchange}")

        self.conn.commit()
        print("✅ Exchange connections created")

    def create_trading_history(self, users):
        """Create realistic trading history for demo users."""
        print("📊 Creating trading history...")

        for user_id, username, role in users:
            # Create 50-100 trades per user over last 3 months
            num_trades = random.randint(50, 100)

            for i in range(num_trades):
                symbol = random.choice(self.symbols)
                exchange = random.choice(self.exchanges)
                side = random.choice(['buy', 'sell'])

                # Generate realistic trade data
                base_price = self.base_prices[symbol]
                price_variation = random.uniform(0.95, 1.05)
                price = base_price * price_variation

                size = random.uniform(0.01, 1.0) if symbol == 'BTCUSDT' else random.uniform(1, 100)
                fee = (price * size) * 0.001  # 0.1% fee

                trade_time = datetime.now() - timedelta(
                    days=random.randint(0, 90),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )

                self.conn.execute("""
                    INSERT INTO user_trades (
                        user_id, exchange_name, symbol, side, size, price, fee,
                        order_type, strategy_name, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id, exchange, symbol, side, size, price, fee,
                    random.choice(['market', 'limit']),
                    f"Strategy_{random.randint(1, 5)}",
                    trade_time.isoformat()
                ))

            print(f"   Created {num_trades} trades for {username}")

        self.conn.commit()
        print("✅ Trading history created")

    def create_open_positions(self, users):
        """Create open positions for demo users."""
        print("📈 Creating open positions...")

        for user_id, username, role in users:
            # Create 3-8 open positions per user
            num_positions = random.randint(3, 8)

            for i in range(num_positions):
                symbol = random.choice(self.symbols)
                exchange = random.choice(self.exchanges)
                side = random.choice(['long', 'short'])

                # Generate realistic position data
                base_price = self.base_prices[symbol]
                entry_price = base_price * random.uniform(0.98, 1.02)
                current_price = base_price * random.uniform(0.95, 1.05)

                size = random.uniform(0.01, 0.5) if symbol == 'BTCUSDT' else random.uniform(1, 50)
                leverage = random.choice([1, 2, 3, 5, 10])

                # Calculate PnL
                if side == 'long':
                    pnl = (current_price - entry_price) * size
                else:
                    pnl = (entry_price - current_price) * size

                self.conn.execute("""
                    INSERT INTO user_positions (
                        user_id, exchange_name, symbol, side, size, entry_price,
                        current_price, pnl, leverage, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id, exchange, symbol, side, size, entry_price,
                    current_price, pnl, leverage, 'open',
                    (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat()
                ))

            print(f"   Created {num_positions} positions for {username}")

        self.conn.commit()
        print("✅ Open positions created")

    def create_member_profiles(self, users):
        """Create member profiles for demo users."""
        print("👤 Creating member profiles...")

        profiles = {
            'alex_trader': {
                'display_name': 'Alex Thompson',
                'bio': 'Quantitative trader with 8 years experience in algorithmic trading. Specializes in momentum strategies and risk management.',
                'trading_style': 'algorithmic',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'BTC, ETH, Futures'
            },
            'sarah_crypto': {
                'display_name': 'Sarah Chen',
                'bio': 'Cryptocurrency enthusiast and DeFi researcher. Focus on altcoin analysis and yield farming strategies.',
                'trading_style': 'swing',
                'risk_tolerance': 'aggressive',
                'preferred_assets': 'Altcoins, DeFi tokens'
            },
            'mike_scalper': {
                'display_name': 'Mike Rodriguez',
                'bio': 'High-frequency scalping specialist. Expert in market microstructure and order flow analysis.',
                'trading_style': 'scalping',
                'risk_tolerance': 'conservative',
                'preferred_assets': 'Major pairs, High volume'
            },
            'emma_hodler': {
                'display_name': 'Emma Wilson',
                'bio': 'Long-term investment strategist. Focuses on fundamental analysis and portfolio diversification.',
                'trading_style': 'position',
                'risk_tolerance': 'conservative',
                'preferred_assets': 'BTC, ETH, Blue chips'
            },
            'epinnox': {
                'display_name': 'Epinnox Admin',
                'bio': 'Platform administrator and trading system architect. Oversees club operations and strategy development.',
                'trading_style': 'systematic',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'All assets'
            }
        }

        for user_id, username, role in users:
            if username in profiles:
                profile = profiles[username]

                self.conn.execute("""
                    INSERT INTO member_profiles (
                        user_id, display_name, bio, trading_style, risk_tolerance,
                        preferred_assets, public_stats, joined_strategies, total_votes, reputation_score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id,
                    profile['display_name'],
                    profile['bio'],
                    profile['trading_style'],
                    profile['risk_tolerance'],
                    profile['preferred_assets'],
                    True,  # public_stats
                    random.randint(2, 8),  # joined_strategies
                    random.randint(10, 50),  # total_votes
                    random.uniform(75, 95)  # reputation_score
                ))

                print(f"   Created profile for {username}")

        self.conn.commit()
        print("✅ Member profiles created")

    def calculate_performance_metrics(self, users):
        """Calculate and store performance metrics for demo users."""
        print("📊 Calculating performance metrics...")

        for user_id, username, role in users:
            # Calculate metrics from trading history
            cursor = self.conn.execute("""
                SELECT
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN side = 'sell' THEN price * size ELSE -price * size END) as total_pnl,
                    AVG(price * size) as avg_trade_size
                FROM user_trades
                WHERE user_id = ?
            """, (user_id,))

            trade_stats = cursor.fetchone()
            total_trades = trade_stats[0] or 0
            total_pnl = trade_stats[1] or 0
            avg_trade_size = trade_stats[2] or 0

            # Calculate win rate (simplified)
            cursor = self.conn.execute("""
                SELECT COUNT(*) FROM user_trades
                WHERE user_id = ? AND side = 'sell'
                AND price > (SELECT AVG(price) FROM user_trades WHERE user_id = ? AND side = 'buy')
            """, (user_id, user_id))

            winning_trades = cursor.fetchone()[0] or 0
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # Generate realistic performance metrics
            daily_return = random.uniform(-2.5, 3.5)
            weekly_return = daily_return * 7 + random.uniform(-5, 5)
            monthly_return = weekly_return * 4 + random.uniform(-10, 15)
            total_return = monthly_return * 3 + random.uniform(-20, 30)

            max_drawdown = abs(random.uniform(5, 25))
            sharpe_ratio = random.uniform(0.5, 2.5)

            # Generate realistic portfolio value
            portfolio_value = random.uniform(10000, 100000)
            volatility = random.uniform(0.1, 0.4)

            self.conn.execute("""
                INSERT INTO trading_performance (
                    user_id, date, total_return, daily_return, portfolio_value,
                    win_rate, volatility, sharpe_ratio, trades_count, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, datetime.now().date().isoformat(), total_return, daily_return,
                portfolio_value, win_rate, volatility, sharpe_ratio, total_trades,
                datetime.now().isoformat()
            ))

            print(f"   Calculated metrics for {username}: {total_return:.1f}% return, {win_rate:.1f}% win rate")

        self.conn.commit()
        print("✅ Performance metrics calculated")

    def fix_all_demo_data(self):
        """Fix all demo data issues."""
        print("🔧 Money Circle Demo Data Fix")
        print("=" * 50)

        # Get demo users
        users = self.get_demo_users()
        if not users:
            print("❌ No demo users found! Please run authentication fix first.")
            return False

        print(f"Found {len(users)} demo users:")
        for user_id, username, role in users:
            print(f"  {username} (ID: {user_id}, Role: {role})")

        # Clear existing data
        if not self.clear_existing_demo_data():
            return False

        # Create all demo data
        self.create_exchange_connections(users)
        self.create_trading_history(users)
        self.create_open_positions(users)
        self.create_member_profiles(users)
        self.calculate_performance_metrics(users)

        print("\n" + "=" * 50)
        print("🎉 Demo Data Fix Complete!")
        print("=" * 50)

        # Verify data was created
        self.verify_demo_data(users)

        return True

    def verify_demo_data(self, users):
        """Verify demo data was created correctly."""
        print("\n🔍 Verifying demo data...")

        for user_id, username, role in users:
            # Check trades
            cursor = self.conn.execute("SELECT COUNT(*) FROM user_trades WHERE user_id = ?", (user_id,))
            trades_count = cursor.fetchone()[0]

            # Check positions
            cursor = self.conn.execute("SELECT COUNT(*) FROM user_positions WHERE user_id = ?", (user_id,))
            positions_count = cursor.fetchone()[0]

            # Check exchanges
            cursor = self.conn.execute("SELECT COUNT(*) FROM user_exchanges WHERE user_id = ?", (user_id,))
            exchanges_count = cursor.fetchone()[0]

            print(f"  {username}: {trades_count} trades, {positions_count} positions, {exchanges_count} exchanges")

        print("✅ Demo data verification complete")

def main():
    """Main function to fix demo data."""
    fixer = DemoDataFixer()
    success = fixer.fix_all_demo_data()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
