#!/usr/bin/env python3
"""
Autonomy Tracker
Tracks rolling performance across all symbols for autonomous decision-making
"""

import logging
import time
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceSnapshot:
    """Performance snapshot for autonomy analysis."""
    timestamp: float
    symbol: str
    win_rate: float
    avg_return: float
    volatility_adjusted_pnl: float
    trade_count: int
    confidence_accuracy: float
    model_performance: Dict[str, float]
    regime_duration: float
    regime_outcome: str

@dataclass
class AutonomyMetrics:
    """Comprehensive autonomy metrics."""
    overall_win_rate: float
    symbol_performance: Dict[str, float]
    model_reliability: Dict[str, float]
    regime_effectiveness: Dict[str, float]
    confidence_calibration: float
    recent_trend: str
    performance_drift: float
    adaptation_needed: bool
    critical_issues: List[str]

class AutonomyTracker:
    """
    Tracks rolling performance across all symbols for autonomous decision-making.
    Provides insights for model tuning and strategy adaptation.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.autonomy_config = config.get('autonomy', {})

        # Storage configuration
        self.data_dir = self.autonomy_config.get('data_dir', 'data/autonomy')
        self.snapshots_file = os.path.join(self.data_dir, 'performance_snapshots.json')
        self.metrics_file = os.path.join(self.data_dir, 'autonomy_metrics.json')

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Performance tracking
        self.performance_snapshots: List[PerformanceSnapshot] = []
        self.symbol_metrics: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.model_performance: Dict[str, List[float]] = defaultdict(list)
        self.regime_outcomes: Dict[str, List[str]] = defaultdict(list)

        # Rolling windows
        self.max_snapshots = self.autonomy_config.get('max_snapshots', 1000)
        self.analysis_window = self.autonomy_config.get('analysis_window_hours', 24)
        self.critical_threshold = self.autonomy_config.get('critical_threshold', 0.3)

        # Performance thresholds
        self.min_win_rate = self.autonomy_config.get('min_win_rate', 0.45)
        self.max_drawdown = self.autonomy_config.get('max_drawdown', 0.15)
        self.confidence_tolerance = self.autonomy_config.get('confidence_tolerance', 0.1)

        # Load existing data
        self._load_data()

        logger.info(f"Autonomy Tracker initialized with {len(self.performance_snapshots)} historical snapshots")

    def record_performance_snapshot(self, symbol: str, trade_data: Dict[str, Any],
                                  model_outputs: Dict[str, Any]) -> str:
        """Record a performance snapshot for autonomy analysis."""
        try:
            # Calculate performance metrics
            win_rate = self._calculate_symbol_win_rate(symbol)
            avg_return = self._calculate_avg_return(symbol)
            volatility_adjusted_pnl = self._calculate_volatility_adjusted_pnl(symbol, trade_data)
            trade_count = self._get_symbol_trade_count(symbol)
            confidence_accuracy = self._calculate_confidence_accuracy(symbol)

            # Analyze model performance
            model_performance = self._analyze_model_performance(model_outputs)

            # Determine regime context
            regime_duration = trade_data.get('regime_duration', 0.0)
            regime_outcome = trade_data.get('regime_outcome', 'unknown')

            # Create snapshot
            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                symbol=symbol,
                win_rate=win_rate,
                avg_return=avg_return,
                volatility_adjusted_pnl=volatility_adjusted_pnl,
                trade_count=trade_count,
                confidence_accuracy=confidence_accuracy,
                model_performance=model_performance,
                regime_duration=regime_duration,
                regime_outcome=regime_outcome
            )

            # Store snapshot
            self.performance_snapshots.append(snapshot)

            # Update symbol metrics
            self._update_symbol_metrics(symbol, snapshot)

            # Update model performance tracking
            self._update_model_tracking(model_outputs, trade_data.get('outcome', 'unknown'))

            # Maintain rolling window
            if len(self.performance_snapshots) > self.max_snapshots:
                self.performance_snapshots = self.performance_snapshots[-self.max_snapshots:]

            # Save to disk
            self._save_data()

            snapshot_id = f"{symbol}_{int(time.time())}"
            logger.debug(f"Performance snapshot recorded: {snapshot_id}")

            return snapshot_id

        except Exception as e:
            logger.error(f"Error recording performance snapshot: {e}")
            return ""

    def get_autonomy_metrics(self) -> AutonomyMetrics:
        """Get comprehensive autonomy metrics for decision-making."""
        try:
            # Get recent snapshots for analysis
            cutoff_time = time.time() - (self.analysis_window * 3600)
            recent_snapshots = [s for s in self.performance_snapshots if s.timestamp >= cutoff_time]

            if not recent_snapshots:
                return self._get_default_metrics()

            # Calculate overall metrics
            overall_win_rate = statistics.mean(s.win_rate for s in recent_snapshots)

            # Symbol performance analysis
            symbol_performance = {}
            for symbol in set(s.symbol for s in recent_snapshots):
                symbol_snapshots = [s for s in recent_snapshots if s.symbol == symbol]
                if symbol_snapshots:
                    symbol_performance[symbol] = statistics.mean(s.avg_return for s in symbol_snapshots)

            # Model reliability analysis
            model_reliability = self._calculate_model_reliability(recent_snapshots)

            # Regime effectiveness analysis
            regime_effectiveness = self._calculate_regime_effectiveness(recent_snapshots)

            # Confidence calibration
            confidence_calibration = self._calculate_confidence_calibration(recent_snapshots)

            # Performance trend analysis
            recent_trend = self._analyze_performance_trend(recent_snapshots)
            performance_drift = self._calculate_performance_drift(recent_snapshots)

            # Determine if adaptation is needed
            adaptation_needed, critical_issues = self._assess_adaptation_needs(
                overall_win_rate, model_reliability, confidence_calibration, performance_drift
            )

            return AutonomyMetrics(
                overall_win_rate=overall_win_rate,
                symbol_performance=symbol_performance,
                model_reliability=model_reliability,
                regime_effectiveness=regime_effectiveness,
                confidence_calibration=confidence_calibration,
                recent_trend=recent_trend,
                performance_drift=performance_drift,
                adaptation_needed=adaptation_needed,
                critical_issues=critical_issues
            )

        except Exception as e:
            logger.error(f"Error getting autonomy metrics: {e}")
            return self._get_default_metrics()

    def _calculate_symbol_win_rate(self, symbol: str) -> float:
        """Calculate win rate for a specific symbol."""
        try:
            symbol_snapshots = [s for s in self.performance_snapshots if s.symbol == symbol]
            if not symbol_snapshots:
                return 0.5  # Default neutral

            recent_snapshots = symbol_snapshots[-10:]  # Last 10 snapshots
            return statistics.mean(s.win_rate for s in recent_snapshots)

        except Exception as e:
            logger.error(f"Error calculating symbol win rate: {e}")
            return 0.5

    def _calculate_avg_return(self, symbol: str) -> float:
        """Calculate average return for a symbol."""
        try:
            symbol_snapshots = [s for s in self.performance_snapshots if s.symbol == symbol]
            if not symbol_snapshots:
                return 0.0

            recent_snapshots = symbol_snapshots[-10:]
            return statistics.mean(s.avg_return for s in recent_snapshots)

        except Exception as e:
            logger.error(f"Error calculating average return: {e}")
            return 0.0

    def _calculate_volatility_adjusted_pnl(self, symbol: str, trade_data: Dict[str, Any]) -> float:
        """Calculate volatility-adjusted PnL."""
        try:
            pnl = trade_data.get('pnl', 0.0)
            volatility = trade_data.get('volatility', 0.01)

            if volatility <= 0:
                return pnl

            # Adjust PnL by volatility (higher volatility = lower adjusted return)
            return pnl / (1 + volatility)

        except Exception as e:
            logger.error(f"Error calculating volatility-adjusted PnL: {e}")
            return 0.0

    def _get_symbol_trade_count(self, symbol: str) -> int:
        """Get trade count for a symbol."""
        try:
            symbol_snapshots = [s for s in self.performance_snapshots if s.symbol == symbol]
            return len(symbol_snapshots)

        except Exception as e:
            logger.error(f"Error getting symbol trade count: {e}")
            return 0

    def _calculate_confidence_accuracy(self, symbol: str) -> float:
        """Calculate confidence prediction accuracy."""
        try:
            symbol_snapshots = [s for s in self.performance_snapshots if s.symbol == symbol]
            if not symbol_snapshots:
                return 0.5

            recent_snapshots = symbol_snapshots[-5:]  # Last 5 snapshots
            return statistics.mean(s.confidence_accuracy for s in recent_snapshots)

        except Exception as e:
            logger.error(f"Error calculating confidence accuracy: {e}")
            return 0.5

    def _analyze_model_performance(self, model_outputs: Dict[str, Any]) -> Dict[str, float]:
        """Analyze individual model performance."""
        try:
            model_performance = {}

            for model_name, output in model_outputs.items():
                confidence = output.get('confidence', 0.0)
                signal_strength = output.get('signal_strength', 0.0)

                # Simple performance score based on confidence and signal strength
                performance_score = (confidence + signal_strength) / 2
                model_performance[model_name] = performance_score

            return model_performance

        except Exception as e:
            logger.error(f"Error analyzing model performance: {e}")
            return {}

    def _update_symbol_metrics(self, symbol: str, snapshot: PerformanceSnapshot):
        """Update symbol-specific metrics."""
        try:
            if symbol not in self.symbol_metrics:
                self.symbol_metrics[symbol] = {
                    'total_trades': 0,
                    'total_pnl': 0.0,
                    'win_count': 0,
                    'loss_count': 0
                }

            metrics = self.symbol_metrics[symbol]
            metrics['total_trades'] += 1
            metrics['total_pnl'] += snapshot.avg_return

            if snapshot.avg_return > 0:
                metrics['win_count'] += 1
            else:
                metrics['loss_count'] += 1

        except Exception as e:
            logger.error(f"Error updating symbol metrics: {e}")

    def _update_model_tracking(self, model_outputs: Dict[str, Any], outcome: str):
        """Update model performance tracking."""
        try:
            for model_name, output in model_outputs.items():
                confidence = output.get('confidence', 0.0)

                # Score based on outcome
                if outcome == 'win':
                    score = confidence
                elif outcome == 'loss':
                    score = 1.0 - confidence
                else:
                    score = 0.5  # Neutral for unknown outcomes

                self.model_performance[model_name].append(score)

                # Keep only recent performance
                if len(self.model_performance[model_name]) > 50:
                    self.model_performance[model_name] = self.model_performance[model_name][-50:]

        except Exception as e:
            logger.error(f"Error updating model tracking: {e}")

    def _calculate_model_reliability(self, snapshots: List[PerformanceSnapshot]) -> Dict[str, float]:
        """Calculate model reliability scores."""
        try:
            model_reliability = {}

            # Aggregate model performance across snapshots
            model_scores = defaultdict(list)
            for snapshot in snapshots:
                for model_name, score in snapshot.model_performance.items():
                    model_scores[model_name].append(score)

            # Calculate reliability as average performance
            for model_name, scores in model_scores.items():
                if scores:
                    model_reliability[model_name] = statistics.mean(scores)
                else:
                    model_reliability[model_name] = 0.5

            return model_reliability

        except Exception as e:
            logger.error(f"Error calculating model reliability: {e}")
            return {}

    def _calculate_regime_effectiveness(self, snapshots: List[PerformanceSnapshot]) -> Dict[str, float]:
        """Calculate effectiveness by market regime."""
        try:
            regime_performance = defaultdict(list)

            for snapshot in snapshots:
                regime = snapshot.regime_outcome
                if regime and regime != 'unknown':
                    regime_performance[regime].append(snapshot.avg_return)

            regime_effectiveness = {}
            for regime, returns in regime_performance.items():
                if returns:
                    regime_effectiveness[regime] = statistics.mean(returns)
                else:
                    regime_effectiveness[regime] = 0.0

            return regime_effectiveness

        except Exception as e:
            logger.error(f"Error calculating regime effectiveness: {e}")
            return {}

    def _calculate_confidence_calibration(self, snapshots: List[PerformanceSnapshot]) -> float:
        """Calculate confidence calibration accuracy."""
        try:
            if not snapshots:
                return 0.5

            calibration_scores = [s.confidence_accuracy for s in snapshots if s.confidence_accuracy > 0]

            if calibration_scores:
                return statistics.mean(calibration_scores)
            else:
                return 0.5

        except Exception as e:
            logger.error(f"Error calculating confidence calibration: {e}")
            return 0.5

    def _analyze_performance_trend(self, snapshots: List[PerformanceSnapshot]) -> str:
        """Analyze recent performance trend."""
        try:
            if len(snapshots) < 5:
                return 'insufficient_data'

            # Split into recent and older periods
            mid_point = len(snapshots) // 2
            older_returns = [s.avg_return for s in snapshots[:mid_point]]
            recent_returns = [s.avg_return for s in snapshots[mid_point:]]

            older_avg = statistics.mean(older_returns) if older_returns else 0
            recent_avg = statistics.mean(recent_returns) if recent_returns else 0

            improvement = recent_avg - older_avg

            if improvement > 0.01:
                return 'improving'
            elif improvement < -0.01:
                return 'declining'
            else:
                return 'stable'

        except Exception as e:
            logger.error(f"Error analyzing performance trend: {e}")
            return 'unknown'

    def _calculate_performance_drift(self, snapshots: List[PerformanceSnapshot]) -> float:
        """Calculate performance drift over time."""
        try:
            if len(snapshots) < 10:
                return 0.0

            # Calculate rolling average drift
            recent_10 = snapshots[-10:]
            recent_5 = snapshots[-5:]

            avg_10 = statistics.mean(s.avg_return for s in recent_10)
            avg_5 = statistics.mean(s.avg_return for s in recent_5)

            return avg_5 - avg_10

        except Exception as e:
            logger.error(f"Error calculating performance drift: {e}")
            return 0.0

    def _assess_adaptation_needs(self, win_rate: float, model_reliability: Dict[str, float],
                               confidence_calibration: float, performance_drift: float) -> tuple:
        """Assess if strategy adaptation is needed."""
        try:
            adaptation_needed = False
            critical_issues = []

            # Check win rate
            if win_rate < self.min_win_rate:
                adaptation_needed = True
                critical_issues.append(f"Low win rate: {win_rate:.2f} < {self.min_win_rate}")

            # Check model reliability
            poor_models = [name for name, score in model_reliability.items() if score < 0.4]
            if len(poor_models) > len(model_reliability) / 2:
                adaptation_needed = True
                critical_issues.append(f"Multiple poor performing models: {poor_models}")

            # Check confidence calibration
            if abs(confidence_calibration - 0.5) > self.confidence_tolerance:
                adaptation_needed = True
                critical_issues.append(f"Poor confidence calibration: {confidence_calibration:.2f}")

            # Check performance drift
            if performance_drift < -0.05:
                adaptation_needed = True
                critical_issues.append(f"Negative performance drift: {performance_drift:.3f}")

            return adaptation_needed, critical_issues

        except Exception as e:
            logger.error(f"Error assessing adaptation needs: {e}")
            return False, []

    def _get_default_metrics(self) -> AutonomyMetrics:
        """Get default metrics when no data is available."""
        return AutonomyMetrics(
            overall_win_rate=0.5,
            symbol_performance={},
            model_reliability={},
            regime_effectiveness={},
            confidence_calibration=0.5,
            recent_trend='insufficient_data',
            performance_drift=0.0,
            adaptation_needed=False,
            critical_issues=[]
        )

    def _load_data(self):
        """Load autonomy data from disk."""
        try:
            # Load performance snapshots
            if os.path.exists(self.snapshots_file):
                with open(self.snapshots_file, 'r') as f:
                    data = json.load(f)
                    self.performance_snapshots = [PerformanceSnapshot(**snap) for snap in data]

            # Load symbol metrics
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.symbol_metrics = data.get('symbol_metrics', {})
                    self.model_performance = data.get('model_performance', {})

        except Exception as e:
            logger.error(f"Error loading autonomy data: {e}")

    def _save_data(self):
        """Save autonomy data to disk."""
        try:
            # Save performance snapshots
            snapshots_data = [asdict(snap) for snap in self.performance_snapshots[-100:]]  # Keep last 100
            with open(self.snapshots_file, 'w') as f:
                json.dump(snapshots_data, f, indent=2)

            # Save metrics
            metrics_data = {
                'symbol_metrics': dict(self.symbol_metrics),
                'model_performance': {k: v[-50:] for k, v in self.model_performance.items()}  # Keep last 50
            }
            with open(self.metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving autonomy data: {e}")

    def get_tuning_recommendations(self) -> List[Dict[str, Any]]:
        """Get specific tuning recommendations based on performance analysis."""
        try:
            metrics = self.get_autonomy_metrics()
            recommendations = []

            # Model weight adjustments
            for model_name, reliability in metrics.model_reliability.items():
                if reliability < 0.3:
                    recommendations.append({
                        'type': 'model_weight',
                        'target': model_name,
                        'action': 'decrease',
                        'current_weight': 0.25,  # Would get from actual config
                        'suggested_weight': 0.15,
                        'reason': f'Low reliability: {reliability:.2f}'
                    })
                elif reliability > 0.8:
                    recommendations.append({
                        'type': 'model_weight',
                        'target': model_name,
                        'action': 'increase',
                        'current_weight': 0.25,
                        'suggested_weight': 0.35,
                        'reason': f'High reliability: {reliability:.2f}'
                    })

            # Confidence threshold adjustments
            if metrics.confidence_calibration < 0.4:
                recommendations.append({
                    'type': 'confidence_threshold',
                    'target': 'buy_threshold',
                    'action': 'increase',
                    'current_value': 0.7,
                    'suggested_value': 0.75,
                    'reason': 'Overconfident predictions detected'
                })

            # Regime-based adjustments
            for regime, effectiveness in metrics.regime_effectiveness.items():
                if effectiveness < -0.02:
                    recommendations.append({
                        'type': 'regime_sensitivity',
                        'target': regime,
                        'action': 'reduce_exposure',
                        'current_multiplier': 1.0,
                        'suggested_multiplier': 0.7,
                        'reason': f'Poor performance in {regime}: {effectiveness:.3f}'
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error getting tuning recommendations: {e}")
            return []
