#!/usr/bin/env python3
"""
Money Circle Responsive Design Testing & Verification
Tests dashboard layout across different screen sizes and devices.
"""

import requests
import re
import sys
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8085"

# Common mobile/tablet/desktop breakpoints
BREAKPOINTS = {
    "mobile_portrait": {"width": 375, "height": 667, "name": "Mobile Portrait (iPhone)"},
    "mobile_landscape": {"width": 667, "height": 375, "name": "Mobile Landscape"},
    "tablet_portrait": {"width": 768, "height": 1024, "name": "Tablet Portrait (iPad)"},
    "tablet_landscape": {"width": 1024, "height": 768, "name": "Tablet Landscape"},
    "desktop_small": {"width": 1366, "height": 768, "name": "Small Desktop"},
    "desktop_large": {"width": 1920, "height": 1080, "name": "Large Desktop"}
}

# CSS files to analyze for responsive design
CSS_FILES = [
    "/static/css/design_system.css",
    "/static/css/dashboard.css", 
    "/static/css/club.css",
    "/static/css/club_analytics.css",
    "/static/css/member_directory.css",
    "/static/css/strategy_marketplace.css"
]

def analyze_css_responsive_features():
    """Analyze CSS files for responsive design features"""
    print("\n📱 Analyzing CSS Responsive Design Features...")
    
    responsive_features = {
        "media_queries": 0,
        "flexbox_usage": 0,
        "grid_usage": 0,
        "viewport_units": 0,
        "responsive_typography": 0,
        "mobile_first": False
    }
    
    all_css_content = ""
    
    for css_file in CSS_FILES:
        try:
            response = requests.get(f"{BASE_URL}{css_file}", timeout=10)
            if response.status_code == 200:
                content = response.text
                all_css_content += content + "\n"
                
                # Count media queries
                media_queries = len(re.findall(r'@media[^{]+{', content))
                responsive_features["media_queries"] += media_queries
                
                # Check for flexbox
                if "display: flex" in content or "flex-direction" in content:
                    responsive_features["flexbox_usage"] += 1
                
                # Check for CSS Grid
                if "display: grid" in content or "grid-template" in content:
                    responsive_features["grid_usage"] += 1
                
                # Check for viewport units
                if "vw" in content or "vh" in content or "vmin" in content or "vmax" in content:
                    responsive_features["viewport_units"] += 1
                
                # Check for responsive typography
                if "clamp(" in content or "min(" in content or "max(" in content:
                    responsive_features["responsive_typography"] += 1
                
                print(f"  📄 {css_file}: {media_queries} media queries")
                
        except Exception as e:
            print(f"  ❌ Error analyzing {css_file}: {str(e)}")
    
    # Check for mobile-first approach
    if "min-width" in all_css_content:
        responsive_features["mobile_first"] = True
    
    return responsive_features

def check_viewport_meta_tag():
    """Check if pages have proper viewport meta tag"""
    print("\n🔍 Checking Viewport Meta Tag...")
    
    try:
        response = requests.get(f"{BASE_URL}/login", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Look for viewport meta tag
            viewport_pattern = r'<meta[^>]*name=["\']viewport["\'][^>]*>'
            viewport_match = re.search(viewport_pattern, content, re.IGNORECASE)
            
            if viewport_match:
                viewport_tag = viewport_match.group(0)
                print(f"  ✅ Viewport meta tag found: {viewport_tag}")
                
                # Check for responsive viewport settings
                if "width=device-width" in viewport_tag:
                    print(f"  ✅ Device width scaling enabled")
                if "initial-scale=1" in viewport_tag:
                    print(f"  ✅ Initial scale set correctly")
                if "user-scalable=no" in viewport_tag:
                    print(f"  ⚠️  User scaling disabled - may affect accessibility")
                else:
                    print(f"  ✅ User scaling allowed")
            else:
                print(f"  ❌ No viewport meta tag found - responsive design may not work properly")
                
    except Exception as e:
        print(f"  ❌ Error checking viewport meta tag: {str(e)}")

def analyze_responsive_breakpoints():
    """Analyze CSS breakpoints and responsive strategy"""
    print("\n📐 Analyzing Responsive Breakpoints...")
    
    try:
        # Check design system for breakpoints
        response = requests.get(f"{BASE_URL}/static/css/design_system.css", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Look for CSS custom properties for breakpoints
            breakpoint_vars = re.findall(r'--.*(?:breakpoint|screen|mobile|tablet|desktop).*?:\s*([^;]+)', content, re.IGNORECASE)
            
            if breakpoint_vars:
                print(f"  ✅ Found breakpoint variables:")
                for var in breakpoint_vars:
                    print(f"    • {var}")
            else:
                print(f"  ⚠️  No breakpoint variables found in design system")
            
            # Look for media queries
            media_queries = re.findall(r'@media[^{]+{', content)
            if media_queries:
                print(f"  ✅ Found {len(media_queries)} media queries in design system")
                for i, query in enumerate(media_queries[:3]):  # Show first 3
                    print(f"    • {query.strip()}")
                if len(media_queries) > 3:
                    print(f"    • ... and {len(media_queries) - 3} more")
            else:
                print(f"  ⚠️  No media queries found in design system")
                
    except Exception as e:
        print(f"  ❌ Error analyzing breakpoints: {str(e)}")

def check_touch_friendly_design():
    """Check for touch-friendly design elements"""
    print("\n👆 Checking Touch-Friendly Design...")
    
    touch_issues = []
    
    try:
        # Check dashboard CSS for touch considerations
        response = requests.get(f"{BASE_URL}/static/css/dashboard.css", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for minimum touch target sizes (44px recommended)
            if "44px" in content or "3rem" in content:
                print(f"  ✅ Found touch-friendly sizing (44px/3rem)")
            else:
                touch_issues.append("No explicit touch target sizing found")
            
            # Check for hover states that might not work on touch
            hover_states = len(re.findall(r':hover', content))
            if hover_states > 0:
                print(f"  ⚠️  Found {hover_states} hover states - ensure touch alternatives exist")
                touch_issues.append(f"{hover_states} hover-only interactions")
            
            # Check for touch-action CSS property
            if "touch-action" in content:
                print(f"  ✅ Touch-action property found - good for touch optimization")
            else:
                touch_issues.append("No touch-action optimization found")
                
    except Exception as e:
        print(f"  ❌ Error checking touch design: {str(e)}")
    
    return touch_issues

def generate_responsive_recommendations():
    """Generate recommendations for responsive design improvements"""
    print("\n💡 Responsive Design Recommendations...")
    
    recommendations = []
    
    # Analyze current responsive features
    features = analyze_css_responsive_features()
    
    if features["media_queries"] < 3:
        recommendations.append("Add more media queries for better responsive coverage")
    
    if features["flexbox_usage"] < 2:
        recommendations.append("Consider using more flexbox for flexible layouts")
    
    if features["grid_usage"] < 1:
        recommendations.append("CSS Grid could improve complex layouts")
    
    if features["viewport_units"] < 1:
        recommendations.append("Consider viewport units (vw, vh) for responsive sizing")
    
    if not features["mobile_first"]:
        recommendations.append("Consider mobile-first design approach with min-width queries")
    
    if features["responsive_typography"] < 1:
        recommendations.append("Add responsive typography with clamp() or fluid scaling")
    
    return recommendations

def main():
    """Run comprehensive responsive design verification"""
    print("📱 Money Circle Responsive Design Verification")
    print("=" * 55)
    print(f"Testing at: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all responsive design tests
    features = analyze_css_responsive_features()
    check_viewport_meta_tag()
    analyze_responsive_breakpoints()
    touch_issues = check_touch_friendly_design()
    recommendations = generate_responsive_recommendations()
    
    # Summary report
    print("\n" + "=" * 55)
    print("📊 Responsive Design Analysis Summary")
    print("=" * 55)
    
    print(f"\n📈 Responsive Features Found:")
    print(f"  • Media Queries: {features['media_queries']}")
    print(f"  • Flexbox Usage: {features['flexbox_usage']} files")
    print(f"  • CSS Grid Usage: {features['grid_usage']} files")
    print(f"  • Viewport Units: {features['viewport_units']} files")
    print(f"  • Responsive Typography: {features['responsive_typography']} files")
    print(f"  • Mobile-First Approach: {'✅ Yes' if features['mobile_first'] else '❌ No'}")
    
    if touch_issues:
        print(f"\n⚠️  Touch Design Issues:")
        for issue in touch_issues:
            print(f"  • {issue}")
    else:
        print(f"\n✅ No major touch design issues found")
    
    if recommendations:
        print(f"\n💡 Improvement Recommendations:")
        for rec in recommendations:
            print(f"  • {rec}")
    else:
        print(f"\n✅ Responsive design looks good!")
    
    print(f"\n🧪 Manual Testing Needed:")
    print(f"  1. Test on actual mobile devices")
    print(f"  2. Verify touch interactions work properly")
    print(f"  3. Check layout at different screen sizes")
    print(f"  4. Test landscape/portrait orientation changes")
    print(f"  5. Verify text readability on small screens")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)
