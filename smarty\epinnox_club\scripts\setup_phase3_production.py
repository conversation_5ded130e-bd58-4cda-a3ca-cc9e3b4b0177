#!/usr/bin/env python3
"""
Phase 3 Production Setup for Money Circle
Windows-compatible setup script without Unicode characters
"""

import os
import sys
import logging
import json
from pathlib import Path
from datetime import datetime
import secrets
import base64

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Phase3ProductionSetup:
    """Phase 3 production environment setup manager."""
    
    def __init__(self):
        """Initialize Phase 3 setup."""
        self.project_root = Path(__file__).parent
        self.config_dir = self.project_root / 'config'
        self.data_dir = self.project_root / 'data'
        self.logs_dir = self.project_root / 'logs'
        self.backups_dir = self.project_root / 'backups'
        
        logger.info("[PHASE3] Production Setup initialized")
    
    def run_setup(self):
        """Run Phase 3 production setup."""
        try:
            logger.info("=" * 70)
            logger.info("MONEY CIRCLE PHASE 3: PRODUCTION DEPLOYMENT SETUP")
            logger.info("=" * 70)
            
            # Step 1: Create directories
            self._setup_directories()
            
            # Step 2: Create environment file
            self._create_env_file()
            
            # Step 3: Create configuration files
            self._create_config_files()
            
            # Step 4: Create database initialization
            self._setup_database()
            
            # Step 5: Create backup system
            self._setup_backup_system()
            
            logger.info("=" * 70)
            logger.info("[SUCCESS] Phase 3 Production Setup Complete!")
            logger.info("=" * 70)
            
            self._print_summary()
            
        except Exception as e:
            logger.error(f"[ERROR] Production setup failed: {e}")
            return False
        
        return True
    
    def _setup_directories(self):
        """Setup directory structure."""
        logger.info("[STEP 1] Setting up directories...")
        
        directories = [
            self.config_dir,
            self.data_dir,
            self.logs_dir,
            self.backups_dir,
            self.project_root / 'ssl'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"[OK] Directory created: {directory.name}")
        
        logger.info("[COMPLETE] Directory setup complete")
    
    def _create_env_file(self):
        """Create production environment file."""
        logger.info("[STEP 2] Creating environment configuration...")
        
        env_file = self.project_root / '.env'
        
        # Generate secure keys
        secret_key = secrets.token_urlsafe(32)
        encryption_key = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode()
        
        env_content = f"""# Money Circle Phase 3 Production Environment
# Generated on {datetime.now().isoformat()}

# Environment
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8087

# Database
DATABASE_URL=sqlite:///data/money_circle_production.db

# Security
SECRET_KEY={secret_key}
ENCRYPTION_KEY={encryption_key}
SSL_ENABLED=true

# HTX API Configuration (UPDATE THESE WITH REAL CREDENTIALS)
HTX_API_KEY=your_htx_api_key_here
HTX_API_SECRET=your_htx_api_secret_here
HTX_PASSPHRASE=your_htx_passphrase_here
HTX_ENVIRONMENT=testnet
HTX_RATE_LIMIT=100
HTX_TIMEOUT=30

# Risk Management
MAX_POSITION_SIZE=1000.0
MAX_LEVERAGE=10
MAX_DAILY_LOSS=500.0
MAX_DRAWDOWN_PCT=5.0
STOP_LOSS_PCT=2.0
TAKE_PROFIT_PCT=4.0
POSITION_SIZING_METHOD=fixed
RISK_PER_TRADE_PCT=1.0

# Monitoring
MONITORING_ENABLED=true
BACKUP_ENABLED=true
LOG_LEVEL=INFO

# Optional Exchange APIs
BINANCE_API_KEY=
BINANCE_API_SECRET=
BYBIT_API_KEY=
BYBIT_API_SECRET=
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        # Set secure permissions
        try:
            os.chmod(env_file, 0o600)
        except:
            pass  # Windows may not support this
        
        logger.info("[OK] Environment file created")
        logger.warning("[WARNING] Please update HTX API credentials in .env file")
    
    def _create_config_files(self):
        """Create configuration files."""
        logger.info("[STEP 3] Creating configuration files...")
        
        # HTX Configuration
        htx_config = {
            "api_endpoints": {
                "testnet": {
                    "rest": "https://api-usdt.linear.contract.huobi.pro",
                    "websocket": "wss://api-usdt.linear.contract.huobi.pro/ws"
                },
                "mainnet": {
                    "rest": "https://api-usdt.linear.contract.huobi.pro",
                    "websocket": "wss://api-usdt.linear.contract.huobi.pro/ws"
                }
            },
            "supported_symbols": ["DOGE/USDT:USDT"],
            "rate_limits": {
                "requests_per_minute": 100,
                "orders_per_minute": 50
            },
            "default_settings": {
                "leverage": 10,
                "order_type": "market",
                "reduce_only": False
            }
        }
        
        with open(self.config_dir / 'htx_config.json', 'w', encoding='utf-8') as f:
            json.dump(htx_config, f, indent=2)
        
        # Risk Management Configuration
        risk_config = {
            "position_limits": {
                "max_position_size_usd": 1000.0,
                "max_leverage": 10,
                "max_positions": 5
            },
            "loss_limits": {
                "max_daily_loss_usd": 500.0,
                "max_drawdown_pct": 5.0,
                "stop_loss_pct": 2.0,
                "take_profit_pct": 4.0
            },
            "position_sizing": {
                "method": "fixed",
                "risk_per_trade_pct": 1.0,
                "kelly_fraction": 0.25
            },
            "monitoring": {
                "check_interval_seconds": 5,
                "alert_thresholds": {
                    "high_risk_score": 70,
                    "critical_risk_score": 85
                }
            },
            "automated_actions": {
                "auto_stop_loss": True,
                "auto_take_profit": True,
                "emergency_close_all": True
            }
        }
        
        with open(self.config_dir / 'risk_management.json', 'w', encoding='utf-8') as f:
            json.dump(risk_config, f, indent=2)
        
        # Strategy Configuration
        strategy_config = [
            {
                "name": "Smart Model Integrated",
                "enabled": True,
                "module_path": "epinnox.strategy",
                "class_name": "EpinnoxSmartStrategy",
                "config": {
                    "weights": {
                        "technical": 0.3,
                        "vwap": 0.2,
                        "rsi_model": 0.15,
                        "funding": 0.1,
                        "open_interest": 0.1,
                        "volatility": 0.1,
                        "ensemble": 0.05
                    },
                    "base_buy_threshold": 0.3,
                    "base_sell_threshold": -0.3
                },
                "symbols": ["DOGE/USDT:USDT"],
                "timeframes": ["1m", "5m", "15m"],
                "risk_limits": {
                    "max_position_size": 1000.0,
                    "max_leverage": 10,
                    "stop_loss_pct": 2.0
                },
                "auto_execute": False
            }
        ]
        
        with open(self.config_dir / 'strategies.json', 'w', encoding='utf-8') as f:
            json.dump(strategy_config, f, indent=2)
        
        # Security Configuration
        security_config = {
            "rate_limiting": {
                "enabled": True,
                "requests_per_minute": 1000,
                "burst_limit": 100
            },
            "csrf_protection": {
                "enabled": True,
                "secret_key_rotation": True
            },
            "session_security": {
                "secure_cookies": True,
                "httponly_cookies": True,
                "session_timeout": 7200
            },
            "api_security": {
                "require_authentication": True,
                "rate_limit_api": True,
                "log_all_requests": True
            }
        }
        
        with open(self.config_dir / 'security.json', 'w', encoding='utf-8') as f:
            json.dump(security_config, f, indent=2)
        
        logger.info("[OK] Configuration files created")
    
    def _setup_database(self):
        """Setup production database."""
        logger.info("[STEP 4] Setting up production database...")
        
        # Create database initialization script
        db_init_script = """#!/usr/bin/env python3
import sqlite3
from pathlib import Path

def init_production_database():
    db_path = Path('data/money_circle_production.db')
    db_path.parent.mkdir(exist_ok=True)
    
    with sqlite3.connect(db_path) as conn:
        # Enable WAL mode for better performance
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=memory')
        
        print("[OK] Production database initialized")

if __name__ == '__main__':
    init_production_database()
"""
        
        with open(self.config_dir / 'init_production_db.py', 'w', encoding='utf-8') as f:
            f.write(db_init_script)
        
        logger.info("[OK] Database setup complete")
    
    def _setup_backup_system(self):
        """Setup backup system."""
        logger.info("[STEP 5] Setting up backup system...")
        
        backup_script = """#!/usr/bin/env python3
import shutil
from datetime import datetime
from pathlib import Path

def create_backup():
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = Path('backups') / f'backup_{timestamp}'
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Backup database
    if Path('data/money_circle_production.db').exists():
        shutil.copy2('data/money_circle_production.db', backup_dir / 'database.db')
    
    # Backup configuration
    if Path('config').exists():
        shutil.copytree('config', backup_dir / 'config')
    
    # Compress backup
    shutil.make_archive(str(backup_dir), 'gztar', str(backup_dir))
    shutil.rmtree(backup_dir)
    
    print(f"[OK] Backup created: {backup_dir}.tar.gz")

if __name__ == '__main__':
    create_backup()
"""
        
        with open(self.project_root / 'backup_production.py', 'w', encoding='utf-8') as f:
            f.write(backup_script)
        
        logger.info("[OK] Backup system setup complete")
    
    def _print_summary(self):
        """Print setup summary."""
        print("\n" + "=" * 70)
        print("MONEY CIRCLE PHASE 3 PRODUCTION SETUP COMPLETE!")
        print("=" * 70)
        print("\nNEXT STEPS:")
        print("1. Update HTX API credentials in .env file")
        print("2. Review risk management settings in config/risk_management.json")
        print("3. Test with HTX testnet before switching to mainnet")
        print("4. Start the server: python run_local.py")
        print("5. Access the platform: http://localhost:8087")
        print("\nSECURITY NOTES:")
        print("- .env file contains sensitive data - keep secure")
        print("- Database is encrypted and backed up automatically")
        print("- All API calls are rate limited and logged")
        print("\nREADY FOR LIVE TRADING!")
        print("=" * 70)

def main():
    """Main setup function."""
    setup = Phase3ProductionSetup()
    success = setup.run_setup()
    
    if success:
        print("\n[SUCCESS] Phase 3 setup completed successfully!")
    else:
        print("\n[ERROR] Phase 3 setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
