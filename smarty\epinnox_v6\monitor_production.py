#!/usr/bin/env python3
"""
🔥 Production Monitoring Script
Real-time monitoring for Epinnox V6 live trading
"""

import asyncio
import aiohttp
import json
import time
import os
from datetime import datetime
from pathlib import Path

class ProductionMonitor:
    def __init__(self, dashboard_url="http://localhost:8086"):
        self.dashboard_url = dashboard_url
        self.session = None
        self.running = False
    
    async def start_monitoring(self):
        """Start real-time production monitoring."""
        print("🔥 EPINNOX V6 PRODUCTION MONITOR")
        print("=" * 50)
        
        self.session = aiohttp.ClientSession()
        self.running = True
        
        try:
            while self.running:
                await self.check_production_status()
                await self.check_latest_logs()
                await asyncio.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        finally:
            if self.session:
                await self.session.close()
    
    async def check_production_status(self):
        """Check production system status."""
        try:
            async with self.session.get(f"{self.dashboard_url}/api/production/status") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        self.display_production_status(data)
                    else:
                        print(f"❌ Status check failed: {data.get('error')}")
                else:
                    print(f"❌ HTTP {response.status} - Dashboard not responding")
                    
        except Exception as e:
            print(f"❌ Error checking status: {e}")
    
    async def check_latest_logs(self):
        """Check latest log entries."""
        try:
            async with self.session.get(f"{self.dashboard_url}/api/logs/latest") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logs = data.get('logs', [])
                        # Show only last 3 log entries to avoid spam
                        recent_logs = logs[-3:] if len(logs) > 3 else logs
                        for log_line in recent_logs:
                            if any(keyword in log_line.lower() for keyword in ['error', 'warning', 'trade', 'emergency']):
                                print(f"📝 {log_line}")
                                
        except Exception as e:
            print(f"❌ Error checking logs: {e}")
    
    def display_production_status(self, data):
        """Display production status in a clean format."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        prod_status = data.get('production_status', {})
        exec_status = data.get('execution_status', {})
        account_status = data.get('account_status', {})
        
        # Clear screen and show status
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🔥 EPINNOX V6 PRODUCTION MONITOR")
        print("=" * 50)
        print(f"⏰ Last Update: {timestamp}")
        print()
        
        # Production Status
        print("🚀 PRODUCTION STATUS:")
        live_trading = prod_status.get('live_trading_enabled', False)
        trading_mode = prod_status.get('trading_mode', 'unknown')
        environment = prod_status.get('environment', 'unknown')
        strategy_mode = prod_status.get('strategy_mode', 'unknown')
        
        status_icon = "🟢" if live_trading else "🔴"
        print(f"   {status_icon} Live Trading: {'ENABLED' if live_trading else 'DISABLED'}")
        print(f"   🎯 Trading Mode: {trading_mode.upper()}")
        print(f"   🌍 Environment: {environment.upper()}")
        print(f"   ⚡ Strategy: {strategy_mode.upper()}")
        print()
        
        # Execution Status
        print("🎯 EXECUTION STATUS:")
        emergency_stopped = exec_status.get('emergency_stopped', False)
        daily_pnl = exec_status.get('daily_pnl', 0.0)
        consecutive_losses = exec_status.get('consecutive_losses', 0)
        max_daily_loss = exec_status.get('max_daily_loss', 1.0)
        active_positions = exec_status.get('active_positions', 0)
        
        emergency_icon = "🚨" if emergency_stopped else "✅"
        pnl_icon = "📈" if daily_pnl >= 0 else "📉"
        
        print(f"   {emergency_icon} Emergency Stop: {'ACTIVE' if emergency_stopped else 'NORMAL'}")
        print(f"   {pnl_icon} Daily PnL: ${daily_pnl:.2f} / -${max_daily_loss:.2f}")
        print(f"   🔄 Consecutive Losses: {consecutive_losses}")
        print(f"   📊 Active Positions: {active_positions}")
        print()
        
        # Account Status
        print("💰 ACCOUNT STATUS:")
        balance = account_status.get('balance', 0.0)
        margin_used = account_status.get('margin_used_pct', 0.0)
        risk_level = account_status.get('risk_level', 'unknown')
        positions_count = account_status.get('positions_count', 0)
        
        risk_icon = {"safe": "🟢", "moderate": "🟡", "high": "🟠", "critical": "🔴"}.get(risk_level, "⚪")
        
        print(f"   💵 Balance: ${balance:.2f}")
        print(f"   📊 Margin Used: {margin_used:.1f}%")
        print(f"   {risk_icon} Risk Level: {risk_level.upper()}")
        print(f"   📈 Positions: {positions_count}")
        print()
        
        # Alerts
        alerts = []
        if emergency_stopped:
            alerts.append("🚨 EMERGENCY STOP ACTIVE")
        if daily_pnl <= -max_daily_loss * 0.8:
            alerts.append(f"⚠️ Daily loss approaching limit: ${daily_pnl:.2f}")
        if consecutive_losses >= 2:
            alerts.append(f"⚠️ {consecutive_losses} consecutive losses")
        if margin_used >= 8.0:
            alerts.append(f"⚠️ High margin usage: {margin_used:.1f}%")
        
        if alerts:
            print("🚨 ALERTS:")
            for alert in alerts:
                print(f"   {alert}")
            print()
        
        print("📝 Recent Activity (last 3 entries shown above)")
        print("Press Ctrl+C to stop monitoring")

async def main():
    """Main monitoring function."""
    monitor = ProductionMonitor()
    await monitor.start_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
