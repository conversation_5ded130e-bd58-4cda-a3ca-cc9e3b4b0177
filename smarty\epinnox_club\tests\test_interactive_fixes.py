#!/usr/bin/env python3
"""
Interactive Elements Functionality Test Suite
Tests all the fixed interactive elements including:
- Header navigation (user dropdown, notification bell, mobile menu)
- Dashboard buttons (Add Exchange, tab switching, order placement)
- Modal functionality
- JavaScript loading and initialization
"""

import requests
import json
import time
from datetime import datetime

class InteractiveElementsTester:
    def __init__(self, base_url="http://localhost:8087"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def run_comprehensive_test(self):
        """Run comprehensive test of interactive elements functionality."""
        print("🧪 INTERACTIVE ELEMENTS FUNCTIONALITY TEST SUITE")
        print("=" * 60)
        
        # Test 1: Platform Health Check
        self.test_platform_health()
        
        # Test 2: Authentication Flow
        self.test_authentication_flow()
        
        # Test 3: Header Navigation JavaScript Loading
        self.test_header_navigation_loading()
        
        # Test 4: Dashboard JavaScript Loading
        self.test_dashboard_javascript_loading()
        
        # Test 5: Interactive Elements Presence
        self.test_interactive_elements_presence()
        
        # Test 6: CSS and Styling
        self.test_css_and_styling()
        
        # Test 7: API Endpoints
        self.test_api_endpoints()
        
        print("\n" + "=" * 60)
        print("✅ INTERACTIVE ELEMENTS FUNCTIONALITY TEST COMPLETE")
        
    def test_platform_health(self):
        """Test platform health and availability."""
        print("\n📊 Testing Platform Health...")
        
        try:
            resp = self.session.get(f"{self.base_url}/health", timeout=10)
            if resp.status_code == 200:
                health_data = resp.json()
                print(f"✅ Platform Status: {health_data.get('status', 'unknown')}")
                print(f"✅ Database: {health_data.get('database', 'unknown')}")
                print(f"✅ Market Data: {health_data.get('market_data', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {resp.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
            
    def test_authentication_flow(self):
        """Test authentication and session management."""
        print("\n🔐 Testing Authentication Flow...")
        
        try:
            # Get login page
            resp = self.session.get(f"{self.base_url}/login", timeout=10)
            if resp.status_code != 200:
                print(f"❌ Login page failed: {resp.status_code}")
                return False
                
            # Login with epinnox credentials
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            resp = self.session.post(f"{self.base_url}/login", data=login_data, timeout=10)
            if resp.status_code in [200, 302]:  # Success or redirect
                print("✅ Authentication successful")
                print("✅ Session established")
                return True
            else:
                print(f"❌ Authentication failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
            
    def test_header_navigation_loading(self):
        """Test header navigation JavaScript loading."""
        print("\n🧭 Testing Header Navigation JavaScript...")
        
        try:
            # Test header navigation JavaScript file
            resp = self.session.get(f"{self.base_url}/static/js/header_navigation.js", timeout=10)
            if resp.status_code == 200:
                js_content = resp.text
                
                # Check for key functions
                required_functions = [
                    'toggleNotifications',
                    'toggleUserDropdown',
                    'toggleMobileMenu',
                    'initializeHeaderNavigation',
                    'closeAllDropdowns'
                ]
                
                found_functions = []
                for func in required_functions:
                    if f"function {func}" in js_content or f"{func} =" in js_content:
                        found_functions.append(func)
                        
                print(f"✅ Header navigation JS loaded: {len(js_content)} characters")
                print(f"✅ Found {len(found_functions)}/5 required functions:")
                for func in found_functions:
                    print(f"   ✓ {func}")
                    
                # Check if functions are exported globally
                if 'window.toggleNotifications' in js_content:
                    print("✅ Functions exported to global scope")
                else:
                    print("⚠️ Functions may not be globally accessible")
                    
                return len(found_functions) >= 4
            else:
                print(f"❌ Header navigation JS failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Header navigation JS test error: {e}")
            return False
            
    def test_dashboard_javascript_loading(self):
        """Test dashboard JavaScript loading."""
        print("\n📊 Testing Dashboard JavaScript...")
        
        try:
            # Test dashboard JavaScript file
            resp = self.session.get(f"{self.base_url}/static/js/personal_dashboard.js", timeout=10)
            if resp.status_code == 200:
                js_content = resp.text
                
                # Check for key functions
                required_functions = [
                    'showAddExchangeModal',
                    'closeModal',
                    'switchTab',
                    'placeMarketOrder',
                    'placeLimitOrder',
                    'initializePersonalDashboard'
                ]
                
                found_functions = []
                for func in required_functions:
                    if f"function {func}" in js_content or f"{func} =" in js_content:
                        found_functions.append(func)
                        
                print(f"✅ Dashboard JS loaded: {len(js_content)} characters")
                print(f"✅ Found {len(found_functions)}/6 required functions:")
                for func in found_functions:
                    print(f"   ✓ {func}")
                    
                return len(found_functions) >= 5
            else:
                print(f"❌ Dashboard JS failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Dashboard JS test error: {e}")
            return False
            
    def test_interactive_elements_presence(self):
        """Test presence of interactive elements in dashboard."""
        print("\n🎯 Testing Interactive Elements Presence...")
        
        try:
            resp = self.session.get(f"{self.base_url}/dashboard", timeout=10)
            if resp.status_code == 200:
                content = resp.text
                
                # Check for interactive elements
                interactive_elements = {
                    'showAddExchangeModal()': 'Add Exchange Button',
                    'toggleNotifications()': 'Notification Bell',
                    'toggleUserDropdown()': 'User Profile Dropdown',
                    'toggleMobileMenu()': 'Mobile Menu Toggle',
                    'switchTab(': 'Tab Switching Buttons',
                    'placeMarketOrder()': 'Market Order Button',
                    'placeLimitOrder()': 'Limit Order Button',
                    'closeModal(': 'Modal Close Buttons',
                    'header_navigation.js': 'Header Navigation Script',
                    'personal_dashboard.js': 'Dashboard Script'
                }
                
                found_elements = []
                missing_elements = []
                
                for element, description in interactive_elements.items():
                    if element in content:
                        found_elements.append(description)
                    else:
                        missing_elements.append(description)
                        
                print(f"✅ Found {len(found_elements)}/10 interactive elements:")
                for element in found_elements:
                    print(f"   ✓ {element}")
                    
                if missing_elements:
                    print(f"⚠️ Missing elements:")
                    for element in missing_elements:
                        print(f"   ✗ {element}")
                        
                # Check for JavaScript loading order
                if 'header_navigation.js' in content and 'personal_dashboard.js' in content:
                    header_pos = content.find('header_navigation.js')
                    dashboard_pos = content.find('personal_dashboard.js')
                    if header_pos < dashboard_pos:
                        print("✅ JavaScript loading order correct")
                    else:
                        print("⚠️ JavaScript loading order may be incorrect")
                        
                return len(found_elements) >= 8
            else:
                print(f"❌ Dashboard page failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Interactive elements test error: {e}")
            return False
            
    def test_css_and_styling(self):
        """Test CSS loading and styling."""
        print("\n🎨 Testing CSS and Styling...")
        
        try:
            # Test dashboard CSS
            resp = self.session.get(f"{self.base_url}/static/css/dashboard.css", timeout=10)
            if resp.status_code == 200:
                css_content = resp.text
                
                # Check for key CSS rules
                css_checks = [
                    'button',
                    '.btn',
                    '.modal',
                    'z-index',
                    'pointer-events',
                    'cursor: pointer',
                    '.exchange-card',
                    '.tab-btn'
                ]
                
                found_css = []
                for check in css_checks:
                    if check in css_content:
                        found_css.append(check)
                        
                print(f"✅ Dashboard CSS loaded: {len(css_content)} characters")
                print(f"✅ Found {len(found_css)}/8 CSS elements:")
                for css in found_css:
                    print(f"   ✓ {css}")
                    
                # Check for z-index fixes
                if 'z-index: 10' in css_content:
                    print("✅ Z-index fixes applied")
                else:
                    print("⚠️ Z-index fixes may be missing")
                    
                return len(found_css) >= 6
            else:
                print(f"❌ Dashboard CSS failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ CSS test error: {e}")
            return False
            
    def test_api_endpoints(self):
        """Test API endpoints functionality."""
        print("\n🔌 Testing API Endpoints...")
        
        try:
            # Test user API
            resp = self.session.get(f"{self.base_url}/api/user/current", timeout=10)
            if resp.status_code == 200:
                user_data = resp.json()
                print(f"✅ User API accessible: {user_data.get('username', 'unknown')}")
            else:
                print(f"⚠️ User API status: {resp.status_code}")
                
            # Test notifications API
            resp = self.session.get(f"{self.base_url}/api/notifications/recent", timeout=10)
            if resp.status_code == 200:
                notifications = resp.json()
                print(f"✅ Notifications API accessible: {len(notifications)} notifications")
            else:
                print(f"⚠️ Notifications API status: {resp.status_code}")
                
            # Test market data API
            resp = self.session.get(f"{self.base_url}/api/market-data/current", timeout=10)
            if resp.status_code == 200:
                market_data = resp.json()
                print(f"✅ Market data API accessible: {len(market_data)} data points")
            else:
                print(f"⚠️ Market data API status: {resp.status_code}")
                
            return True
            
        except Exception as e:
            print(f"❌ API endpoints test error: {e}")
            return False

def main():
    """Run the interactive elements functionality test suite."""
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = InteractiveElementsTester()
    tester.run_comprehensive_test()
    
    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🌟 NEXT STEPS:")
    print("1. Open http://localhost:8087/dashboard in your browser")
    print("2. Test the Add Exchange button functionality")
    print("3. Test the user profile dropdown in the header")
    print("4. Test the notification bell functionality")
    print("5. Test tab switching and order placement buttons")

if __name__ == "__main__":
    main()
