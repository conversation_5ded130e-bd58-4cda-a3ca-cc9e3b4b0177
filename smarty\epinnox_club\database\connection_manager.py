"""
Database Connection Manager
Production-ready SQLite connection management with proper lifecycle handling
"""

import sqlite3
import threading
import time
import logging
import asyncio
import aiosqlite
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager, contextmanager
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseConnectionManager:
    """Enhanced database connection manager with proper lifecycle management."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._connection_pool: Dict[int, sqlite3.Connection] = {}
        self._async_connection_pool: Dict[int, aiosqlite.Connection] = {}
        self._connection_lock = threading.RLock()
        self._last_health_check = 0
        self._health_check_interval = 30  # seconds
        self._max_connections = 20
        self._connection_timeout = 30.0
        
        # Ensure database directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
    
    def _get_thread_id(self) -> int:
        """Get current thread ID for connection pooling."""
        return threading.get_ident()
    
    def _optimize_connection(self, conn: sqlite3.Connection) -> None:
        """Apply SQLite optimizations to connection."""
        try:
            # Enable WAL mode for better concurrency
            conn.execute("PRAGMA journal_mode=WAL")
            
            # Optimize for performance
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")  # 64MB cache
            conn.execute("PRAGMA temp_store=memory")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB mmap
            
            # Enable foreign keys
            conn.execute("PRAGMA foreign_keys=ON")
            
            # Set busy timeout
            conn.execute(f"PRAGMA busy_timeout={int(self._connection_timeout * 1000)}")
            
            logger.debug(f"[DB] Connection optimized for thread {self._get_thread_id()}")
            
        except Exception as e:
            logger.warning(f"[DB] Failed to optimize connection: {e}")
    
    def _create_connection(self) -> sqlite3.Connection:
        """Create a new optimized SQLite connection."""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=self._connection_timeout,
                isolation_level=None  # Autocommit mode
            )
            conn.row_factory = sqlite3.Row
            self._optimize_connection(conn)
            return conn
            
        except Exception as e:
            logger.error(f"[DB] Failed to create connection: {e}")
            raise
    
    def _test_connection(self, conn: sqlite3.Connection) -> bool:
        """Test if connection is still valid."""
        try:
            conn.execute("SELECT 1").fetchone()
            return True
        except Exception:
            return False
    
    @contextmanager
    def get_connection(self):
        """Get a thread-safe database connection with automatic cleanup."""
        thread_id = self._get_thread_id()
        conn = None
        
        try:
            with self._connection_lock:
                # Check if we have a valid connection for this thread
                if thread_id in self._connection_pool:
                    conn = self._connection_pool[thread_id]
                    if not self._test_connection(conn):
                        # Connection is stale, remove and create new
                        try:
                            conn.close()
                        except:
                            pass
                        del self._connection_pool[thread_id]
                        conn = None
                
                # Create new connection if needed
                if conn is None:
                    conn = self._create_connection()
                    self._connection_pool[thread_id] = conn
                    logger.debug(f"[DB] Created new connection for thread {thread_id}")
            
            yield conn
            
        except Exception as e:
            logger.error(f"[DB] Connection error: {e}")
            # Remove failed connection from pool
            with self._connection_lock:
                if thread_id in self._connection_pool:
                    try:
                        self._connection_pool[thread_id].close()
                    except:
                        pass
                    del self._connection_pool[thread_id]
            raise
    
    @asynccontextmanager
    async def get_async_connection(self):
        """Get an async database connection with automatic cleanup."""
        conn = None
        try:
            conn = await aiosqlite.connect(
                self.db_path,
                timeout=self._connection_timeout
            )
            conn.row_factory = aiosqlite.Row
            
            # Apply optimizations
            await conn.execute("PRAGMA journal_mode=WAL")
            await conn.execute("PRAGMA synchronous=NORMAL")
            await conn.execute("PRAGMA cache_size=-64000")
            await conn.execute("PRAGMA foreign_keys=ON")
            await conn.execute(f"PRAGMA busy_timeout={int(self._connection_timeout * 1000)}")
            
            yield conn
            
        except Exception as e:
            logger.error(f"[DB] Async connection error: {e}")
            raise
        finally:
            if conn:
                try:
                    await conn.close()
                except:
                    pass
    
    def health_check(self) -> bool:
        """Perform health check on database connections."""
        current_time = time.time()
        
        # Only run health check if enough time has passed
        if current_time - self._last_health_check < self._health_check_interval:
            return True
        
        try:
            with self._connection_lock:
                # Test all connections in pool
                failed_connections = []
                for thread_id, conn in self._connection_pool.items():
                    if not self._test_connection(conn):
                        failed_connections.append(thread_id)
                
                # Remove failed connections
                for thread_id in failed_connections:
                    try:
                        self._connection_pool[thread_id].close()
                    except:
                        pass
                    del self._connection_pool[thread_id]
                    logger.warning(f"[DB] Removed stale connection for thread {thread_id}")
                
                self._last_health_check = current_time
                
                # Test with a new connection
                with self.get_connection() as conn:
                    conn.execute("SELECT 1").fetchone()
                
                logger.debug(f"[DB] Health check passed. Active connections: {len(self._connection_pool)}")
                return True
                
        except Exception as e:
            logger.error(f"[DB] Health check failed: {e}")
            return False
    
    def close_all_connections(self):
        """Close all connections in the pool."""
        with self._connection_lock:
            for thread_id, conn in self._connection_pool.items():
                try:
                    conn.close()
                    logger.debug(f"[DB] Closed connection for thread {thread_id}")
                except Exception as e:
                    logger.warning(f"[DB] Error closing connection for thread {thread_id}: {e}")
            
            self._connection_pool.clear()
            logger.info("[DB] All connections closed")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        with self._connection_lock:
            return {
                'active_connections': len(self._connection_pool),
                'max_connections': self._max_connections,
                'last_health_check': self._last_health_check,
                'db_path': self.db_path
            }


# Global connection manager instance
_connection_manager: Optional[DatabaseConnectionManager] = None


def get_connection_manager(db_path: str = None) -> DatabaseConnectionManager:
    """Get or create the global connection manager."""
    global _connection_manager
    
    if _connection_manager is None or (db_path and _connection_manager.db_path != db_path):
        _connection_manager = DatabaseConnectionManager(db_path)
    
    return _connection_manager


def close_all_connections():
    """Close all database connections."""
    global _connection_manager
    if _connection_manager:
        _connection_manager.close_all_connections()
