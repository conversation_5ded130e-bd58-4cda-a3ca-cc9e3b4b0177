"""
WebSocket Connection Manager
Enhanced WebSocket connection management with retry logic and health monitoring
"""

import asyncio
import json
import gzip
import logging
import time
import websockets
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """WebSocket connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


@dataclass
class ConnectionConfig:
    """WebSocket connection configuration."""
    url: str
    ping_interval: Optional[int] = None
    ping_timeout: Optional[int] = None
    close_timeout: int = 10
    max_retries: int = 5
    retry_delay: float = 5.0
    max_retry_delay: float = 60.0
    backoff_multiplier: float = 1.5


@dataclass
class ConnectionStats:
    """WebSocket connection statistics."""
    connect_time: Optional[datetime] = None
    last_message_time: Optional[datetime] = None
    messages_received: int = 0
    ping_count: int = 0
    reconnect_count: int = 0
    last_error: Optional[str] = None


class WebSocketConnectionManager:
    """Enhanced WebSocket connection manager with retry logic."""

    def __init__(self, config: ConnectionConfig):
        self.config = config
        self.state = ConnectionState.DISCONNECTED
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.stats = ConnectionStats()

        # Event handlers
        self.on_message: Optional[Callable[[dict], None]] = None
        self.on_connect: Optional[Callable[[], None]] = None
        self.on_disconnect: Optional[Callable[[], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None

        # Internal state
        self._running = False
        self._retry_count = 0
        self._last_ping_time = 0
        self._connection_task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the WebSocket connection with retry logic."""
        if self._running:
            return

        self._running = True
        self.state = ConnectionState.CONNECTING

        logger.info(f"[WS] Starting WebSocket connection to {self.config.url}")

        self._connection_task = asyncio.create_task(self._connection_loop())

    async def stop(self):
        """Stop the WebSocket connection."""
        self._running = False
        self.state = ConnectionState.DISCONNECTED

        if self._connection_task:
            self._connection_task.cancel()
            try:
                await self._connection_task
            except asyncio.CancelledError:
                pass

        if self.websocket:
            await self.websocket.close()
            self.websocket = None

        logger.info("[WS] WebSocket connection stopped")

    async def send_message(self, message: dict):
        """Send a message through the WebSocket."""
        if self.websocket and self.state == ConnectionState.CONNECTED:
            try:
                await self.websocket.send(json.dumps(message))
                logger.debug(f"[WS] Sent message: {message}")
            except Exception as e:
                logger.error(f"[WS] Failed to send message: {e}")
                await self._handle_error(e)

    async def _connection_loop(self):
        """Main connection loop with retry logic."""
        while self._running:
            try:
                await self._connect_and_listen()

                # Reset retry count on successful connection
                self._retry_count = 0

            except Exception as e:
                await self._handle_error(e)

                if not self._running:
                    break

                # Calculate retry delay with exponential backoff
                retry_delay = min(
                    self.config.retry_delay * (self.config.backoff_multiplier ** self._retry_count),
                    self.config.max_retry_delay
                )

                self._retry_count += 1

                if self._retry_count > self.config.max_retries:
                    logger.error(f"[WS] Max retries ({self.config.max_retries}) exceeded")
                    self.state = ConnectionState.FAILED
                    break

                logger.warning(f"[WS] Retrying connection in {retry_delay:.1f}s (attempt {self._retry_count})")
                self.state = ConnectionState.RECONNECTING

                await asyncio.sleep(retry_delay)

    async def _connect_and_listen(self):
        """Connect to WebSocket and listen for messages."""
        logger.info(f"[WS] Connecting to {self.config.url}")

        async with websockets.connect(
            self.config.url,
            ping_interval=self.config.ping_interval,
            ping_timeout=self.config.ping_timeout,
            close_timeout=self.config.close_timeout
        ) as websocket:

            self.websocket = websocket
            self.state = ConnectionState.CONNECTED
            self.stats.connect_time = datetime.now()

            if self._retry_count > 0:
                self.stats.reconnect_count += 1

            logger.info("[WS] ✅ WebSocket connected successfully")

            if self.on_connect:
                await self.on_connect()

            # Listen for messages
            async for message in websocket:
                if not self._running:
                    break

                try:
                    await self._process_message(message)
                except Exception as e:
                    logger.error(f"[WS] Error processing message: {e}")

    async def _process_message(self, message):
        """Process incoming WebSocket message."""
        self.stats.last_message_time = datetime.now()
        self.stats.messages_received += 1

        try:
            # Handle different message types
            if isinstance(message, bytes):
                # Try to decompress if it's gzipped (HTX sends gzipped data)
                try:
                    data = json.loads(gzip.decompress(message).decode('utf-8'))
                except:
                    # If decompression fails, try as regular bytes
                    data = json.loads(message.decode('utf-8'))
            else:
                data = json.loads(message)

            # Handle ping/pong for HTX
            if 'ping' in data:
                await self._handle_ping(data['ping'])
                return

            # Call message handler
            if self.on_message:
                await self.on_message(data)

        except Exception as e:
            logger.error(f"[WS] Error parsing message: {e}")

    async def _handle_ping(self, ping_timestamp):
        """Handle ping message and respond with pong."""
        try:
            self.stats.ping_count += 1
            self._last_ping_time = ping_timestamp

            pong_msg = {"pong": ping_timestamp}
            await self.websocket.send(json.dumps(pong_msg))

            logger.debug(f"[WS] 🏓 Ping/pong #{self.stats.ping_count}: {ping_timestamp}")

        except Exception as e:
            logger.error(f"[WS] Error handling ping: {e}")

    async def _handle_error(self, error: Exception):
        """Handle WebSocket errors."""
        self.stats.last_error = str(error)

        if self.state == ConnectionState.CONNECTED:
            self.state = ConnectionState.DISCONNECTED

            if self.on_disconnect:
                await self.on_disconnect()

        if self.on_error:
            await self.on_error(error)

        logger.error(f"[WS] WebSocket error: {error}")

    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        uptime = None
        if self.stats.connect_time:
            uptime = (datetime.now() - self.stats.connect_time).total_seconds()

        return {
            'state': self.state.value,
            'url': self.config.url,
            'uptime_seconds': uptime,
            'messages_received': self.stats.messages_received,
            'ping_count': self.stats.ping_count,
            'reconnect_count': self.stats.reconnect_count,
            'retry_count': self._retry_count,
            'last_error': self.stats.last_error,
            'last_message_time': self.stats.last_message_time.isoformat() if self.stats.last_message_time else None
        }

    def is_healthy(self) -> bool:
        """Check if connection is healthy."""
        if self.state != ConnectionState.CONNECTED:
            return False

        # Check if we've received messages recently
        if self.stats.last_message_time:
            time_since_last_message = datetime.now() - self.stats.last_message_time
            if time_since_last_message > timedelta(minutes=5):
                return False

        return True


class HTXWebSocketManager(WebSocketConnectionManager):
    """HTX-specific WebSocket manager with proper ping/pong handling."""

    def __init__(self, symbols: List[str]):
        config = ConnectionConfig(
            url="wss://api-aws.huobi.pro/ws",
            ping_interval=None,  # HTX handles ping/pong manually
            ping_timeout=None,
            close_timeout=10,
            max_retries=10,
            retry_delay=5.0,
            max_retry_delay=60.0,
            backoff_multiplier=1.5
        )

        super().__init__(config)
        self.symbols = symbols
        self.subscribed_symbols = set()

    async def _on_connect_handler(self):
        """Handle connection establishment."""
        logger.info("[HTX] Connected to HTX WebSocket")

        # Subscribe to all symbols
        for symbol in self.symbols:
            await self._subscribe_to_symbol(symbol)

    async def _subscribe_to_symbol(self, symbol: str):
        """Subscribe to a symbol's market data."""
        try:
            htx_symbol = symbol.replace('/', '').lower()
            subscribe_msg = {
                "sub": f"market.{htx_symbol}.detail",
                "id": f"id_{htx_symbol}"
            }

            await self.send_message(subscribe_msg)
            self.subscribed_symbols.add(symbol)

            logger.info(f"[HTX] 📡 Subscribed to {symbol} ({htx_symbol})")

        except Exception as e:
            logger.error(f"[HTX] Failed to subscribe to {symbol}: {e}")

    async def start_htx_connection(self):
        """Start HTX WebSocket connection with proper handlers."""
        self.on_connect = self._on_connect_handler
        await self.start()


async def test_htx_connection():
    """Test HTX WebSocket connection."""
    logger.info("[TEST] Testing HTX WebSocket connection...")

    symbols = ['BTC/USDT', 'ETH/USDT']
    manager = HTXWebSocketManager(symbols)

    # Set up message handler
    async def message_handler(data):
        if 'ch' in data and 'tick' in data:
            symbol = data['ch'].replace('market.', '').replace('.detail', '').upper()
            tick = data['tick']
            price = tick.get('close', 0)
            logger.info(f"[TEST] {symbol}: ${price:,.2f}")

    manager.on_message = message_handler

    try:
        await manager.start_htx_connection()

        # Let it run for 30 seconds
        await asyncio.sleep(30)

        stats = manager.get_stats()
        logger.info(f"[TEST] Connection stats: {stats}")

        return manager.is_healthy()

    finally:
        await manager.stop()


if __name__ == "__main__":
    # Initialize Windows fixes first
    from utils.windows_event_loop_fix import initialize_windows_fixes
    initialize_windows_fixes()

    async def main():
        success = await test_htx_connection()
        if success:
            print("✅ HTX WebSocket test passed")
        else:
            print("❌ HTX WebSocket test failed")

    asyncio.run(main())
