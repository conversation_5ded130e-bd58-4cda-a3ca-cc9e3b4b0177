2025-06-07 15:26:13,504 - monitoring.alert_system - WARNING - Email functionality not available - install email packages if needed
2025-06-07 15:26:13,531 - __main__ - INFO - ================================================================================
2025-06-07 15:26:13,532 - __main__ - INFO - ================================================================================
2025-06-07 15:26:13,535 - __main__ - INFO - ================================================================================
2025-06-07 15:26:13,537 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:13,570 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:13,572 - storage.live_store - INFO - State loaded from cache file
2025-06-07 15:26:13,572 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-07 15:26:13,574 - execution.trade_executor - INFO - Risk-Aware Trade Executor initialized in live_trading mode
2025-06-07 15:26:13,574 - execution.order_logic - INFO - Adaptive Order Logic initialized
2025-06-07 15:26:13,575 - execution.execution_memory - INFO - Execution Memory initialized with 56 historical executions
2025-06-07 15:26:13,575 - execution.post_trade_reflection - INFO - Post-Trade Reflection system initialized
2025-06-07 15:26:14,077 - monitoring.account_tracker - INFO -    Account Balance: $15.0
2025-06-07 15:26:14,077 - monitoring.account_tracker - INFO -    Max Position: $1.0
2025-06-07 15:26:14,077 - monitoring.account_tracker - INFO -    Leverage: 75.0x
2025-06-07 15:26:14,078 - execution.execution_controller - INFO - Execution Intelligence Controller initialized
2025-06-07 15:26:14,081 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-07 15:26:14,084 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:14,117 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:14,124 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:14,134 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:14,142 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:17,130 - __main__ - INFO - --------------------------------------------------
2025-06-07 15:26:17,176 - ccxt.base.exchange - WARNING - huobi requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-06-07 15:26:17,176 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x0000023D787670A0>, 938935.25)]', '[(<aiohttp.client_proto.ResponseHandler object at 0x0000023D78A50F40>, 938935.437), (<aiohttp.client_proto.ResponseHandler object at 0x0000023D78A50B20>, 938935.656)]']
connector: <aiohttp.connector.TCPConnector object at 0x0000023D7877EEB0>
2025-06-07 15:26:17,177 - asyncio - ERROR - Fatal error on SSL transport
protocol: <asyncio.sslproto.SSLProtocol object at 0x0000023D7877EA60>
transport: <_ProactorSocketTransport fd=-1 read=<_OverlappedFuture cancelled>>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\sslproto.py", line 690, in _process_write_backlog
    self._transport.write(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 359, in write
    self._loop_writing(data=bytes(data))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 395, in _loop_writing
    self._write_fut = self._loop._proactor.send(self._sock, data)
AttributeError: 'NoneType' object has no attribute 'send'
2025-06-07 15:26:17,181 - asyncio - ERROR - Fatal error on SSL transport
protocol: <asyncio.sslproto.SSLProtocol object at 0x0000023D7888FFA0>
transport: <_ProactorSocketTransport fd=-1 read=<_OverlappedFuture cancelled>>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\sslproto.py", line 690, in _process_write_backlog
    self._transport.write(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 359, in write
    self._loop_writing(data=bytes(data))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 395, in _loop_writing
    self._write_fut = self._loop._proactor.send(self._sock, data)
AttributeError: 'NoneType' object has no attribute 'send'
2025-06-07 15:26:17,183 - asyncio - ERROR - Fatal error on SSL transport
protocol: <asyncio.sslproto.SSLProtocol object at 0x0000023D7888FE20>
transport: <_ProactorSocketTransport fd=-1 read=<_OverlappedFuture cancelled>>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\sslproto.py", line 690, in _process_write_backlog
    self._transport.write(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 359, in write
    self._loop_writing(data=bytes(data))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\proactor_events.py", line 395, in _loop_writing
    self._write_fut = self._loop._proactor.send(self._sock, data)
AttributeError: 'NoneType' object has no attribute 'send'
