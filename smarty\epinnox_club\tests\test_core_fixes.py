#!/usr/bin/env python3
"""
Core Security Fixes Test
Tests the essential security fixes without requiring a running server
"""

import logging
import os
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_config():
    """Test environment configuration."""
    logger.info("[TEST 1] Testing Environment Configuration...")
    
    try:
        # Check .env file
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.read()
            
            # Check for required variables
            required_vars = [
                'ENCRYPTION_KEY',
                'SECRET_KEY',
                'HTX_API_KEY',
                'HTX_API_SECRET'
            ]
            
            missing_vars = []
            for var in required_vars:
                if var not in env_content:
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning(f"⚠️ Missing environment variables: {missing_vars}")
                return 'PARTIAL'
            else:
                logger.info("✅ All required environment variables present")
                return 'PASS'
        else:
            logger.error("❌ .env file not found")
            return 'FAIL'
            
    except Exception as e:
        logger.error(f"❌ Environment config test failed: {e}")
        return 'FAIL'

def test_encryption_decryption():
    """Test encryption/decryption functionality."""
    logger.info("[TEST 2] Testing Encryption/Decryption...")
    
    try:
        from exchanges.encryption_utils import EncryptionManager
        
        # Create encryption manager
        encryption_manager = EncryptionManager()
        logger.info("✅ Encryption manager created")
        
        # Test encryption/decryption
        test_data = "test_api_key_12345"
        encrypted = encryption_manager.encrypt(test_data)
        decrypted = encryption_manager.decrypt(encrypted)
        
        if decrypted == test_data:
            logger.info("✅ Encryption/decryption working correctly")
            return 'PASS'
        else:
            logger.error("❌ Encryption/decryption mismatch")
            return 'FAIL'
            
    except Exception as e:
        logger.error(f"❌ Encryption/decryption test failed: {e}")
        return 'FAIL'

def test_security_manager():
    """Test security manager functionality."""
    logger.info("[TEST 3] Testing Security Manager...")
    
    try:
        from security.security_manager import SecurityManager
        
        # Create security manager
        config = {
            'max_login_attempts': 5,
            'lockout_duration': 900,
            'rate_limit_requests': 100,
            'rate_limit_window': 3600,
            'session_timeout': 3600
        }
        
        security_manager = SecurityManager(config)
        logger.info("✅ Security manager created")
        
        # Test localhost detection
        localhost_ips = ['127.0.0.1', '::1', 'localhost']
        for ip in localhost_ips:
            if security_manager._is_localhost(ip):
                logger.info(f"✅ Localhost detection working for {ip}")
            else:
                logger.error(f"❌ Localhost detection failed for {ip}")
                return 'FAIL'
        
        # Test CSRF token generation
        test_session = "test_session_token"
        csrf_token = security_manager.generate_csrf_token(test_session)
        if csrf_token and len(csrf_token) == 64:
            logger.info("✅ CSRF token generation working")
            return 'PASS'
        else:
            logger.error("❌ CSRF token generation failed")
            return 'FAIL'
            
    except Exception as e:
        logger.error(f"❌ Security manager test failed: {e}")
        return 'FAIL'

def test_htx_client():
    """Test HTX client creation."""
    logger.info("[TEST 4] Testing HTX Client Creation...")
    
    try:
        from trading.htx_futures_client import HTXFuturesClient
        
        # Create HTX client
        htx_client = HTXFuturesClient()
        logger.info("✅ HTX client created successfully")
        
        # Check if it has the required methods
        required_methods = ['connect', 'get_balance', 'place_order']
        for method in required_methods:
            if hasattr(htx_client, method):
                logger.info(f"✅ HTX client has {method} method")
            else:
                logger.warning(f"⚠️ HTX client missing {method} method")
        
        return 'PASS'
        
    except Exception as e:
        logger.error(f"❌ HTX client test failed: {e}")
        return 'FAIL'

def test_csrf_fix_file():
    """Test CSRF fix JavaScript file."""
    logger.info("[TEST 5] Testing CSRF Fix File...")
    
    try:
        csrf_file = Path('static/js/csrf_fix.js')
        if csrf_file.exists():
            with open(csrf_file, 'r') as f:
                content = f.read()
            
            # Check for key components
            required_components = [
                'CSRFManager',
                'getCSRFToken',
                'X-CSRF-Token',
                'patchFetch',
                'patchXMLHttpRequest'
            ]
            
            missing_components = []
            for component in required_components:
                if component not in content:
                    missing_components.append(component)
            
            if missing_components:
                logger.warning(f"⚠️ Missing CSRF fix components: {missing_components}")
                return 'PARTIAL'
            else:
                logger.info("✅ CSRF fix file has all required components")
                return 'PASS'
        else:
            logger.error("❌ CSRF fix file not found")
            return 'FAIL'
            
    except Exception as e:
        logger.error(f"❌ CSRF fix file test failed: {e}")
        return 'FAIL'

def test_symbol_selection_fix():
    """Test symbol selection fix."""
    logger.info("[TEST 6] Testing Symbol Selection Fix...")
    
    try:
        # Check symbol config file
        symbol_config_file = Path('config/symbols.json')
        if symbol_config_file.exists():
            import json
            with open(symbol_config_file, 'r') as f:
                config = json.load(f)
            
            if 'supported_symbols' in config and len(config['supported_symbols']) > 0:
                logger.info(f"✅ Symbol config has {len(config['supported_symbols'])} symbols")
            else:
                logger.warning("⚠️ Symbol config missing supported_symbols")
                return 'PARTIAL'
        else:
            logger.warning("⚠️ Symbol config file not found")
            return 'PARTIAL'
        
        # Check symbol selection fix file
        symbol_fix_file = Path('static/js/symbol_selection_fix.js')
        if symbol_fix_file.exists():
            logger.info("✅ Symbol selection fix file exists")
            return 'PASS'
        else:
            logger.warning("⚠️ Symbol selection fix file not found")
            return 'PARTIAL'
            
    except Exception as e:
        logger.error(f"❌ Symbol selection fix test failed: {e}")
        return 'FAIL'

def main():
    """Main test function."""
    logger.info("=" * 70)
    logger.info("MONEY CIRCLE CORE SECURITY FIXES TEST")
    logger.info("=" * 70)
    
    # Run all tests
    test_results = {}
    test_results['environment_config'] = test_environment_config()
    test_results['encryption_decryption'] = test_encryption_decryption()
    test_results['security_manager'] = test_security_manager()
    test_results['htx_client'] = test_htx_client()
    test_results['csrf_fix_file'] = test_csrf_fix_file()
    test_results['symbol_selection_fix'] = test_symbol_selection_fix()
    
    # Generate report
    logger.info("\n" + "=" * 70)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 70)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result == 'PASS')
    partial_tests = sum(1 for result in test_results.values() if result == 'PARTIAL')
    failed_tests = sum(1 for result in test_results.values() if result == 'FAIL')
    
    for test_name, result in test_results.items():
        status_symbol = {
            'PASS': '✅ [PASS]',
            'PARTIAL': '⚠️ [PARTIAL]',
            'FAIL': '❌ [FAIL]'
        }.get(result, '[UNKNOWN]')
        
        logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
    
    logger.info("\n" + "-" * 70)
    logger.info(f"TOTAL TESTS: {total_tests}")
    logger.info(f"PASSED: {passed_tests}")
    logger.info(f"PARTIAL: {partial_tests}")
    logger.info(f"FAILED: {failed_tests}")
    
    success_rate = (passed_tests + partial_tests * 0.5) / total_tests * 100
    logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 90:
        logger.info("\n🎉 [SUCCESS] All critical security fixes are working!")
        logger.info("\nNext steps:")
        logger.info("1. Start the Money Circle server")
        logger.info("2. Test live trading interface with symbol selection")
        logger.info("3. Verify HTX API integration with real credentials")
        logger.info("4. Test exchange balance refresh functionality")
    elif success_rate >= 70:
        logger.info("\n⚠️ [WARNING] Most security fixes working, some issues remain")
    else:
        logger.info("\n❌ [ERROR] Critical security issues need attention")
    
    logger.info("=" * 70)
    
    return 0 if success_rate >= 70 else 1

if __name__ == "__main__":
    sys.exit(main())
