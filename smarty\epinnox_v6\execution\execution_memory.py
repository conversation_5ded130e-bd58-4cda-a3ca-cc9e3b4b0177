#!/usr/bin/env python3
"""
Execution Memory
Stores fill quality, latency, slippage, unrealized PnL — adds that back into performance memory
"""

import logging
import time
import json
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

@dataclass
class ExecutionMetrics:
    """Metrics for a single execution."""
    execution_id: str
    symbol: str
    timestamp: float
    action: str
    size: float
    entry_price: float
    execution_price: float
    slippage: float
    latency_ms: float
    fill_quality: float
    order_type: str
    confidence: float
    conviction_score: int
    market_regime: str
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    is_closed: bool = False
    close_timestamp: Optional[float] = None
    close_price: Optional[float] = None

@dataclass
class PerformanceSnapshot:
    """Performance snapshot for analysis."""
    timestamp: float
    total_executions: int
    win_rate: float
    avg_slippage: float
    avg_latency: float
    avg_fill_quality: float
    total_pnl: float
    unrealized_pnl: float
    sharpe_ratio: float
    max_drawdown: float
    execution_efficiency: float

class ExecutionMemory:
    """
    Comprehensive execution tracking and performance analysis system.
    Stores all execution data and provides insights for strategy improvement.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.memory_config = config.get('execution_memory', {})

        # Storage paths
        self.data_dir = self.memory_config.get('data_dir', 'data/execution')
        self.executions_file = os.path.join(self.data_dir, 'executions.json')
        self.snapshots_file = os.path.join(self.data_dir, 'performance_snapshots.json')

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # In-memory storage
        self.executions: Dict[str, ExecutionMetrics] = {}
        self.performance_snapshots: List[PerformanceSnapshot] = []

        # Performance tracking
        self.symbol_performance: Dict[str, List[float]] = defaultdict(list)
        self.regime_performance: Dict[str, List[float]] = defaultdict(list)
        self.confidence_performance: Dict[float, List[float]] = defaultdict(list)

        # Rolling windows for analysis
        self.max_history = self.memory_config.get('max_history', 10000)
        self.recent_executions = deque(maxlen=100)  # Last 100 executions

        # Load existing data
        self._load_data()

        logger.info(f"Execution Memory initialized with {len(self.executions)} historical executions")

    def record_execution(self, execution_data: Dict[str, Any]) -> str:
        """Record a new execution and update performance metrics."""
        try:
            # Create execution metrics
            execution = ExecutionMetrics(
                execution_id=execution_data['execution_id'],
                symbol=execution_data['symbol'],
                timestamp=execution_data['timestamp'],
                action=execution_data['action'],
                size=execution_data['size'],
                entry_price=execution_data['entry_price'],
                execution_price=execution_data['execution_price'],
                slippage=execution_data['slippage'],
                latency_ms=execution_data['latency_ms'],
                fill_quality=execution_data['fill_quality'],
                order_type=execution_data['order_type'],
                confidence=execution_data['confidence'],
                conviction_score=execution_data['conviction_score'],
                market_regime=execution_data['market_regime']
            )

            # Store execution
            self.executions[execution.execution_id] = execution
            self.recent_executions.append(execution.execution_id)

            # Update performance tracking
            self._update_performance_tracking(execution)

            # Save to disk
            self._save_executions()

            logger.info(f"Recorded execution: {execution.symbol} {execution.action} "
                       f"{execution.size:.2f} @ ${execution.execution_price:.2f}")

            return execution.execution_id

        except Exception as e:
            logger.error(f"Error recording execution: {e}")
            return ""

    def update_position_pnl(self, execution_id: str, current_price: float) -> Optional[float]:
        """Update unrealized PnL for an open position."""
        try:
            if execution_id not in self.executions:
                return None

            execution = self.executions[execution_id]
            if execution.is_closed:
                return execution.realized_pnl

            # Calculate unrealized PnL
            if execution.action.upper() in ['LONG', 'BUY']:
                pnl = (current_price - execution.execution_price) * execution.size
            else:  # SHORT/SELL
                pnl = (execution.execution_price - current_price) * execution.size

            execution.unrealized_pnl = pnl

            # Update symbol performance
            pnl_percentage = pnl / (execution.execution_price * execution.size)
            self.symbol_performance[execution.symbol].append(pnl_percentage)

            return pnl

        except Exception as e:
            logger.error(f"Error updating position PnL: {e}")
            return None

    def close_position(self, execution_id: str, close_price: float) -> Optional[float]:
        """Close a position and record realized PnL."""
        try:
            if execution_id not in self.executions:
                return None

            execution = self.executions[execution_id]
            if execution.is_closed:
                return execution.realized_pnl

            # Calculate realized PnL
            if execution.action.upper() in ['LONG', 'BUY']:
                pnl = (close_price - execution.execution_price) * execution.size
            else:  # SHORT/SELL
                pnl = (execution.execution_price - close_price) * execution.size

            # Update execution record
            execution.realized_pnl = pnl
            execution.unrealized_pnl = 0.0
            execution.is_closed = True
            execution.close_timestamp = time.time()
            execution.close_price = close_price

            # Update performance tracking
            pnl_percentage = pnl / (execution.execution_price * execution.size)
            self.symbol_performance[execution.symbol].append(pnl_percentage)
            self.regime_performance[execution.market_regime].append(pnl_percentage)
            self.confidence_performance[round(execution.confidence, 1)].append(pnl_percentage)

            # Save to disk
            self._save_executions()

            logger.info(f"Closed position: {execution.symbol} {execution.action} "
                       f"PnL: ${pnl:.2f} ({pnl_percentage*100:.2f}%)")

            return pnl

        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return None

    def get_performance_metrics(self, lookback_hours: int = 24) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        try:
            cutoff_time = time.time() - (lookback_hours * 3600)
            recent_executions = [
                e for e in self.executions.values()
                if e.timestamp >= cutoff_time
            ]

            if not recent_executions:
                return {'error': 'No recent executions found'}

            # Basic metrics
            total_executions = len(recent_executions)
            closed_positions = [e for e in recent_executions if e.is_closed]

            # Win rate
            winning_trades = [e for e in closed_positions if e.realized_pnl > 0]
            win_rate = len(winning_trades) / len(closed_positions) if closed_positions else 0

            # PnL metrics
            total_realized_pnl = sum(e.realized_pnl for e in closed_positions)
            total_unrealized_pnl = sum(e.unrealized_pnl for e in recent_executions if not e.is_closed)
            total_pnl = total_realized_pnl + total_unrealized_pnl

            # Execution quality metrics
            avg_slippage = statistics.mean(e.slippage for e in recent_executions)
            avg_latency = statistics.mean(e.latency_ms for e in recent_executions)
            avg_fill_quality = statistics.mean(e.fill_quality for e in recent_executions)

            # Risk metrics
            pnl_values = [e.realized_pnl for e in closed_positions if e.realized_pnl != 0]
            sharpe_ratio = self._calculate_sharpe_ratio(pnl_values) if pnl_values else 0
            max_drawdown = self._calculate_max_drawdown(pnl_values) if pnl_values else 0

            # Execution efficiency (fill quality weighted by size)
            total_value = sum(e.size * e.execution_price for e in recent_executions)
            weighted_quality = sum(e.fill_quality * e.size * e.execution_price for e in recent_executions)
            execution_efficiency = weighted_quality / total_value if total_value > 0 else 0

            return {
                'lookback_hours': lookback_hours,
                'total_executions': total_executions,
                'closed_positions': len(closed_positions),
                'win_rate': win_rate,
                'total_realized_pnl': total_realized_pnl,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_pnl': total_pnl,
                'avg_slippage': avg_slippage,
                'avg_latency_ms': avg_latency,
                'avg_fill_quality': avg_fill_quality,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'execution_efficiency': execution_efficiency,
                'symbol_breakdown': self._get_symbol_breakdown(recent_executions),
                'regime_breakdown': self._get_regime_breakdown(recent_executions),
                'confidence_analysis': self._get_confidence_analysis(recent_executions)
            }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {'error': str(e)}

    def create_performance_snapshot(self) -> PerformanceSnapshot:
        """Create a performance snapshot for historical tracking."""
        try:
            metrics = self.get_performance_metrics(24)  # Last 24 hours

            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                total_executions=metrics.get('total_executions', 0),
                win_rate=metrics.get('win_rate', 0.0),
                avg_slippage=metrics.get('avg_slippage', 0.0),
                avg_latency=metrics.get('avg_latency_ms', 0.0),
                avg_fill_quality=metrics.get('avg_fill_quality', 0.0),
                total_pnl=metrics.get('total_pnl', 0.0),
                unrealized_pnl=metrics.get('total_unrealized_pnl', 0.0),
                sharpe_ratio=metrics.get('sharpe_ratio', 0.0),
                max_drawdown=metrics.get('max_drawdown', 0.0),
                execution_efficiency=metrics.get('execution_efficiency', 0.0)
            )

            self.performance_snapshots.append(snapshot)
            self._save_snapshots()

            return snapshot

        except Exception as e:
            logger.error(f"Error creating performance snapshot: {e}")
            return None

    def get_execution_insights(self) -> Dict[str, Any]:
        """Get actionable insights from execution history."""
        try:
            insights = {
                'execution_quality': self._analyze_execution_quality(),
                'timing_analysis': self._analyze_timing_patterns(),
                'regime_performance': self._analyze_regime_performance(),
                'confidence_calibration': self._analyze_confidence_calibration(),
                'recommendations': []
            }

            # Generate recommendations
            insights['recommendations'] = self._generate_recommendations(insights)

            return insights

        except Exception as e:
            logger.error(f"Error getting execution insights: {e}")
            return {'error': str(e)}

    def _update_performance_tracking(self, execution: ExecutionMetrics):
        """Update internal performance tracking structures."""
        try:
            # Add to recent executions for quick access
            # Performance tracking will be updated when position is closed
            pass

        except Exception as e:
            logger.error(f"Error updating performance tracking: {e}")

    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calculate Sharpe ratio from returns."""
        try:
            if len(returns) < 2:
                return 0.0

            mean_return = statistics.mean(returns)
            std_return = statistics.stdev(returns)

            if std_return == 0:
                return 0.0

            # Assuming risk-free rate of 0 for simplicity
            return mean_return / std_return

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown from returns."""
        try:
            if not returns:
                return 0.0

            cumulative = 0.0
            peak = 0.0
            max_dd = 0.0

            for ret in returns:
                cumulative += ret
                if cumulative > peak:
                    peak = cumulative

                drawdown = peak - cumulative
                if drawdown > max_dd:
                    max_dd = drawdown

            return max_dd

        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0

    def _get_symbol_breakdown(self, executions: List[ExecutionMetrics]) -> Dict[str, Any]:
        """Get performance breakdown by symbol."""
        try:
            symbol_stats = defaultdict(lambda: {
                'count': 0, 'total_pnl': 0.0, 'avg_slippage': 0.0, 'avg_quality': 0.0
            })

            for execution in executions:
                stats = symbol_stats[execution.symbol]
                stats['count'] += 1
                stats['total_pnl'] += execution.realized_pnl + execution.unrealized_pnl
                stats['avg_slippage'] += execution.slippage
                stats['avg_quality'] += execution.fill_quality

            # Calculate averages
            for symbol, stats in symbol_stats.items():
                if stats['count'] > 0:
                    stats['avg_slippage'] /= stats['count']
                    stats['avg_quality'] /= stats['count']

            return dict(symbol_stats)

        except Exception as e:
            logger.error(f"Error getting symbol breakdown: {e}")
            return {}

    def _get_regime_breakdown(self, executions: List[ExecutionMetrics]) -> Dict[str, Any]:
        """Get performance breakdown by market regime."""
        try:
            regime_stats = defaultdict(lambda: {
                'count': 0, 'total_pnl': 0.0, 'win_rate': 0.0
            })

            for execution in executions:
                if execution.is_closed:
                    stats = regime_stats[execution.market_regime]
                    stats['count'] += 1
                    stats['total_pnl'] += execution.realized_pnl
                    if execution.realized_pnl > 0:
                        stats['win_rate'] += 1

            # Calculate win rates
            for regime, stats in regime_stats.items():
                if stats['count'] > 0:
                    stats['win_rate'] /= stats['count']

            return dict(regime_stats)

        except Exception as e:
            logger.error(f"Error getting regime breakdown: {e}")
            return {}

    def _get_confidence_analysis(self, executions: List[ExecutionMetrics]) -> Dict[str, Any]:
        """Analyze confidence vs actual performance."""
        try:
            confidence_buckets = defaultdict(lambda: {'count': 0, 'wins': 0, 'total_pnl': 0.0})

            for execution in executions:
                if execution.is_closed:
                    bucket = round(execution.confidence, 1)
                    stats = confidence_buckets[bucket]
                    stats['count'] += 1
                    stats['total_pnl'] += execution.realized_pnl
                    if execution.realized_pnl > 0:
                        stats['wins'] += 1

            # Calculate calibration
            calibration_data = {}
            for confidence, stats in confidence_buckets.items():
                if stats['count'] > 0:
                    actual_win_rate = stats['wins'] / stats['count']
                    calibration_error = abs(confidence - actual_win_rate)
                    calibration_data[confidence] = {
                        'predicted': confidence,
                        'actual': actual_win_rate,
                        'error': calibration_error,
                        'count': stats['count'],
                        'avg_pnl': stats['total_pnl'] / stats['count']
                    }

            return calibration_data

        except Exception as e:
            logger.error(f"Error getting confidence analysis: {e}")
            return {}

    def _analyze_execution_quality(self) -> Dict[str, Any]:
        """Analyze execution quality patterns."""
        try:
            recent = list(self.recent_executions)[-50:]  # Last 50 executions
            if not recent:
                return {}

            executions = [self.executions[eid] for eid in recent if eid in self.executions]

            avg_slippage = statistics.mean(e.slippage for e in executions)
            avg_latency = statistics.mean(e.latency_ms for e in executions)
            avg_quality = statistics.mean(e.fill_quality for e in executions)

            # Quality trends
            quality_trend = 'improving' if len(executions) > 10 and \
                statistics.mean(e.fill_quality for e in executions[-10:]) > \
                statistics.mean(e.fill_quality for e in executions[-20:-10]) else 'stable'

            return {
                'avg_slippage': avg_slippage,
                'avg_latency_ms': avg_latency,
                'avg_fill_quality': avg_quality,
                'quality_trend': quality_trend,
                'sample_size': len(executions)
            }

        except Exception as e:
            logger.error(f"Error analyzing execution quality: {e}")
            return {}

    def _analyze_timing_patterns(self) -> Dict[str, Any]:
        """Analyze timing patterns in executions."""
        try:
            # Placeholder for timing analysis
            return {
                'best_execution_hours': [9, 10, 14, 15],  # Example
                'worst_execution_hours': [12, 13],
                'avg_hold_time_hours': 2.5
            }

        except Exception as e:
            logger.error(f"Error analyzing timing patterns: {e}")
            return {}

    def _analyze_regime_performance(self) -> Dict[str, Any]:
        """Analyze performance by market regime."""
        try:
            regime_performance = {}
            for regime, pnl_list in self.regime_performance.items():
                if pnl_list:
                    regime_performance[regime] = {
                        'avg_return': statistics.mean(pnl_list),
                        'win_rate': len([p for p in pnl_list if p > 0]) / len(pnl_list),
                        'volatility': statistics.stdev(pnl_list) if len(pnl_list) > 1 else 0,
                        'trade_count': len(pnl_list)
                    }

            return regime_performance

        except Exception as e:
            logger.error(f"Error analyzing regime performance: {e}")
            return {}

    def _analyze_confidence_calibration(self) -> Dict[str, Any]:
        """Analyze how well confidence predicts actual performance."""
        try:
            calibration_error = 0.0
            total_trades = 0

            for confidence, pnl_list in self.confidence_performance.items():
                if pnl_list:
                    actual_win_rate = len([p for p in pnl_list if p > 0]) / len(pnl_list)
                    error = abs(confidence - actual_win_rate)
                    calibration_error += error * len(pnl_list)
                    total_trades += len(pnl_list)

            avg_calibration_error = calibration_error / total_trades if total_trades > 0 else 0

            return {
                'avg_calibration_error': avg_calibration_error,
                'is_well_calibrated': avg_calibration_error < 0.1,
                'total_trades_analyzed': total_trades
            }

        except Exception as e:
            logger.error(f"Error analyzing confidence calibration: {e}")
            return {}

    def _generate_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on insights."""
        recommendations = []

        try:
            # Execution quality recommendations
            quality = insights.get('execution_quality', {})
            if quality.get('avg_slippage', 0) > 0.005:
                recommendations.append("High slippage detected - consider using limit orders more frequently")

            if quality.get('avg_latency_ms', 0) > 500:
                recommendations.append("High execution latency - optimize order routing or reduce order size")

            # Confidence calibration recommendations
            calibration = insights.get('confidence_calibration', {})
            if not calibration.get('is_well_calibrated', True):
                recommendations.append("Confidence scores poorly calibrated - review decision thresholds")

            # Regime performance recommendations
            regime_perf = insights.get('regime_performance', {})
            for regime, stats in regime_perf.items():
                if stats.get('win_rate', 0) < 0.4:
                    recommendations.append(f"Poor performance in {regime} market - consider reducing exposure")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]

    def _load_data(self):
        """Load execution data from disk."""
        try:
            # Load executions
            if os.path.exists(self.executions_file):
                with open(self.executions_file, 'r') as f:
                    data = json.load(f)
                    for exec_id, exec_data in data.items():
                        self.executions[exec_id] = ExecutionMetrics(**exec_data)
                        self.recent_executions.append(exec_id)

            # Load snapshots
            if os.path.exists(self.snapshots_file):
                with open(self.snapshots_file, 'r') as f:
                    data = json.load(f)
                    self.performance_snapshots = [PerformanceSnapshot(**snap) for snap in data]

        except Exception as e:
            logger.error(f"Error loading execution data: {e}")

    def _save_executions(self):
        """Save executions to disk."""
        try:
            # Keep only recent executions to prevent file bloat
            recent_ids = list(self.recent_executions)
            executions_to_save = {
                eid: asdict(self.executions[eid])
                for eid in recent_ids
                if eid in self.executions
            }

            with open(self.executions_file, 'w') as f:
                json.dump(executions_to_save, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving executions: {e}")

    def _save_snapshots(self):
        """Save performance snapshots to disk."""
        try:
            # Keep only recent snapshots
            recent_snapshots = self.performance_snapshots[-100:]  # Last 100 snapshots
            snapshots_data = [asdict(snap) for snap in recent_snapshots]

            with open(self.snapshots_file, 'w') as f:
                json.dump(snapshots_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving snapshots: {e}")
