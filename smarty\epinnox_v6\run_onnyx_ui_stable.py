#!/usr/bin/env python3
"""
Stable Onnyx UI Runner - Phase 9.1
Runs the Onnyx-themed UI with proper cleanup and error handling
"""

import asyncio
import logging
import yaml
import signal
import sys
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController

class OnnxUIRunner:
    """Stable runner for Onnyx UI with proper cleanup."""
    
    def __init__(self):
        self.ui = None
        self.data_store = None
        self.execution_controller = None
        self.runner = None
        self.shutdown_event = asyncio.Event()
        
    async def start(self):
        """Start the Onnyx UI with proper initialization."""
        try:
            logger.info("🚀 Starting Onnyx UI - Phase 9.1")
            
            # Load configuration
            config_path = Path("config/strategy.yaml")
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Initialize components
            logger.info("📊 Initializing data store...")
            self.data_store = LiveDataStore(config)
            
            logger.info("🎯 Initializing execution controller...")
            self.execution_controller = ExecutionController(config)
            
            logger.info("🎨 Initializing Onnyx UI...")
            self.ui = AIStrategyTunerDashboard(config, self.data_store, self.execution_controller)
            
            # Start the dashboard
            logger.info("🌐 Starting dashboard server...")
            self.runner = await self.ui.start_server(host='localhost', port=8086)
            
            logger.info("✅ Onnyx UI started successfully!")
            logger.info("🌐 Dashboard URL: http://localhost:8086")
            logger.info("\n🎯 Phase 9.1 Features Active:")
            logger.info("   ✅ Onnyx-themed interface with cyber aesthetics")
            logger.info("   ✅ Live Account Metrics panel")
            logger.info("   ✅ Real-time CCXT HTX integration")
            logger.info("   ✅ Account-aware risk management")
            logger.info("   ✅ Glassmorphism design with neon effects")
            logger.info("   ✅ Auto-updating metrics every 5 seconds")
            
            logger.info("\n💰 Trading Configuration:")
            logger.info("   Account: $5.00 | Leverage: 20x | Symbol: DOGE/USDT:USDT")
            logger.info("   Risk Level: SAFE | Trade Capability: ENABLED")
            
            logger.info("\n⌨️ Press Ctrl+C to stop the server")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"❌ Error starting Onnyx UI: {e}")
            raise
    
    async def stop(self):
        """Stop the UI with proper cleanup."""
        try:
            logger.info("🛑 Shutting down Onnyx UI...")
            
            # Stop account monitoring
            if self.execution_controller and hasattr(self.execution_controller, 'account_tracker'):
                await self.execution_controller.account_tracker.stop_monitoring()
                logger.info("✅ Account monitoring stopped")
            
            # Close CCXT connections
            if self.execution_controller and hasattr(self.execution_controller, 'htx_client'):
                await self.execution_controller.htx_client.disconnect()
                logger.info("✅ HTX client disconnected")
            
            # Stop the web server
            if self.runner:
                await self.runner.cleanup()
                logger.info("✅ Web server stopped")
            
            logger.info("✅ Onnyx UI shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, initiating shutdown...")
        self.shutdown_event.set()

async def main():
    """Main function with proper signal handling."""
    runner = OnnxUIRunner()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, runner.signal_handler)
    signal.signal(signal.SIGTERM, runner.signal_handler)
    
    try:
        await runner.start()
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        await runner.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)
